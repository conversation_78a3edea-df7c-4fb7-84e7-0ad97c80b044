"use client";

import React, { JSX } from "react";
import  BecomePartnerByAnima  from "@/pages/homepage/sections/BecomePartnerByAnima/BecomePartnerByAnima";
import  DivByAnima  from "@/pages/homepage/sections/DivByAnima/DivByAnima";
import  DivWrapperByAnima  from "@/pages/homepage/sections/DivWrapperByAnima/DivWrapperByAnima";

import  HeroSectionByAnima  from "@/pages/homepage/sections/HeroSectionByAnima/HeroSectionByAnima";
import  MainContainerByAnima  from "@/pages/homepage/sections/MainContainerByAnima/MainContainerByAnima";
import  SectionByAnima  from "@/pages/homepage/sections/SectionByAnima/SectionByAnima";
import  SectionWrapperByAnima  from "@/pages/homepage/sections/SectionWrapperByAnima/SectionWrapperByAnima";

export const HomeSignedOut = (): JSX.Element => {
  return (
    <div className="flex flex-col w-full min-h-screen bg-[#FCFCFC]">
      <HeroSectionByAnima />
      <span className="hidden md:block">
        <SectionByAnima />
        <SectionWrapperByAnima />
      </span>

      <DivWrapperByAnima />
      <MainContainerByAnima />
      <DivByAnima />
      <BecomePartnerByAnima />
    </div>
  );
};

export const HomePage = (): React.ReactElement => {
  return <HomeSignedOut />;
};

export default HomePage;
