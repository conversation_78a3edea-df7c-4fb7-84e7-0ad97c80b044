.dshjhsdjadhsdjasjh {
    width: 100%;
}
.jjdjjjjjjjj{
    margin-bottom: 80px;
}
.hsdjshfhdh{
    padding: 94.5px 100px 94.5px 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #E9EDE9;
    gap: 64px;
margin-top: 100px;

}

.dshjhsdjadhsdjasjh{
    max-width:  602px;
    width: 100%;
    /* height: 498; */
    border-radius: 20px;
    border-width: 1px;
    
    
}
.dshjhsdjadhsdjasjh2{

}
.dshjhsdjadhsdjasjh2_div1{
    width: 144px;
    height: 36px;
    border-radius: 60px;
    /* padding-top: 8px;
    padding-right: 16px;
    padding-bottom: 8px;
    padding-left: 16px; */
  display: flex;
    justify-content: center;
    align-items: center;
    background: #D2DAD2;

  
    color: #00000014;

    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #23232399;
margin-bottom: 24px;

}

.dshjhsdjadhsdjasjh2_div2{
    margin-bottom: 40px;
}


.dshjhsdjadhsdjasjh2_div2_p1{
    font-family: "ScandiaMedium";
    font-weight: 500;
    font-size: 40px;
    line-height: 48px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #181916;
margin: 0px !important;
    
}
.dshjhsdjadhsdjasjh2_div2_p2{
    font-family: Scandia;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #303030;
    margin: 0px !important;
}


.dshjhsdjadhsdjasjh3{

    margin-bottom: 40px;
}
.dshjhsdjadhsdjasjh3_div1{

}
.dshjhsdjadhsdjasjh3_div1_P1{
margin: 0px !important;
font-family: "ScandiaMedium";
}

.dshjhsdjadhsdjasjh3_div1_P2{
    margin: 0px !important;
    font-family: "Scandia";
}

/* ......phala portion ki styling .... */
/* ........ */
.ndxjswbdjhhbsjhdbxjhsMain{
padding: 41px 100px 32px 100px;
display: flex;
flex-direction: column;
}
.ndxjswbdjhhbsjhdbxjhsMain_1{
    display: flex;
    flex-direction: column;
    gap: 24px;

    width: 100%;
}
.ndxjswbdjhhbsjhdbxjhsMain_1_1{
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.ndxjswbdjhhbsjhdbxjhsMain_1_1_p{
    font-family: "ScandiaMedium";
    font-weight: 500;
    font-size: 28px;
    line-height: 36px;
    letter-spacing: 0%;
    text-align: left;
    vertical-align: middle;
    color: #181916;
    margin: 0px !important;
    
}
.ndxjswbdjhhbsjhdbxjhsMain_1_1_p2{
    font-family: Scandia;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    vertical-align: middle;
    color: #303030;
    margin: 0px !important;
}

.ndxjswbdjhhbsjhdbxjhsMain_1_2{
    display: flex;
    gap: 16px;
    justify-content: space-between;
    align-items: center;

}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__1{
    display: flex;
    flex-direction: row;
    gap: 8px;   
    flex-wrap: wrap;
}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1{
border-radius: 80px;
padding-top: 16px;
padding-right: 24;
padding-bottom: 16px;
padding-left: 24;
font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #FFFFFF;
background: #2F3B31;
border: 1px solid ;

}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2{

    border-radius: 60px;
    border-width: 1px;
    padding-top: 16px;
    padding-right: 24;
    padding-bottom: 16px;
    padding-left: 24;
    border: 1px solid #303030;
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #303030;

    
}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__2{
    display: flex;
    flex-direction: row;
    gap: 16px;   
    flex-wrap: wrap;
}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1{
    padding: 13px 24px 13px 24px;
    border: 1px solid #23232366;
    border-radius: 99px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

}

.bhabdhsser{
    width: 18px;
height: 18px;

}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1_input{
    font-family: DM Sans;
font-weight: 400;
font-size: 16px;
line-height: 26px;
letter-spacing: 0%;
vertical-align: middle;
color: #303030;
border: none;
}

.ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1_input::placeholder{
    font-family: DM Sans;
font-weight: 400;  
color: #23232366; 


}
.ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1_input:focus{
    outline: none;
}

.ndxjswbdjhhbsjhdbxjhsMain_1_2__2_2{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 6px;   
    padding: 16px 24px 16px 24px;
    background: #ECE7E3;
border-radius: 60px;
}
/* ......... */
.vjsghvjhsgv{
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
}
.yarrrrmaincard{
    max-width: 310px;
/* height: 450; */
border-radius: 20px;
border-width: 1px;
padding-top: 20px;
padding-right: 20px;
padding-bottom: 24px;
padding-left: 20px;
border: 1px solid #0000000A;
box-shadow: 4px 12px 24px 0px #0000000A;
background: #FFFFFF;

}
.isimg{
    width: 270px;
height: 186px;
border-radius: 12px;


}

.lasthbvsjabvhjx{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #303030;
    margin: 0px 0px 80px 0px  !important;

}
/* ......... */

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.4);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 999;
  }
  
  .modal {
    background: white;
    padding:12px  24px 24px 24px;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    position: relative;
  }
  
  .filter-sections {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    flex-direction: row;
    gap: 8px;
  }
  
  .filtersection1{
padding-right: 16px;
border-right: 0.5px solid #DEDEDEE5;
max-width: 236px;
width: 100%;
display: flex;
flex-direction: column;
gap: 20px;
  }
  .filtersection2{
    padding-right: 16px;
    max-width: 236px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .filtersection3{
    padding-right: 16px;
padding-left: 16px;
border-left: 0.5px solid #DEDEDEE5;
max-width: 236px;
width: 100%;
display: flex;
flex-direction: column;
gap: 20px;
  }
 .filtersection1_div{
    display: flex;
flex-direction: column;
gap: 12px;
 }
  .filtersection1_p{
    font-family: Scandia;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
color: #303030;

  }
  .filtersection1_div_p{
    font-family: Scandia;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
color: #4C4C4CFA;

  }
 
  .filter-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 32px;
    gap: 10px;
    flex-wrap: wrap;


  }
  .filter_buttons_p1{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 16px;
line-height: 24px;
letter-spacing: 0%;
color: #0E0E0EFA;
  }
 .filter_buttonsbtnDiv{
    display: flex;
    gap: 10px;
    align-items: center;
 }
 .restimg{
    width: 13px;
    height: 13px;
 }
  .reset-btn {
    background: white;
    border: 1px solid #303030;
    padding: 8px 12px;
    border-radius: 60px;
    cursor: pointer;
display: flex;
    align-items: center;
    gap: 6px;
    font-family: "ScandiaMedium";
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    letter-spacing: 0%;
    text-align: center;
    vertical-align: middle;
    color: #303030;

  }
  
  .apply-btn {
    display: flex;
    align-items: center;
    border: none;
    border-radius: 60px;
    background: #D31B1B;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 12px;
line-height: 16px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #FFFFFF;

  }
  
  .close-btn {
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 24px;
    background: none;
    border: none;
    cursor: pointer;
  }
  


/* ....... */
@media (min-width:1441px) {
    .dshjhsdjadhsdjasjh {
        width: 800px;
    }
}

@media (max-width:1440px) {
    .dshjhsdjadhsdjasjh {
        height: 550px;
        width: 50%;
    }
    .hsdjshfhdh{
        padding: 94.5px 60px 94.5px 60px;
        gap: 30px;
    }
    .ndxjswbdjhhbsjhdbxjhsMain {
        padding: 41px 60px 32px;
    }
}

@media (max-width:1300px) {
    .dshjhsdjadhsdjasjh {
        width: 50%;
    }

}

@media (max-width:1150px) {
    .dshjhsdjadhsdjasjh {
        height: unset;
        width: 600px;
    }

    .hsdjshfhdh {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }
    .ndxjswbdjhhbsjhdbxjhsMain_1_2{
        flex-direction: column-reverse;
        align-items: start;
    }
}

@media (max-width:769px) {
    .dshjhsdjadhsdjasjh {
        height: unset;
        width: 100%;
        max-width: 100%;
    }

    .hsdjshfhdh {
        padding: 40px;
        gap: 25px;
    }
  
    .ndxjswbdjhhbsjhdbxjhsMain{
        padding: 40px;
    }
    .modal {
        background: #fff;
        border-radius: 12px;
        width: 95%;
        max-width: 75%;
        padding: 13px 15px 15px;
    }

}

@media (max-width:660px) {
    .ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1{
        border-radius: 20px;
        padding: 14px;
        font-size: 14px;
        gap: 4px;
    }
    .ndxjswbdjhhbsjhdbxjhsMain_1_2__2_2 {
        border-radius: 20px;
        padding: 14px;
        font-size: 14px;
        gap: 4px;
    }
    .ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2 {
     border-radius: 35px;
        padding: 10px 10px;
  
    }
    .ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1{
        border-radius: 35px;
        padding: 10px 10px;
       
    }
    .hsdjshfhdh {
        padding: 20px;
        gap: 25px;
    }
    .ndxjswbdjhhbsjhdbxjhsMain {
        padding: 20px;
    }
    .dshjhsdjadhsdjasjh2_div2_p1 {
     

        font-size: 35px;
        line-height: 36px;
        margin: 0 !important;
 

    }
    .dshjhsdjadhsdjasjh4{
        display: flex;
        flex-wrap: wrap;
    }


    .modal {
        background: #fff;
        border-radius: 12px;
        width: 95%;
        max-width: 100%;
        padding: 13px 15px 15px;
    }
    .filtersection1{
padding-right: 5px;
gap: 8px;
    }
    .filtersection1_div {
        flex-direction: column;
        gap: 5px;
        display: flex
    ;
    }
    .filtersection2{
padding-right: 0px ;
gap: 8px;
    }
    .filtersection3{
        padding-right: 0px ;
gap: 8px;
    }
    .filter_buttonsbtnDiv{
        gap: 4px;
    }
}


@media (max-width:400px) {
    .dshjhsdjadhsdjasjh2_div2_p1 {
        font-size: 27px;
        line-height: 27px;
        margin: 0 !important;
    }
    .dshjhsdjadhsdjasjh2_div2_p2 {
        font-size: 14px;
        line-height: 19px;
    }
    .dshjhsdjadhsdjasjh2_div2 {
        margin-bottom: 25px;
    }
    .gsvjxvsvdgvsP{
        font-size: 13px;
        line-height: 19px;
    }
.gsvjxvsvdgvsBtn{
    width: 100%;
}
.filtersection1_div_p {

 
    font-size: 12px;

    line-height: 17px;
}

}

@media (max-width:370px) {
    .filter_buttonsbtnDiv {
      width: 100%;
    }
    .reset-btn{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .apply-btn{
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center; 
    }
    .filtersection3{
padding-left: 5px;
    }
     .ndxjswbdjhhbsjhdbxjhsMain_1_2__2_2 {
        border-radius: 20px;
        gap: 4px;
        padding: 14px;
        font-size: 14px;
        width: 100%;
    }
    .ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1{
        border-radius: 20px;
        gap: 4px;
        padding: 12px;
        font-size: 14px;
        width: 100%;
        justify-content: start;
    }

}
