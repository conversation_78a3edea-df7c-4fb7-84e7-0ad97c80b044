import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chip,
} from "@mui/material";

const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 740,
  bgcolor: "background.paper",
  borderRadius: 2,
  boxShadow: 24,
  p: 3,
};

const types = ["Donation", "Volunteering"];
const categories = [
  "Education",
  "Food",
  "Housing",
  "Health",
  "Welfare",
  "Orphans",
  "Women",
  "Tech",
  "Advocation",
];
const cities = [
  "Lahore",
  "Karachi",
  "Islamabad",
  "Multan",
  "Faisalabad",
  "Quetta",
  "Peshawar",
  "Rawalpindi",
  "Sialkot",
  "Hyderabad",
];

interface FilterModalProps {
  open: boolean;
  onClose: () => void;
}
export default function FilterModal({ open, onClose }: FilterModalProps) {
  const [selectedType, setSelectedType] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedCity, setSelectedCity] = useState("");

  const handleReset = () => {
    setSelectedType("");
    setSelectedCategory("");
    setSelectedCity("");
  };

  return (
    <div>
      <Modal open={open} onClose={onClose}>
        <Box sx={style}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography variant="h6">Filter Project</Typography>

            <Box>
              <Button
                size="small"
                onClick={handleReset}
                sx={{
                  padding: "8px 12px",
                  borderRadius: "40px",
                  border: "1px solid #000",
                  textAlign: "center",
                  fontFamily: "ScandiaMedium",
                }}
              >
                Reset Filter
              </Button>
              <Button
                size="small"
                sx={{
                  padding: "8px 12px",
                  borderRadius: "40px",
                  border: "1px solid #000",
                  textAlign: "center",
                  fontFamily: "ScandiaMedium",
                  background:""
                }}
              >
                Apply Filter
              </Button>
            </Box>
          </Stack>

          {/* Filter by Type */}
          <Box sx={{ display: "flex" }}>
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                gap: "134px",
              }}
            >
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography variant="subtitle1">By Type</Typography>
                <Stack direction="column" spacing={1} flexWrap="wrap" my={1}>
                  {types.map((type) => (
                    <Chip
                      key={type}
                      label={type}
                      clickable
                      color={selectedType === type ? "primary" : "default"}
                      onClick={() => setSelectedType(type)}
                    />
                  ))}
                </Stack>
              </Box>

              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
            </Box>
            {/* Filter by Category */}

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                gap: "134px",
              }}
            >
              <Box sx={{ display: "flex", flexDirection: "column" }}>
                <Typography variant="subtitle1" mt={2}>
                  By Category
                </Typography>
                <Stack direction="column" spacing={1} flexWrap="wrap" my={1}>
                  {categories.map((cat) => (
                    <Chip
                      key={cat}
                      label={cat}
                      clickable
                      color={selectedCategory === cat ? "primary" : "default"}
                      onClick={() => setSelectedCategory(cat)}
                    />
                  ))}
                </Stack>
              </Box>

              <Divider orientation="vertical" flexItem sx={{ mx: 2 }} />
            </Box>
            {/* Filter by City */}
            <Box sx={{ display: "flex", flexDirection: "column" }}>
              <Typography variant="subtitle1" mt={2}>
                By City
              </Typography>
              <Stack direction="column" spacing={1} flexWrap="wrap" my={1}>
                {cities.map((city) => (
                  <Chip
                    key={city}
                    label={city}
                    clickable
                    color={selectedCity === city ? "primary" : "default"}
                    onClick={() => setSelectedCity(city)}
                  />
                ))}
              </Stack>
            </Box>
          </Box>
        </Box>
      </Modal>
    </div>
  );
}
