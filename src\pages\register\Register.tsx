"use client";
import RegisterWeb from "./RegisterWeb";
function Register() {
  return (
    <>
         <div className="bg-[white] p-[20px] mt-[100px] flex gap-spacing justify-start items-center h-[95vh]">
      <div
        style={{
          backgroundImage: "url('/Images/signin/form_partner.webp')",
          backgroundSize: "cover", // cover the whole div
          backgroundPosition: "center",
          borderRadius: "20px",
        }}
        className="w-[60%] h-[100%]"
      >
        <div className="flex flex-col gap-[16px] join-pading justify-center h-full">
          <p className="text-[50px] leading-[40px] font-[scandiaMedium] text-[white]">
            Join to give the impact
          </p>
          <p className="text-[22px] leading-[32px] font-[scandia] text-[white]">
            Join a community dedicated to rebuilding, empowering, and inspiring
            a brighter future for Syria.
          </p>
        </div>
      </div>
      <RegisterWeb />
    </div>
    </>
  )
}

export default Register;
