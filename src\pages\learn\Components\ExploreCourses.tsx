import Image from "next/image";
import React, { useEffect, useState } from "react";
import { CourseCard } from "./CodeOfEthics";
import FilterModal from "@/components/FilterModal/FilterModal";

const coursesList = [
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Rebuild Syria Network",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Books for Doctors",
    user: "Rebuild Syria Network",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Tools for Doctors",
    user: "Rebuild Syria Network",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Syrian Orphan Charity ",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Iltezam",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Ilzetam",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Green Syria Initiative",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
  {
    image:
      "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
    icon: "/Images/learn/card.png",
    title: "Ethics for Doctors",
    user: "Iltezam",
    description:
      "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
    students: "245",
    languages: "English, Arabic",
    isFeatured: false,
  },
];

const categories = ["All Topics", "Social", "Engineering", "Science"];

type Course = {
  image: string;
  icon: string;
  title: string;
  user: string;
  description: string;
  students: string;
  languages: string;
  isFeatured: boolean;
};

export const ExploreCourses = (): React.ReactElement => {
  const [filteredCourses, setFilteredCourses] = useState<Course[]>(coursesList);
  const [activeCategory, setActiveCategory] = useState("All Topics");
  const [isFilterOpen, setFilterOpen] = useState(false);
  const handleClose = () => setFilterOpen(false);
  const handleOpen = () => setFilterOpen(true);

  const searchCourse = (value: string) => {
    const searchValue = value.toLowerCase().trim();

    if (!searchValue) {
      setFilteredCourses(coursesList); // Show all if search is empty
      return;
    }

    const filtered = coursesList.filter((course) =>
      course.title.toLowerCase().includes(searchValue)
    );

    setFilteredCourses(filtered);
  };

  useEffect(() => {
    setFilteredCourses(coursesList);
  }, []);

  return (
    <div className="flex flex-col gap-8">
      <FilterModal open={isFilterOpen} onClose={handleClose} />

      <header className="flex flex-col gap-5 lg:gap-8">
        <div className="flex flex-col gap-2">
          <div className="flex justify-between items-center">
            <p className="font-[ScandiaMedium] text-[22px] text-[#181916]">
              Explore Courses
            </p>
            <div className="flex lg:hidden gap-2">
              <Image
                src="/Images/learn/search.png"
                alt="search"
                width={24}
                height={24}
              />
              <Image
                src="/Images/learn/filter.png"
                alt="filter"
                width={24}
                height={24}
              />
            </div>
          </div>
          <p className="font-[Scandia] text-[16px] text-[#303030] hidden lg:block">
            Discover learning paths to grow your skills, support your community,
            and lead with purpose.
          </p>
        </div>
        <div className="flex justify-between">
          <div className="flex gap-2 overflow-x-auto">
            {categories.map((category, index) => (
              <div
                key={index}
                onClick={() => setActiveCategory(category)}
                className={`flex justify-center items-center px-6 py-2 lg:py-4 rounded-full w-fit whitespace-nowrap cursor-pointer font-[ScandiaMedium] text-[12px] lg:text-[14px] leading-[1.125rem] 
            ${
              activeCategory === category
                ? "bg-[#2f3b31] text-white"
                : "border border-[#303030] text-[#181916]"
            }`}
              >
                {category}
              </div>
            ))}
          </div>
          <div className="lg:flex gap-2 hidden">
            <div className="flex w-[285px] items-center justify-start gap-2 border border-[#23232366] rounded-full px-6 py-[17px]">
              <Image
                src="/Images/learn/search.png"
                alt="search"
                width={18}
                height={18}
              />
              <input
                placeholder="Search"
                className="border-0 outline-0 focus:outline-0 focus:border-0 font-[Scandia] text-[16px] text-[#23232366] leading-4"
                onChange={(e) => searchCourse(e.target.value)}
              />
            </div>
            <div className="flex justify-start items-center gap-2 rounded-full px-6 py-[17px] bg-[#ECE7E3] cursor-pointer" onClick={handleOpen}>
              <Image
                src="/Images/learn/filter.png"
                alt="filter"
                width={18}
                height={18}
              />
              <p className="font-[ScandiaMedium] text-[14px] text-[#232323] leading-4">
                Filter
              </p>
            </div>
          </div>
        </div>
      </header>
      <div className="flex overflow-x-auto whitespace-nowrap gap-6 lg:grid lg:grid-cols-3 xl:grid-cols-4 lg:flex-none lg:whitespace-normal">
        {filteredCourses?.map((course, index) => (
          <div
            key={index}
            className="min-w-[290px] sm:min-w-[310px] shrink-0 lg:min-w-0 lg:w-full"
          >
            <CourseCard {...course} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default ExploreCourses;
