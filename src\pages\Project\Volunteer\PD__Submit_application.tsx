"use client";
import React from "react";
import {
  Card,
  CardContent,
 
  Box,

  Avatar,
} from "@mui/material";
import Image from "next/image";
import "../Donation/ProjectDetail";
import "./Volunteer.css";
const PD__Submit_application = () => {
  return (
    <div>
      <Box mt={15} className="mcncghfjshhhh" mb={4} bgcolor="#FCFCFC">
        <p
          className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31] mb-[24px] flex items-center gap-1  dhakghghsfshjsbjhseee"
          style={{ fontFamily: "ScandiaMedium" }}
        >
          <span>Project</span>
          <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
          <span>Rebuilding Children&apos;s Hospital in Aleppo</span>
          <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
          <span>Apply as volunteer</span>
          <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
          <span>Submit application</span>
        </p>
        <div className="tynxrw3mauluilqyrrt">
          <div className="hsdjhhjhddjhfdfnnsaty">
            <Card sx={{ border: "none", boxShadow: "none", padding: "0px" }}>
              <CardContent sx={{ padding: "0px" }}>
                <Box display="flex" alignItems="center" gap={1} mb={2} mt={1}>
                  <Avatar
                    src="/Img/city.png"
                    sx={{ width: "48px", height: "48px" }}
                  />
                  <div>
                    <p
                      className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                      style={{ fontFamily: "ScandiaMedium" }}
                    >
                      Rebuild Syria Network
                    </p>
                    <p
                      className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                      style={{ fontFamily: "Scandia" }}
                    >
                      Infrastructure & development
                    </p>
                  </div>
                </Box>
                <Box borderRadius={4} mb={2} overflow="hidden">
                  <Image
                    src="/Img/Syrian house.png"
                    alt="Main Project"
                    className="fsdhjsdfjdfhjjhsf"
                    width={800}
                    height={400}
                  />
                </Box>
                <div className="hdhhhyttrtrt">
                  <p
                    className=" sdfggsdfdhsgasdfhasdjajdjjdjd"
                    style={{ fontFamily: "ScandiaMedium" }}
                  >
                    Rebuilding Children&apos;s Hospital in Aleppo
                  </p>
                  <p
                    className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mb-[16px] m-[0px]"
                    style={{ fontFamily: "Scandia" }}
                  >
                    Reconstructing homes for displaced Syrian families, focusing
                    on sustainable and durable housing.
                  </p>
                </div>
                <Box mt={2}>
                  <p
                    className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                    style={{ fontFamily: "ScandiaMedium" }}
                  >
                    Details
                  </p>
                  <div className="flex items-center gap-[30px]">
                    <div className="flex items-center gap-[8px]">
                      <Image src="/Img/loc.svg" alt="Location" width={24} height={24} />
                      <div>
                        <p
                          className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs"
                          style={{ fontFamily: "Scandia" }}
                        >
                          Location:
                        </p>
                        <p
                          className="font-[400] leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs"
                          style={{ fontFamily: "Scandia" }}
                        >
                          Aleppo, Syria
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-[8px]">
                      <Image src="/Img/calendar.svg" alt="Calendar" width={24} height={24} />
                      <div>
                        <p
                          className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs"
                          style={{ fontFamily: "Scandia" }}
                        >
                          Project timeline:
                        </p>
                        <p
                          className="font-[400]  leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs"
                          style={{ fontFamily: "Scandia" }}
                        >
                          April–June 2025
                        </p>
                      </div>
                    </div>
                  </div>
                </Box>
              </CardContent>
            </Card>
          </div>
          {/* ....../ */}

          <div className="hsdjhhjhddjhfdfnnsatygggg2">
            <div className="tttggdgvsygvdhgsdvhkkkkkksat">
              <p className="yggtreww">Complete Application</p>
              <p className="desbxhjsbsjhbxsh">
                Submit your
                <span className="desbdjhbcjhebjhed">
                  {" "}
                  CV, motivation letter, and other documents{" "}
                </span>
                required for this position.
              </p>
            </div>
            <div className="tttggdgvsygvdhgsdvhkkkkkksat">
              <div className="tttggdgvsygvdhgsdvhkkkksatt1"> 
                <div className="tttggdgvsygvdhgsdvhkkkksatt11">
                  <p className="desbdjhbcjhebjhed">CV_Rami.pdf</p>
                  <Image src="/Img/projectImg/canceel.png" alt="Cancel" width={24} height={24} className="tttggdgvsygvdhgsdvhkkkksatt111" />
                </div>
                <div className="tttggdgvsygvdhgsdvhkkkksatt11">
                <p className="desbdjhbcjhebjhed">Medical lisence_Rami.pdf</p>
                <Image src="/Img/projectImg/canceel.png" alt="Cancel" width={24} height={24} className="tttggdgvsygvdhgsdvhkkkksatt111" />
                </div>
                <div className="tttggdgvsygvdhgsdvhkkkksatt11_second">

                <Image src="/Img/projectImg/uplooad.png" alt="Upload" width={24} height={24} className="tttggdgvsygvdhgsdvhkkkksatt111"/>
                <p className="desbdjhbcjhebjhed">Upload file</p>
                </div>
              </div>

              <p className="desbxhjsbsjhbxsh">
                Documents must be in
                <span className="desbdjhbcjhebjhed"> .doc or .pdf format </span>
                and should not exceed 2MB in size.
              </p>
            </div>
            <button className="yybcybcybcybyb">
            Submit application
            </button>


          </div>

          {/* ...... */}
        </div>
      </Box>
    </div>
  );
};

export default PD__Submit_application;
