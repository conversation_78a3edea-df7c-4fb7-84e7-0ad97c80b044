import {
  FacebookIcon,
  InstagramIcon,
  TwitterIcon,
  YoutubeIcon,
} from "lucide-react";
import React from "react";
import Image from "next/image";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Separator } from "../../../../components/ui/separator";

// Define footer link categories for mapping
const footerCategories = [
  {
    title: "Project",
    links: ["All Project", "Latest Volunteer", "Latest Donation"],
  },
  {
    title: "Community",
    links: ["Latest Joiner", "Latest Port"],
  },
  {
    title: "Learn",
    links: ["All Courses", "Hot Courses"],
  },
  {
    title: "Partner",
    links: ["Benefit", "Become a Partner"],
  },
  {
    title: "Get in touch",
    links: ["Contact"],
  },
];
const FooterDesktopByAnima = () => {
  return (
    <footer className="flex flex-col w-full items-start justify-center gap-16 pt-[60px] pb-10 px-6 md:px-[100px] relative bg-[#181916]">
      {/* Logo and Brand Name */}
      <div className="inline-flex flex-col items-start justify-center gap-[9.17px] relative">
        <div className="relative w-[40.46px] h-[53.4px] rounded-[6.47px] overflow-hidden">
          <div className="relative w-[67px] h-[67px] top-[-7px] left-px rotate-180">
            <div className="h-[67px]">
              <div className="relative w-[53px] h-[53px] top-[7px] left-[7px] bg-[#ffffff] rounded-[1212.48px] overflow-hidden rotate-[-162.20deg]">
                <div className="relative w-[54px] h-[58px] -top-2 -left-3">
                  <Image
                    className="absolute w-4 h-[17px] top-[33px] left-[31px] rotate-[56.23deg]"
                    alt="Union"
                    src="/union.svg"
                    width={16}
                    height={17}
                  />
                  <Image
                    className="absolute w-6 h-6 top-[5px] left-[25px] rotate-[56.23deg]"
                    alt="Vector"
                    src="/vector-105.svg"
                    width={24}
                    height={24}
                  />
                  <Image
                    className="absolute w-1.5 h-1.5 top-[43px] left-[42px] rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect.svg"
                    width={6}
                    height={6}
                  />
                  <Image
                    className="w-[5px] h-2 top-11 left-[29px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-1.svg"
                    width={5}
                    height={8}
                  />
                  <Image
                    className="w-[9px] h-1.5 top-[30px] left-[27px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-13.svg"
                    width={9}
                    height={6}
                  />
                  <Image
                    className="w-1.5 h-2 top-[35px] left-[45px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-11.svg"
                    width={6}
                    height={8}
                  />
                  <Image
                    className="w-2 h-1.5 top-7 left-[34px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-5.svg"
                    width={8}
                    height={6}
                  />
                  <Image
                    className="w-[5px] h-2 top-[29px] left-[43px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-4.svg"
                    width={5}
                    height={8}
                  />
                  <Image
                    className="w-[7px] h-1.5 top-[46px] left-[37px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-3.svg"
                    width={7}
                    height={6}
                  />
                  <Image
                    className="w-1.5 h-2 top-[38px] left-[26px] absolute rotate-[56.23deg]"
                    alt="Intersect"
                    src="/intersect-2.svg"
                    width={6}
                    height={8}
                  />
                  <Image
                    className="absolute w-[23px] h-[23px] top-3.5 left-2.5 rotate-[56.23deg]"
                    alt="Vector"
                    src="/vector-107.svg"
                    width={23}
                    height={23}
                  />
                  <Image
                    className="absolute w-[23px] h-[23px] top-[31px] left-[5px] rotate-[56.23deg]"
                    alt="Vector"
                    src="/vector-108.svg"
                    width={23}
                    height={23}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="inline-flex flex-col items-start justify-center gap-[1.08px] relative">
          <div className="inline-flex flex-col items-start relative">
            <div className="relative w-fit mt-[-0.54px] [font-family:'Scandia-Bold',Helvetica] font-bold text-white text-[18.3px] tracking-[0] leading-normal whitespace-nowrap">
              ILZETAM
            </div>
          </div>
          <div className="relative w-fit [font-family:'Co_Headline_Arbc-Bold',Helvetica] font-bold text-white text-[9.7px] text-center tracking-[0] leading-normal [direction:rtl]">
            مؤسسة التزام
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="items-start justify-between w-full flex flex-col md:flex-row gap-8 md:gap-0">
        {/* Newsletter and Social Media Section */}
        <div className="flex flex-col w-full md:w-[426px] items-start gap-16">
          {/* Newsletter Subscription */}
          <div className="flex flex-col items-start gap-5 relative w-full">
            <h5 className="font-default-h5-headline-5-bold text-white">
              Stay connected and updated
            </h5>
            <div className="flex flex-col sm:flex-row items-center gap-3 w-full">
              <div className="h-[52px] w-full">
                <div className="flex items-center h-full w-full">
                  <Input
                    className="flex-1 h-full bg-transparent text-white opacity-80 rounded-[99px] border-white px-6"
                    placeholder="Enter email"
                  />
                </div>
              </div>
              <Button className="w-full sm:w-[100px] bg-base text-[#181916] rounded-[70px] px-4 py-4 font-default-l1-label-1-bold">
                Subscribe
              </Button>
            </div>
          </div>

          {/* Social Media Icons */}
          <div className="flex items-center gap-4 w-full">
            <FacebookIcon className="w-6 h-6 text-white" />
            <InstagramIcon className="w-6 h-6 text-white" />
            <TwitterIcon className="w-6 h-6 text-white" />
            <YoutubeIcon className="w-6 h-6 text-white" />
          </div>
        </div>

        {/* Footer Links Section */}
        <div className="flex flex-wrap gap-8 md:gap-[64px] justify-start md:justify-end w-full md:w-[612px]">
          {footerCategories.map((category, index) => (
            <div
              key={index}
              className="flex flex-col w-[148px] items-start gap-5"
            >
              <div className="flex flex-col items-start gap-[13px] w-full">
                <h6 className="font-default-h6-headline-6-bold text-white">
                  {category.title}
                </h6>
                <div className="flex flex-col gap-2 w-full">
                  {category.links.map((link, linkIndex) => (
                    <a
                      key={linkIndex}
                      href="#"
                      className="font-default-p2-paragraph-2-reg text-white hover:underline"
                    >
                      {link}
                    </a>
                  ))}
                </div>
              </div>
            </div>
          ))}
          <div className="w-[148px] h-[92px]" />{" "}
          {/* Empty space for alignment */}
        </div>
      </div>

      {/* Footer Bottom Section */}
      <div className="flex flex-col items-start gap-6 w-full">
        <Separator className="w-full bg-white/20" />
        <div className="flex flex-col sm:flex-row justify-between w-full items-start sm:items-center gap-4 sm:gap-0">
          <p className="font-default-p2-paragraph-2-reg text-white whitespace-nowrap">
            Copyright © 2025. All rights reserved.
          </p>
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-10">
            <a
              href="#"
              className="font-default-p2-paragraph-2-reg text-white whitespace-nowrap hover:underline"
            >
              Privacy Policy
            </a>
            <a
              href="#"
              className="font-default-p2-paragraph-2-reg text-white whitespace-nowrap hover:underline"
            >
              Cookie policy
            </a>
            <a
              href="#"
              className="font-default-p2-paragraph-2-reg text-white whitespace-nowrap hover:underline"
            >
              Terms and conditions
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};
export default FooterDesktopByAnima;
