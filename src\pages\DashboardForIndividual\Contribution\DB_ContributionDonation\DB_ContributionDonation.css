.hdjsbdjbsjhDB_db{
    padding: 40px 100px ;
    display: flex;
    gap: 24px;
    background: #ECE7E3;
    margin-top: 100px;

}
.hdhhdhdhh{
    width: 328px;

    border-radius: 20px;
    padding: 32px;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    gap: 34px;
}
.DB_ContributionDonation_Main2{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 26px;
    max-width: 960px;
}

.DB_ContributionDonation_Main2hfdbjhfbdp{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
color: #303030;
margin: 0px !important;
}
  .hbdjbjshbjshbsetetette{
height: 630px;
    border-radius: 20px;
    padding: 32px;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    gap: 34px;

  }

  .dbjh<PERSON><PERSON>yeyeeg{
    padding-bottom: 24px;
    border-bottom: 1px solid #0000000A;
  }

  .hdhhdhdhhdhddhdhhdhuu{
    padding-top: 8px;
    padding-bottom: 8px;
  }
.hddhhdhhhbbbbhdhdhhd{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 16px;
line-height: 24px;
letter-spacing: 0%;
vertical-align: middle;
color: #000000;

}
.hddhhdhhhbbbbhdhdhhdpP{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
vertical-align: middle;
color: #181916;
white-space: nowrap;

}
.hddhhdhhhbbbbhdhdhhdpP2{
    font-family: Scandia;
font-weight: 400;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
vertical-align: middle;
color: #23232366;
white-space: nowrap;


}

.hddhhdhhhbbbbhdhdhhdpP3{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
vertical-align: middle;

}
.hhdhhdhdhhdeeeeere{
    border: 1px solid #23232366;
    border-radius: 99px;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}
.hfhfvshgvhsgvhgscv{

padding-top: 16px;
padding-right: 24px;
padding-bottom: 16px;
padding-left: 24px;
display: flex;
align-items: center;
justify-content: center;
gap: 6px;
border-radius: 70px;
background: #ECE7E3;
font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #181916;


}
.bhbjbjhbjhbjhbjjhjhbnn{
    font-family: "Scandia";
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    vertical-align: middle;

    color: #181916;


}
.bhbjbjhbjhbjhbjjhjhbnn::placeholder{
    font-family: "Scandia";
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    vertical-align: middle;

    color: #23232366;
}


.hbsjhjhvjhvjuytr{
    padding-top: 16px;
    padding-right: 24px;
    padding-bottom: 16px;
    padding-left: 24px;
    border-radius: 80px;
    background: #2F3B31;
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #FFFFFF;
display: flex;
    align-items: center;
    justify-content: center;

    
}
.hbsjhjhvjhvjuytr1{
    padding-top: 16px;
    padding-right: 24px;
    padding-bottom: 16px;
    padding-left: 24px;
    border-radius: 60px;
    border-width: 1px;
    border: 1px solid #303030;
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #303030;
display: flex;
    align-items: center;
    justify-content: center;

    
}

.scrollablesectionss {
    max-height: 430px;
    overflow-y: auto;
    padding-right: 4px;
  }
  
  /* Optional: Hide scrollbar */
  .scrollablesectionss::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }
  .scrollablesectionss {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none;    /* Firefox */
  }
  


@media(max-width: 1300px){
    .hdjsbdjbsjhDB_db {
   
        gap: 24px;
        padding: 40px 60px;
    }
    .hbdjbjshbjshbsetetette{
        height: unset;
    }
}



@media(max-width: 1000px){

.hdjsbdjbsjhDB_db{
flex-direction: column;
gap: 20px;
padding: 40px 40px;
}

}


@media(max-width: 770px){
.dbjhbyyyeyeeg{
flex-direction: column;
align-items: start;
}
.hsjbsjhbjhsbhjrtdtr{
    width: 100%;
}
    .hhdhhdhdhhdeeeeere{
        width: 100%;
    }

    .hbdjbjshbjshbsetetette{
        padding: 24px;
    }
    .hhdhhdhdhhdeeeeere {
        padding: 12px 20px;
        border-radius: 20px;
    }
    .hfhfvshgvhsgvhgscv {
        padding: 14px 24px;
        border-radius: 20px;

    }
    .hbsjhjhvjhvjuytr{
        padding: 14px 24px;
        border-radius: 50px;
    }
    .hbsjhjhvjhvjuytr1{
        padding: 14px 24px;
        border-radius: 50px;
    }
   
 
      .hdhhdhdhh{
display: none;
      }
}

@media(max-width: 770px){
    .hdhhdhdhhdhddhdhhdhuu { 
        overflow-x: auto; /* hide horizontal scroll */
        width: 100%;
        white-space: nonrmal; /* wrap text instead of going out */
        scrollbar-width: none; /* Firefox */
        gap: 70px;
      }
      
      /* Hide scrollbar for Chrome, Safari and Edge */
      .hdhhdhdhhdhddhdhhdhuu::-webkit-scrollbar {
        display: none;
      }

}

@media(max-width: 430px){

    .hdjsbdjbsjhDB_db {
        flex-direction: column;
        gap: 20px;
        padding: 40px 25px;
    }
    .hbdjbjshbjshbsetetette{
        padding: 24px 20px;
    }


}

@media(max-width: 400px){
    .hrarrararbtn{
        width: 100%;
    }
    .hbsjhjhvjhvjuytr{
width: 100%;
    }
 .hbsjhjhvjhvjuytr1{
        width: 100%;
            }
            .hdjsbdjbsjhDB_db {
                gap: 15px;
                padding: 40px 15px;
            }
}
