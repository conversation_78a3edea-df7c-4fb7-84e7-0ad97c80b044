"use client";
import React from "react";
// import { useState } from "react";
import GroupCard from "@/components/communityGroupCard/GroupCard";    

function Group() {
  return (
    <div className="bg-[#FFFFFF] rounded-[20px] flex flex-col md:gap-[34px] gap-[19px]  p-[32px] max-[900px]:px-[0]">
    <div className="hidden md:flex gap-[8px]">
         <button style={{
                fontFamily:"ScandiaMedium",
                background:"#ECE7E3"

              }} className="font-[scandiaMedium] flex gap-[6px] items-center cursor-pointer text-[#181916] text-[14px] leading-[20px] rounded-[70px] border-none py-[16px] px-[24px]  ">
             <img src="/Images/CommunityMain/filter.svg" alt="filter"/>   Filter
              </button>
<div className="flex items-center gap-[8px] w-[285px] rounded-[99px] border" style={{ border: "1px solid #23232366" }}>
  <img src="/Images/CommunityMain/search.svg" alt="search" className="w-[16px] h-[16px] ml-[24px]" />
  <input
    className="bg-transparent flex-1 focus:outline-none focus:ring-0 placeholder:font-[scandia] text-[14px] font-[ScandiaMedium] py-[13px] pr-[24px] placeholder-[#23232366]"
    placeholder="Enter email"
  />
</div>

             
    </div>
    <div className="flex md:hidden justify-between">
<p className="text-[#181916] text-[22px] leading-[32px] font-[ScandiaMedium]">
    Group
</p>
<div className="flex gap-[12px]">
<img src="/Images/CommunityMain/search.svg" alt="search" className="w-[24px] h-[24px] cursor-pointer" />
 <img src="/Images/CommunityMain/filter.svg" alt="filter" className="w-[24px] h-[24px] cursor-pointer"/>
</div>
    </div>
<img style={{width:"100%"}} src="/Images/CommunityMain/seperator.png" alt="seperator" />
<div className="flex flex-col sm:gap-[32px]  gap-[18px]">
<div className="flex gap-[64px] max-[1300px]:gap-[30px] max-[768px]:gap-[8px] max-[1150px]:flex-col">
  <div className="flex flex-col gap-[8px] w-full">
    <GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Syria Rising"
  members="1M"
  buttonText="View group chat"
/>

<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>

<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>

  </div>
   <div className="flex flex-col gap-[8px] w-full">
    <GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Syria Rising"
  members="1M"
  buttonText="View group chat"
/>

<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>

<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>
<GroupCard
  imageSrc="Images/CommunityMain/group1.png"
  title="Women in Syria"
  members="559K"
  buttonText="Join"
/>

  </div>
</div>

 <img
        style={{ width: "100%" }}
        className="block sm:hidden"
        src="/Images/CommunityMain/seperator.png"
        alt="seperator"
      />
<div className="flex w-full flex-col items-center">
<p className="text-[#303030] text-[14px] leading-[20px] font-[ScandiaMedium] cursor-pointer">Load more</p>
</div>
</div>
    </div>
  );
}

export default Group;
