import React from "react";
import { MoreHorizontalIcon } from "lucide-react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import Image from "next/image";

// Community posts data
const communityPosts = [
  {
    id: 1,
    author: {
      name: "<PERSON>",
      profession: "Software Engineer",
      avatar: "/Images/homepage/omar.png", // Image URL would go here
    },
    image: "/Images/homepage/womenWater.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px] md:h-[395px]",
  },
  {
    id: 2,
    author: {
      name: "<PERSON><PERSON>",
      profession: "Politician",
      avatar: "/Images/homepage/faris.png", // Image URL would go here
    },
    image: "/Images/homepage/society.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px]",
  },
  {
    id: 3,
    author: {
      name: "<PERSON><PERSON><PERSON>",
      profession: "Architecture",
      avatar: "/Images/homepage/tariq.png", // Image URL would go here
    },
    image: "/Images/homepage/siblings.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px] md:h-[395px]",
  },
  {
    id: 4,
    author: {
      name: "Walid Habib",
      profession: "Lawyer",
      avatar: "/Images/homepage/walid.png", // Image URL would go here
    },
    image: "/Images/homepage/friends.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px]",
  },
  {
    id: 5,
    author: {
      name: "Youssef Hamdan",
      profession: "Doctor",
      avatar: "/Images/homepage/youssef.png", // Image URL would go here
    },
    image: "/Images/homepage/tent.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px]",
  },
  {
    id: 6,
    author: {
      name: "Salma Zayed",
      profession: "Teacher",
      avatar: "/Images/homepage/salma.png", // Image URL would go here
    },
    image: "/Images/homepage/flowers.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px] md:h-[395px]",
  },
  {
    id: 7,
    author: {
      name: "Sara Al-Masri",
      profession: "Nurse",
      avatar: "/Images/homepage/sara.png", // Image URL would go here
    },
    image: "/Images/homepage/playing.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px] md:h-[395px]",
  },
  {
    id: 8,
    author: {
      name: "Rana Mansour",
      profession: "Medical Student",
      avatar: "/Images/homepage/rana.png", // Image URL would go here
    },
    image: "/Images/homepage/dryingClothes.png", // Image URL would go here
    title: "What a great day to start helping the community",
    date: "24 Feb 2025",
    imageHeight: "h-[215px]",
  },
];

// Group posts into columns (4 columns with 2 posts each)
const columns = [
  communityPosts.slice(0, 2),
  communityPosts.slice(2, 4),
  communityPosts.slice(4, 6),
  communityPosts.slice(6, 8),
];

const MainContainerByAnima = () => {
  return (
    <section className="flex flex-col items-center justify-center gap-10 px-6 lg:px-[50px] xl:px-[100px] py-16 w-full bg-[#ECE7E3]">
      <header className="flex flex-col md:flex-row md:items-center gap-4 w-full">
        <div className="flex flex-col gap-2 flex-1">
          <h2 className="font-[ScandiaMedium] md:text-[28px] text-[#181916] text-[22px]">
            Latest from the Community
          </h2>
          <p className="font-[Scandia] text-[16px]">
            Stay updated with the community&apos;s latest posts.
          </p>
        </div>
        <Button className="px-16 py-4 bg-[#D31B1B] w-fit md:w-auto rounded-[80px] text-white font-[ScandiaMedium] text-[16px] h-auto cursor-pointer">
          Join Community
        </Button>
      </header>

      <div
        className="
    flex flex-row overflow-x-auto gap-4 w-full
    md:grid md:grid-cols-2 lg:grid-cols-4 md:overflow-visible
    no-scrollbar
  "
      >
        {columns.map((column, columnIndex) => (
          <div
            key={`column-${columnIndex}`}
            className="flex flex-row md:flex-col gap-4"
          >
            {column.map((post) => (
              <Card
                key={post.id}
                className="w-full md:w-auto min-w-[310px] md:min-w-0 flex flex-col items-start justify-center gap-4 p-5 bg-white rounded-[20px] border border-solid border-[#0000000a] shadow-card-shadow"
              >
                <CardContent className="p-0 w-full">
                  <div className="flex items-center justify-between w-full mb-4">
                    <div className="flex items-center gap-2">
                      <Image
                        className="w-10 h-10 object-cover rounded-full"
                        alt={`${post.author.name}&apos;s avatar`}
                        src={post.author.avatar}
                        width={40}
                        height={40}
                      />
                      <div className="flex flex-col">
                        <span className="font-[ScandiaMedium] text-[#191816] text-[14px] ">
                          {post.author.name}
                        </span>
                        <span className="font-[Scandia] text-[#303030] text-[12px]">
                          {post.author.profession}
                        </span>
                      </div>
                    </div>
                    <button className="p-1">
                      <MoreHorizontalIcon className="w-[18px] h-[18px] text-gray-500 cursor-pointer" />
                    </button>
                  </div>

                  <Image
                    className={`w-full ${post.imageHeight} object-cover mb-4 rounded-[12px]`}
                    alt="Post image"
                    src={post.image}
                    width={500}
                    height={300}
                  />

                  <div className="flex flex-col gap-2.5">
                    <h3 className="font-[ScandiaMedium] text-[#191816] text-[14px] ">
                      {post.title}
                    </h3>
                    <time className="font-[Scandia] text-[#23232366] text-[12px] ">
                      {post.date}
                    </time>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ))}
      </div>
    </section>
  );
};
export default MainContainerByAnima;


