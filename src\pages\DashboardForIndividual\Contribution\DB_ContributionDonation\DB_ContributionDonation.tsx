// DB_ContributionDonation.tsx
import React, { useState } from "react";
import "./DB_ContributionDonation.css";
import Image from "next/image";
const contributions = {


  March: [
    {
      title: "Rebuilding Children's Hospital in Aleppo",
      org: "Rebuild Syria Network",
      type: "One-time",
      status: "Success",
      amount: "$1,000",
      img: "/Images/contribution/1.png",
    },
    {
      title: "Any projects",
      org: "Syrian Orphan Charity",
      type: "Monthly",
      status: "Success",
      amount: "$100",
      img: "/Images/contribution/2.png",
    },
    {
      title: "Homes for Hope",
      org: "Rebuild Syria Network",
      type: "One-time",
      status: "Success",
      amount: "$500",
      img: "/Images/contribution/3.png",
    },
  ],
  February: [
    {
      title: "Any projects",
      org: "Syrian Orphan Charity",
      type: "Monthly",
      status: "Success",
      amount: "$100",
      img: "/Images/contribution/1.png",
    },
    {
      title: "Any projects",
      org: "Syrian Orphan Charity",
      type: "Monthly",
      status: "Failed",
      amount: "$100",
      img: "/Images/contribution/1.png",
    },
  ],
};

const volunteerContributions = {
  March: [
    {
      title: "Rebuilding Children's Hospital in Aleppo",
      org: "Rebuild Syria Network",
      type: "Doctor",
      status: "Completed",
      amount: "Applied",
      img: "/Images/contribution/1.png",
    },
    {
      title: "Education for a brighter future",
      org: "Syrian Orphan Charity",
      type: "Teacher",
      status: "Completed",
      amount: "Applied",
     img: "/Images/contribution/1.png",
    },
  ],
};

const DB_ContributionDonation = () => {
  const [activeTab, setActiveTab] = useState<'Donation' | 'Volunteer'>('Donation');





  return (
    <div className="hdjsbdjbsjhDB_db">
      {/* <CommunityMain/> */}
<div className="hdhhdhdhh"></div>
      <div className=" max-w-[960px] DB_ContributionDonation_Main2">
        <p className="DB_ContributionDonation_Main2hfdbjhfbdp">Contribution</p>
        <div className=" hbdjbjshbjshbsetetette">
          {/* Header Tabs and Search */}
          <div className="flex flex-wrap justify-between items-center gap-4 dbjhbyyyeyeeg">
            <div className="flex gap-3   hrarrararbtn ">
            <button
        onClick={() => setActiveTab('Donation')}
        className={activeTab === 'Donation' ? 'hbsjhjhvjhvjuytr' : 'hbsjhjhvjhvjuytr1'}
      >
        Donation
      </button>

      <button
        onClick={() => setActiveTab('Volunteer')}
        className={activeTab === 'Volunteer' ? 'hbsjhjhvjhvjuytr' : 'hbsjhjhvjhvjuytr1'}
      >
        Volunteer
      </button>
            </div>

            <div className="flex items-center gap-2 w-full sm:w-auto hsjbsjhbjhsbhjrtdtr">
              <div className=" w-full sm:w-64  hhdhhdhdhhdeeeeere">
              <Image     src={"/Images/contribution/search-01.png"}   alt=""    width={18}
                        height={18}  
                  />
                <input
                  type="text"
                  placeholder="Search"
                  className="w-full  focus:outline-none bhbjbjhbjhbjhbjjhjhbnn"
                />
               
              </div>
              <button className="flex items-center  bg-[#EFE9E3] hfhfvshgvhsgvhgscv">
              <Image     src={"/Images/contribution/filter.png"}   alt=""    width={18}
                        height={18}  
                  />
                Filter
              </button>
            </div>
          </div>

   {/* ✅ Show this only if Donation tab is active */}
{activeTab === 'Donation' && (
  <div  className="scrollablesectionss">
    {Object.entries(contributions).map(([month, items]) => (
      <div key={month}>
        <h2 className="text-lg font-semibold hddhhdhhhbbbbhdhdhhd">{month}</h2>
        <div className="flex flex-col gap-4 mt-2 bjhabjhbjhbjbjhyyyyflow">
          {items.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center hdhhdhdhhdhddhdhhdhuu"
            >
              <div className="flex gap-4 items-center">
                <Image
                  src={item.img}
                  alt={item.title}
                  className="w-12 h-12 rounded-md object-cover"
                  width={48}
                  height={48}
                />
                <div>
                  <div className="hddhhdhhhbbbbhdhdhhdpP">{item.title}</div>
                  <div className="hddhhdhhhbbbbhdhdhhdpP2">{item.org}</div>
                </div>
              </div>
              <div className="hddhhdhhhbbbbhdhdhhdpP2">{item.type}</div>
              <div
                className={`text-sm font-medium hddhhdhhhbbbbhdhdhhdpP3 ${
                  item.status === "Success" ? "text-green-600" : "text-red-600"
                }`}
              >
                {item.status}
              </div>
              <div
                className="font-bold hddhhdhhhbbbbhdhdhhdpP3"
                style={{ color: "#181916" }}
              >
                {item.amount}
              </div>
            </div>
          ))}
        </div>
      </div>
    ))}
  </div>
)}

{/* ✅ Show this only if Volunteer tab is active */}
{activeTab === 'Volunteer' && (
  <div className="scrollablesectionss">
    {Object.entries(volunteerContributions).map(([month, items]) => (
      <div key={month}>
        <h2 className="text-lg font-semibold hddhhdhhhbbbbhdhdhhd">{month}</h2>
        <div className="flex flex-col gap-4 mt-2 bjhabjhbjhbjbjhyyyyflow">
          {items.map((item, index) => (
            <div
              key={index}
              className="flex justify-between items-center hdhhdhdhhdhddhdhhdhuu"
            >
              <div className="flex gap-4 items-center">
                <Image
                  src={item.img}
                  alt={item.title}
                  className="w-12 h-12 rounded-md object-cover"
                  width={48}
                  height={48}
                />
                <div>
                  <div className="hddhhdhhhbbbbhdhdhhdpP">{item.title}</div>
                  <div className="hddhhdhhhbbbbhdhdhhdpP2">{item.org}</div>
                </div>
              </div>
              <div className="hddhhdhhhbbbbhdhdhhdpP2">{item.type}</div>
             
              <div
                className="font-bold hddhhdhhhbbbbhdhdhhdpP3"
                style={{ color: "#181916" }}
              >
                {item.amount}
              </div>
            </div>
          ))}
        </div>
      </div>
    ))}
  </div>
)}




        </div>
      </div>
    </div>
  );
};

export default DB_ContributionDonation;
