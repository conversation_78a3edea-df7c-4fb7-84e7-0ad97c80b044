import React from "react";
// Remove the unused import
// import FortgetpassMob from "./ForgetpassMob";
import FortgetpassWeb from "./FortgetpassWeb";

function Forgetpass() {
  return (
    <div className="bg-[white] max-[919px]:bg-[#2F3B31]  p-[20px] flex max-[919px]:flex-col  gap-spacing justify-start mt-[100px] max-[919px]:items-start items-center h-[95vh]">
      <div
        style={{
          backgroundImage: "url('/Images/signin/form_partner.webp')",
          backgroundSize: "cover", // cover the whole div
          backgroundPosition: "center",
          borderRadius: "20px",
        }}
        className="w-[60%] max-[919px]:hidden max-[919px]:w-full h-[100%]"
      >
        <div className="flex flex-col gap-[16px] join-pading justify-center h-full">
          <p className="text-[50px] max-[380px]:text-[30px] max-[919px]:text-[40px] max-[919px]:leading-[40px] leading-[40px] font-[scandiaMedium] text-[white]">
            Join to give the impact
          </p>
          <p className="max-[919px]:text-[14px] text-[22px] max-[919px]:leading-[20px] leading-[32px] font-[scandia] text-[white]">
            Join a community dedicated to rebuilding, empowering, and inspiring
            a brighter future for Syria.
          </p>
        </div>
      </div>
      <div className="flex flex-col max-[919px]:block hidden  join-pading justify-center ">
        <p className="text-[50px] max-[919px]:pb-[12px] max-[380px]:text-[32px] max-[919px]:text-[40px] max-[919px]:leading-[40px] leading-[40px] font-[scandiaMedium] text-[white]">
          Join to give the impact
        </p>
        <p className="max-[919px]:text-[14px] text-[22px] max-[919px]:leading-[20px] leading-[32px] font-[scandia] text-[white]">
          Join a community dedicated to rebuilding, empowering, and inspiring a
          brighter future for Syria.
        </p>
      </div>
      <FortgetpassWeb />
    </div>
  );
}

export default Forgetpass;
