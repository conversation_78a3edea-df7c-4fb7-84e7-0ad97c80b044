"use client";
// Remove unused import
import './mail.css'
import Image from "next/image";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useState } from "react";
import { toast } from 'react-toastify';
import { resendVerification, verifyEmail } from '@/services/redux/middleware/register';
import { AppDispatch } from "../../../services/redux/store";
import { useDispatch } from 'react-redux';


function MailVerificationWeb() {
  // Remove unused state variables
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams?.get("email");
  const [code, setCode] = useState(['', '', '', '']);
  const [loading, setLoading] = useState<boolean>(false);
  const type = searchParams?.get("type") ?? "individual";
  const [resendloading, setResendLoading] = useState<boolean>(false);
  const dispatch = useDispatch<AppDispatch>();
  
  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const { value } = e.target;

    if (value.length <= 1) {
      const updatedCode = [...code];
      updatedCode[index] = value;
      setCode(updatedCode);

      if (value !== '' && index < code.length - 1) {
        document.getElementById(`input-${index + 1}`)?.focus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace') {
      const updatedCode = [...code];

      if (code[index] === '') {
        if (index > 0) {
          document.getElementById(`input-${index - 1}`)?.focus();
        }
      } else {
        updatedCode[index] = '';
        setCode(updatedCode);
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, 4); // Get first 4 characters
    if (pastedData.length > 0) {
      const updatedCode = pastedData.split("");
      while (updatedCode.length < 4) {
        updatedCode.push("");
      }
      setCode(updatedCode);


      setTimeout(() => {
        document.getElementById(`input-${pastedData.length - 1}`)?.focus();
      }, 10);
    }
  };


  const handleVerify = async () => {
    const joinedCode = code.join('');

    if (joinedCode.length < 4 || code.includes("")) {
      toast.error("Enter the complete code");
      return;
    }

    if (!email) {
      toast.error("Email is missing.");
      return;
    }

    const data = {
      email: email,
      code: joinedCode
    };

    console.log("dataaa", data);
    setLoading(true);
    try {
      const result = await dispatch(verifyEmail(data));
      console.log(result);

      if (result?.payload?.statusCode === 200) {
        setLoading(false);
        setTimeout(() => {
          toast.success("Verification successful!");
          // type === "individual" ? 
          if (type === "individual"){
            router.push(`/Auth/Register/verified-mail?type=${type}`)
          }else{
            router.push("/Auth/Register/verified-org");
          }
        
          //  : router.push("/Auth/Register/verified-org");


        }, 100);
      } else {
        setLoading(false);
        setTimeout(() => {
          toast.error("Verification failed, please try again.");
        }, 100)
      }
    } catch (error) {
      setLoading(false);
      console.error(error);
    }
  };

  const handleResendCode = async () => {
    if (!email) {
      toast.error("Email is missing.");
      return;
    }

    try {
      setResendLoading(true);
      const data = { email: email };
      const result = await dispatch(resendVerification(data));
      toast.success("Email resent successfully");
      setResendLoading(false);
      console.log(result);
    } catch (error) {
      console.error("Error resending email:", error);
    }
  };


  return (
    // <div className="flex flex-col items-center w-[40%] gap-[40px]">
    //   <div>
    //     <Image
    //       src="/Images/signin/mail-receive-01.svg"
    //       alt="lock"
    //       width={64}
    //       height={64}
    //     />
    //   </div>

    //   <div className="flex flex-col gap-[12px]">
    //     <p className="text-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
    //       Email verification
    //     </p>
    //     <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
    //       Email activation has been sent to
    //       <span className="text-[14px] cursor-pointer font-[scandiaMedium] text-[black]">
    //         {" "}
    //         {email}.
    //       </span>{" "}
    //       Check <br className="hiden-block-stlying" /> your inbox to activate.
    //     </p>
    //   </div>

    //   <div className="flex flex-col gap-[12px]">
    //     <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
    //       Didn’t receive any email?
    //     </p>
    //     <Button
    //       onClick={() => {
    //         if (isResendActive) handleResend();
    //       }}
    //       disabled={!isResendActive}
    //     >
    //       <p className="text-[14px] font-[scandiaMedium] text-[white]">
    //         {isResendActive ? "Resend Email" : `Resend in ${seconds}s`}
    //       </p>
    //     </Button>
    //   </div>
    // </div>
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:rounded-[20px] max-[370px]:px-[18px] max-[919px]:py-[40px] flex-col items-center max-[919px]:w-full w-[40%] max-[919px]:gap-[24px] gap-[40px]">
      <div>
        {/* Replace img with Image component */}
        <Image
          src="/Images/signin/mail-receive-01.svg"
          alt="Email verification"
          width={64}
          height={64}
          className="max-[919px]:w-[40px] max-[919px]:h-[40px] w-[64px] h-[64px]"
        />
      </div>
      <div className="flex flex-col gap-[12px]">
        <p className="max-[919px]:text-[22px] text-[32px] max-[919px]:leading-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
          Email verification
        </p>
        <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
          Email activation has been sent to
          <span className="text-[14px] cursor-pointer font-[scandiaMedium] text-[black]">
            {" "}
          </span>{" "}
          Check <br className="hiden-block-stlying" /> your inbox to activate.
        </p>
      </div>
      <div className="input-group flex justify-center gap-2 my-4">
        {code?.map((value, index) => (
          <input
            key={index}
            id={`input-${index}`}
            type="text"
            maxLength={1}
            value={value}
            className="code-input w-12 h-12 text-center border border-gray-300 rounded-md focus:outline-none focus:border-[#D31B1B]"
            onChange={(e) => handleCodeChange(e, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={index === 0 ? handlePaste : undefined}
          />
        ))}
      </div>
      {/* <div className="flex flex-col gap-[12px]">
        <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
           Didn’t receive any email?
         </p>
        <Button
          onClick={() => {
            if (isResendActive) handleResend();
          }}
          disabled={!isResendActive}
        >
          <p className="text-[14px] font-[scandiaMedium] text-[white]">
            {isResendActive ? "Resend Email" : `Resend in ${seconds}s`}
          </p>
        </Button>
      </div> */}

      <div
        className="flex justify-center gap-[16px]"
      >


        <button
          onClick={handleResendCode}
          style={{
            height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
            display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
          }}
        >
          {resendloading ? (
            <div className="loader"></div>
          ) : (
            <p
              className="text-[14px] text-[white] font-[scandiaMedium]"

            >
              Resend
              {/* in {seconds}s */}
            </p>
          )}
        </button>

        <button
          onClick={handleVerify}
          style={{
            height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
            display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
          }}
        >
          {loading ? (
            <div className="loader"></div>
          ) : (
            <p
              className="text-[14px] text-[white] font-[scandiaMedium]"
            >
              Verify Otp
            </p>
          )}
        </button>
      </div>
    </div>
  );
}

// Remove this extra Image component outside the function
export default MailVerificationWeb;
