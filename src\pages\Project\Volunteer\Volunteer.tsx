"use client";
import React, { useState } from "react";
import "./Volunteer.css";
import Image from "next/image";
import {
  Card,
  CardContent,
  Box,
  Avatar,
  IconButton,
  Chip,
} from "@mui/material";
import { useRouter } from 'next/navigation';

export default function Volunteer() {
  const router = useRouter();
  const [showMore, setShowMore] = useState(false);
  const images = [
    "/Img/Syrian house (1).png",
    "/Img/construction work.png",
    "/Img/reconstruct.png",
    "/Img/rereere.png",
  ];
  const jobData = [
    {
      title: "Doctor",
      positions: 3,
      icon: "/Img/projectImg/doctor-01.png", // Replace with your actual icon path
    },
    {
      title: "Accountant",
      positions: 3,
      icon: "/Img/projectImg/briefcase-01.png",
    },
    {
      title: "Project Manager",
      positions: 3,
      icon: "/Img/projectImg/laptop 2.png",
    },
  ];
  return (
    <div>
    <div className="vfcghjvfghcvjd1  bg-[#fbfbfb] ">
      {/* Breadcrumb */}
      <p
        className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31]  flex items-center gap-1  bjhdbjhbjb"
        style={{ fontFamily: "ScandiaMedium" }}
      >
        <span>Project</span>
        <Image src="/Img/arrow.svg" alt="arrow" width={16} height={16} />
        <span>Rebuilding Children`&apos;`s Hospital in Aleppo</span>
      </p>

      <Box
        display="flex"
        flexDirection={{ xs: "column", lg: "row" }}
        gap={4}
        mt={2}
      >
        {/* Left Column - Image Gallery */}
        <Box flex={1}>
          <Box borderRadius={4} overflow="hidden">
            <Image
              src="/Img/Syrian house.png"
              alt="Main Project"
              width={600}
              height={400}
              style={{ width: "100%", height: "auto" }}
            />
          </Box>
          <Box display="flex" gap={1} mt={2} className="hhhhhyuyguy">
            {images.map((row, i) => (
              <Image
                key={i}
                src={row}
                alt="Project gallery image"
                width={80}
                height={60}
                style={{ borderRadius: 8, objectFit: "cover" }}
              />
            ))}
          </Box>
          <Box display="flex" alignItems="center" gap={2} mt={2}>
            <p
              className="font-[500] text-[13.33px] leading-[20px] tracking-[0%] text-[#303030]"
              style={{ fontFamily: "ScandiaMedium" }}
            >
              Share:
            </p>
            <div className="flex items-center gap-[8px]   gjgbjygbjy">
              <Image src="/Img/linkdin.svg" alt="LinkedIn" width={24} height={24} className="gdvhgs" />
              <Image src="/Img/fb.svg" alt="Facebook" width={24} height={24} className="gdvhgs" />
              <Image src="/Img/x.svg" alt="Twitter" width={24} height={24} className="gdvhgs" />
            </div>
          </Box>
        </Box>

        {/* Right Column - Project Info */}
        <Box flex={1}>
          <p className=" ********************************">
            Rebuilding Children`&apos;`s Hospital in Aleppo
          </p>
          <Box
            display="flex"
            alignItems="center"
            gap={1}
            mt={1}
            className="hvhvhgvhgjv"
          >
            <Avatar
              src="/Img/city.png"
              sx={{ width: "48px", height: "48px" }}
            />
            <div>
              <p
                className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                style={{ fontFamily: "ScandiaMedium" }}
              >
                Rebuild Syria Network
              </p>
              <p
                className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                style={{ fontFamily: "Scandia" }}
              >
                Infrastructure & development
              </p>
            </div>
          </Box>

          {/* open position */}
          <div className="bdkjdhbxjhbdthispos">
            <h1 className="bdkjdhbxjhbdthisposP">Open positions (9)</h1>
            <div className="flex flex-wrap bdkjdhbxjhbdthisposhgvh">
              {jobData.map((job, index) => (
                <div key={index} className="bdkjdhbxjhbdthisposhgvhcard">
                  <Image
                    src={job.icon}
                    alt={`${job.title} icon`}
                    width={24}
                    height={24}
                    className="vhgsvhgvdhsgvhsg"
                  />
                  <h2 className="bjfhdbjdhfb">{job.title}</h2>
                  <p className="hjbdfjhebcvjde">
                    Open positions : {job.positions}
                  </p>
                  <button className="ybshcdbcshbc" style={{cursor:"pointer"}} onClick={() => router.push("/Project/ProjectDetail_Apply_as_volunteer")} >Apply</button>
                </div>
              ))}
            </div>
          </div>
          {/* Details */}
          <Box mt={3}>
            <p
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
              style={{ fontFamily: "ScandiaMedium" }}
            >
              Details
            </p>
            <div className="flex items-center gap-[30px]">
              <div className="flex items-center gap-[8px]">
                <Image src="/Img/loc.svg" alt="Location icon" width={16} height={16} />
                <div>
                  <p
                    className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs"
                    style={{ fontFamily: "Scandia" }}
                  >
                    Location:
                  </p>
                  <p
                    className="font-[400] leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs"
                    style={{ fontFamily: "Scandia" }}
                  >
                    Aleppo, Syria
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-[8px]">
                <Image src="/Img/calendar.svg" alt="Calendar icon" width={16} height={16} />
                <div>
                  <p
                    className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs"
                    style={{ fontFamily: "Scandia" }}
                  >
                    Project timeline:
                  </p>
                  <p
                    className="font-[400]  leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs"
                    style={{ fontFamily: "Scandia" }}
                  >
                    April–June 2025
                  </p>
                </div>
              </div>
            </div>
          </Box>

          {/* About */}
          <Box mt={4}>
            <p
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
              style={{ fontFamily: "ScandiaMedium" }}
            >
              About
            </p>
            <p
              className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mt-[8px] m-[0px]"
              style={{ fontFamily: "Scandia" }}
            >
              {showMore ? (
                <>
                  Millions of Syrians have lost their homes due to war, facing
                  immense challenges in rebuilding their lives. This project
                  aims to provide sustainable housing, education, and healthcare
                  for affected families, ensuring a brighter future for the next
                  generation.{" "}
                  <span
                    style={{ color: "red", cursor: "pointer" }}
                    onClick={() => setShowMore(false)}
                  >
                    show less
                  </span>
                </>
              ) : (
                <>
                  Millions of Syrians have lost their homes due to war...{" "}
                  <span
                    style={{ color: "red", cursor: "pointer" }}
                    onClick={() => setShowMore(true)}
                  >
                    show more
                  </span>
                </>
              )}
            </p>
          </Box>
        </Box>
      </Box>

      <div className=" behdcjehbcard">
        {/* <div></div> */}

        {/* Similar Projects */}

        <Box display="flex" justifyContent="space-between" alignItems="center">
          <p className="bhjdbcjdhbcjhdbPPP">Similar Projects</p>
          <Box>
            <IconButton>
              <Image src="/Img/left.svg" alt="Left arrow" width={24} height={24} className="trweyuassdj" />
            </IconButton>
            <IconButton>
              <Image src="/Img/right.svg" alt="Right arrow" width={24} height={24} className="trweyuassdj" />
            </IconButton>
          </Box>
        </Box>

        <Box
          display="grid"
          gridTemplateColumns={{
            xs: "1fr",
            sm: "repeat(2, 1fr)",
            lg: "repeat(4, 1fr)",
          }}
          gap={2}
        >
          {[
            {
              icon: "/Img/orp3.png",
              title: "Skills for change",
              org: "Women for Change",
              desc: "Vocational training for Syrian women...",
              image: "/Img/projectImg/4.png",
              tags: ["Doctor", "Nurse", "+5 More"],
            },
            {
              icon: "/Img/orp2.png",
              title: "Reforest Syria",
              org: "Green Syria Initiative",
              desc: "Restoring forests and green spaces damaged by war...",
              image: "/Img/projectImg/7.png",
              tags: ["Photographer", "Journalist", "+3 More"],
            },
            {
              icon: "/Img/orp3.png",
              title: "Skills for change",
              org: "Women for Change",
              desc: "Vocational training for Syrian women...",
              image: "/Img/projectImg/8.png",
              tags: ["Security engineer", "+2 More"],
            },
            {
              icon: "/Img/orp3.png",
              title: "Skills for change",
              org: "Women for Change",
              desc: "Vocational training for Syrian women...",
              image: "/Img/projectImg/8.png",
              tags: ["Security engineer", "+2 More"],
            },
          ].map((proj, i) => (
            <Card
              key={i}
              sx={{
                borderRadius: "20px",
                border: "1px solid #0000000A",
                boxShadow: "4px 12px 24px 0px #0000000A",
                padding: "20px",
              }}
            >
              <Image
                src={proj.image}
                alt={proj.title}
                width={400}
                height={300}
                style={{
                  width: "100%",
                  height: 160,
                  objectFit: "cover",
                  borderRadius: "12px",
                }}
              />
              <CardContent sx={{ padding: "16px 0px" }}>
                <div className="vjsghvjhsgjhbjhbv">
                  <Image
                    src={proj?.icon}
                    alt="Organization icon"
                    width={24}
                    height={24}
                  />
                  <p
                    className="font-[500] text-[12px] leading-[15px] tracking-[0%] text-[#000000] m-[0px]"
                    style={{ fontFamily: "ScandiaMedium" }}
                  >
                    {proj.org}
                  </p>
                </div>
                <p
                  className="font-[500] leading-[32px] tracking-[0%] text-[#000000] m-[0px] okjyyaa"
                  style={{ fontFamily: "ScandiaMedium" }}
                >
                  {proj.title}
                </p>
                <p
                  className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mt-[8px] m-[0px]"
                  style={{ fontFamily: "Scandia" }}
                >
                  {proj.desc}
                </p>

                {proj.tags && (
                  <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
                    {proj.tags.map((tag, i) => (
                      <Chip key={i} label={tag} size="small" />
                    ))}
                  </Box>
                )}
              </CardContent>
            </Card>
          ))}
        </Box>
      </div>
     
    </div>
    <div className="sdghfsdhdgsjhsjsalg">
        <div className="retwytqeuwqryeralg">
          <p
            className="font-[500]  leading-[40px] tracking-[0%] text-[#ffffff]  hdskguqzqa"
            style={{ fontFamily: "ScandiaMedium" }}
          >
            Join Us. Shape the Future.
          </p>
          <div className="msuhkqwaidaalg">
            <button
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px] flex items-center justify-center text-center bg-[#ECE3E7] rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
              style={{ fontFamily: "ScandiaMedium" }}
            >
              As an Organization
            </button>
            <button
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-transparent rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
              style={{ fontFamily: "ScandiaMedium", border: "1px solid white" }}
            >
              As an Individual
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}