import type { Config } from "tailwindcss";
import lineClamp from "@tailwindcss/line-clamp";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        scandia: ['Scandia', 'sans-serif'],
        scandiaMedium: ['ScandiaMedium', 'sans-serif'],
        scandiaBold: ['ScandiaBold', 'sans-serif'],
        coheading:['co_headline_regular','sans-serif'],
        coheadingBold:['co_headline_bold', 'sans-serif'],
      },
    },
  },
  plugins: [lineClamp],
};

export default config;
