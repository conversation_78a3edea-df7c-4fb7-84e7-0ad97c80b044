"use client";

// import { ChangeEvent } from "react";
import  { FormData } from './StepsRegistration';

type Props = {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
};


export default function Step2({ formData, setFormData }: Props) {
  
  //  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
  //   const { name, value } = e.target;
  //   setFormData((prev) => ({
  //     ...prev,
  //     [name]: value,
  //   }));
  // };

 

  return (
     <div className="flex flex-col gap-[32px]">
              {/* <TextField
          fullWidth
          label="Email"
          variant="outlined"
          name="email"
          value={formData.email}
          onChange={handleChange}
          className="mb-4"
        /> */}
              <div className="flex justify-between items-center w-full gap-[30px] max-[850px]:flex-col  ">
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030]">
                   Contact person name
                  </p>
                 <input
                 value={formData.contactPersonName}
  onChange={(e) =>
    setFormData(prev => ({ ...prev, contactPersonName: e.target.value }))
  }
                    placeholder="Enter full name"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030]">
                  Phone number
                  </p>
                 <input
                   value={formData.phoneNumber}
  onChange={(e) =>
    setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))
  }
                    placeholder="Enter phone number"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
              </div>
             

             <div className="flex justify-between items-center w-full gap-[30px] max-[850px]:flex-col  ">
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030]">
                  Position/Title
                  </p>
                 <input
                   value={formData.position}
  onChange={(e) =>
    setFormData(prev => ({ ...prev, position: e.target.value }))
  }
                    placeholder="Enter position/title"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030]">
                  Email
                  </p>
                 <input
                   value={formData.contactEmail}
  onChange={(e) =>
    setFormData(prev => ({ ...prev, contactEmail: e.target.value }))
  }
                    placeholder="Enter email "
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
              </div>
            </div>
  );
}
