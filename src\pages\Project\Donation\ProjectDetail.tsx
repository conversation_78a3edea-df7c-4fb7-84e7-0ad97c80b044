"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Card, CardContent, Box, LinearProgress, Avatar, IconButton } from "@mui/material";
import "./ProjectDetail.css"
import { useRouter } from 'next/navigation';
export default function ProjectDetail() {
    const [showMore, setShowMore] = useState(false);
    const router = useRouter();
    const images = ["/Img/Syrian house (1).png", "/Img/construction work.png", "/Img/reconstruct.png", "/Img/rereere.png"];
    const handleApplyNowClick = () => {
        router.push("/Project/PDmakeDonation");
      };
    return (
        <Box mt={15} >
            <Box className="dscbuwbanue">
                {/* Breadcrumb */}
                <p
                    className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31] mb-[24px] flex items-center gap-1  dhakghghsfs"
                    style={{ fontFamily: "ScandiaMedium" }}
                >
                    <span>Project</span>
                    <Image src="/Img/arrow.svg" alt="arrow" width={16} height={16} />
                    <span>Rebuilding Children{"'"}s Hospital in Aleppo</span>
                </p>

                <Box display="flex" flexDirection={{ xs: "column", lg: "row" }} mt={3} gap={4}>
                    {/* Left Column - Image Gallery */}
                    <Box flex={1}>
                        <Box borderRadius={4} overflow="hidden">
                            <Image
                                src="/Img/Syrian house.png"
                                alt="Main Project"
                                width={600}
                                height={400}
                                style={{ width: "100%", height: "auto" }}
                            />
                        </Box>
                        <Box display="flex" gap={1} mt={2} flexWrap="wrap">
                            {images.map((row, i) => (
                                <Image
                                    key={i}
                                    src={row}
                                    alt="Project thumbnail"
                                    width={80}
                                    height={60}
                                    style={{ borderRadius: 8, objectFit: "cover" }}
                                />
                            ))}
                        </Box>
                        <Box display="flex" alignItems="center" gap={2} mt={2}>
                            <p
                                className="font-[500] text-[13.33px] leading-[20px] tracking-[0%] text-[#303030]"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Share:</p>
                            <div className="flex items-center gap-[8px]">
                                <Image src="/Img/linkdin.svg" alt="LinkedIn" width={24} height={24} />
                                <Image src="/Img/fb.svg" alt="Facebook" width={24} height={24} />
                                <Image src="/Img/x.svg" alt="Twitter" width={24} height={24} />
                            </div>
                        </Box>
                    </Box>


                    <Box flex={1}>
                        <p
                            className="font-[500] leading-[20px] tracking-[0%] text-[#181916] sdfggsdfdhsgasdfhasdja"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >Homes for Hope</p>
                        <Box display="flex" alignItems="center" gap={1} mt={1}>
                            <Avatar src="/Img/city.png" sx={{ width: "48px", height: "48px" }} />
                            <div >
                                <p
                                    className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Rebuild Syria Network</p>
                                <p
                                    className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                                    style={{ fontFamily: "Scandia" }}
                                >Infrastructure & development</p>
                            </div>
                        </Box>

                        {/* Fundraising */}
                        <Box mt={3} p={3} borderRadius={4} border="1px solid #ECE7E3">
                            <Box display="flex" justifyContent="space-between">
                                <Box>
                                    <p
                                        className="font-[400] text-[16px] leading-[20px] tracking-[0%] text-[#303030] m-[0px] fhdweryjfuyw"
                                        style={{ fontFamily: "Scandia" }}
                                    >Raised</p>
                                    <p
                                        className="font-[500] text-[22px] leading-[22px] tracking-[0%] text-[#000000] m-[0px] okjyyaa"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >125,000 USD</p>
                                </Box>
                                <Box>
                                    <p
                                        className="font-[400] text-[16px] leading-[20px] tracking-[0%] text-[#303030] m-[0px] fhdweryjfuyw"
                                        style={{ fontFamily: "Scandia" }}
                                    >Fundraising Goal</p>
                                    <p
                                        className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px] fhdweryjfuyw"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >500,000 USD</p>
                                </Box>
                            </Box>
                            <LinearProgress
                                variant="determinate"
                                value={25}
                                sx={{
                                    mt: 2,
                                    height: 12,
                                    borderRadius: 5,
                                    backgroundColor: '#D9D3CD',
                                    '& .MuiLinearProgress-bar': {
                                        backgroundColor: '#2F3B31',
                                    },
                                }}
                            />
                            <button onClick={handleApplyNowClick}
                                className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-[#D31B1B] rounded-[80px] border-0 w-full h-[49px] mt-[24px] cursor-pointer"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Donate now</button>
                            <Box display="flex" alignItems="center" justifyContent="center" gap="8px" mt={1} >
                                <Image src="/Img/ipay.svg" alt="Apple Pay" width={32} height={20} style={{ opacity: "0.6" }} />
                                <Image src="/Img/gpay.svg" alt="Google Pay" width={32} height={20} style={{ opacity: "0.6" }} />
                                <Image src="/Img/spay.svg" alt="Samsung Pay" width={32} height={20} style={{ opacity: "0.6" }} />
                                <Image src="/Img/visa.svg" alt="Visa" width={32} height={20} style={{ opacity: "0.6" }} />
                                <Image src="/Img/mastercrd.svg" alt="Mastercard" width={32} height={20} style={{ opacity: "0.6" }} />
                            </Box>
                        </Box>
                        {/* Details */}
                        <Box mt={3}>
                            <p
                                className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Details</p>
                            <div className="flex items-center gap-[30px]">
                                <div className="flex items-center gap-[8px]">
                                    <Image src="/Img/loc.svg" alt="Location" width={16} height={16} />
                                    <div>
                                        <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Location:</p>
                                        <p className="font-[400] leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Aleppo, Syria</p>
                                    </div>
                                </div>
                                <div className="flex items-center gap-[8px]">
                                    <Image src="/Img/calendar.svg" alt="Calendar" width={16} height={16} />
                                    <div>
                                        <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Project timeline:</p>
                                        <p className="font-[400]  leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>April–June 2025</p>
                                    </div>
                                </div>
                            </div>

                        </Box>

                        {/* About */}
                        <Box mt={4}>
                            <p className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                                style={{ fontFamily: "ScandiaMedium" }}>About</p>
                            <p className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mt-[8px] m-[0px]" style={{ fontFamily: "Scandia" }}>
                                {showMore
                                    ? (
                                        <>
                                            Millions of Syrians have lost their homes due to war, facing immense challenges in rebuilding their lives. This project aims to provide sustainable housing, education, and healthcare for affected families, ensuring a brighter future for the next generation. <span style={{ color: 'red', cursor: 'pointer' }} onClick={() => setShowMore(false)}>show less</span>
                                        </>
                                    )
                                    : (
                                        <>
                                            Millions of Syrians have lost their homes due to war... <span style={{ color: 'red', cursor: 'pointer' }} onClick={() => setShowMore(true)}>show more</span>
                                        </>
                                    )
                                }
                            </p>
                        </Box>
                    </Box>
                </Box>
                {/* Similar Projects */}
                <Box mt={8}>
                    <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                        <p
                            className="font-[500]  leading-[32px] tracking-[0%] text-[#181916] dghdfgaghghsdadgh"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >Similar Projects</p>
                        <Box>
                            <IconButton><Image src="/Img/left.svg" alt="Previous" width={24} height={24} className="trweyuassdj" /></IconButton>
                            <IconButton><Image src="/Img/right.svg" alt="Next" width={24} height={24} className="trweyuassdj" /></IconButton>
                        </Box>
                    </Box>

                    <Box display="grid" gridTemplateColumns={{ xs: '1fr', sm: 'repeat(2, 1fr)', lg: 'repeat(4, 1fr)' }} gap={2}>
                        {[
                            {
                                icon: "/Img/orp1.png",
                                title: "Safe haven for orphans",
                                org: "Syrian Orphan Charity",
                                desc: "Building a secure shelter for Syrian orphans...",
                                goal: "25,000 USD",
                                image: "/Img/build1.png",
                            },
                            {
                                icon: "/Img/orp1.png",
                                title: "Schools of Tomorrow",
                                org: "Syrian Orphan Charity",
                                desc: "Establishing schools with essential learning materials...",
                                goal: "400,000 USD",
                                image: "/Img/build 2.png",
                            },
                            {
                                icon: "/Img/orp2.png",
                                title: "Reforest Syria",
                                org: "Green Syria Initiative",
                                desc: "Restoring forests and green spaces damaged by war...",
                                goal: "200,000 USD",
                                image: "/Img/build3.png",
                            },
                            {
                                icon: "/Img/orp3.png",
                                title: "Skills for change",
                                org: "Women for Change",
                                desc: "Vocational training for Syrian women...",
                                goal: "150,000 USD",
                                image: "/Img/build4.png",
                            },
                        ].map((proj, i) => (
                            <Card key={i} sx={{ borderRadius: "20px", border: "1px solid #0000000A", boxShadow: "4px 12px 24px 0px #0000000A", padding: "20px" }}>
                                <Image
                                    src={proj.image}
                                    alt={proj.title}
                                    width={400}
                                    height={300}
                                    style={{ width: "100%", height: 160, objectFit: "cover", borderRadius: "12px" }}
                                />
                                <CardContent sx={{ padding: "16px 0px" }}>
                                    <div>
                                        <Image src={proj.icon} alt="Organization icon" width={24} height={24} />
                                        <p
                                            className="font-[500] text-[12px] leading-[15px] tracking-[0%] text-[#000000] m-[0px]"
                                            style={{ fontFamily: "ScandiaMedium" }}
                                        >{proj.org}</p>
                                    </div>
                                    <p className="font-[500] leading-[32px] tracking-[0%] text-[#000000] m-[0px] okjyyaa"
                                        style={{ fontFamily: "ScandiaMedium" }}>{proj.title}</p>
                                    <p className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mt-[8px] m-[0px]" style={{ fontFamily: "Scandia" }}>{proj.desc}</p>
                                    <Box className="flex  flex-col items-start bg-[#ECE7E3] rounded-[12px] py-[10px] px-[16px] mt-[16px]">
                                        <div className="flex items-center justify-between w-full">
                                            <p
                                                className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#181916]  m-[0px]" style={{ fontFamily: "Scandia" }}
                                            >Fundraising goal</p>
                                            <p
                                                className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px]"
                                                style={{ fontFamily: "ScandiaMedium" }}
                                            >{proj.goal}</p>
                                        </div>
                                        <LinearProgress
                                            variant="determinate"
                                            value={25}
                                            sx={{
                                                mt: 2,
                                                height: 12,
                                                borderRadius: 5,
                                                width: "100%",
                                                backgroundColor: '#D9D3CD',
                                                '& .MuiLinearProgress-bar': {
                                                    backgroundColor: '#2F3B31',
                                                },
                                            }}
                                        />

                                    </Box>
                                </CardContent>
                            </Card>
                        ))}
                    </Box>
                </Box>


            </Box >
            <div className="sdghfsdhdgsjhsjs">

                <div className="retwytqeuwqryer">
                    <p className="font-[500]  leading-[40px] tracking-[0%] text-[#ffffff]  hdskguqzqa"
                        style={{ fontFamily: "ScandiaMedium" }}>
                        Join Us. Shape the Future.
                    </p>
                    <div className="msuhkqwaida">
                        <button className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px] flex items-center justify-center text-center bg-[#ECE3E7] rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
                            style={{ fontFamily: "ScandiaMedium" }}>
                            As an Organization
                        </button>
                        <button className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-transparent rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
                            style={{ fontFamily: "ScandiaMedium", border: "1px solid white" }}>
                            As an Individual
                        </button>
                    </div>
                </div>

            </div>
        </Box>
    )
}