import React,{useState} from "react";
import Image from "next/image";
import {
  Accordion,
  AccordionSummary,
  Typography,
  
} from "@mui/material";
import "./StoryTeller.css";
import  SideMenu  from "@/pages/quiz/components/SideMenu";

function StoryTeller() {
  const [activeChapter, setActiveChapter] = useState(0);
  const handleChapterChange = (index: number) => {
    setActiveChapter(index);
  };
  const nextChapters = [
    {
      image: "/Images/coursedetail/videoImage.svg",
      chapter: "Chapter 2",
      title: "Building a compelling narrative",
      duration: "12:23",
    },
    {
      image: "/Images/coursedetail/videoImage.svg",
      chapter: "Chapter 3",
      title: "Delivering your story for maximum impact",
      duration: "09:46",
    },
  ];

  const sections = [
    {
      title: "Developing a fundraising strategy",
    },
    {
      title: "Donor engagement and stewardship",
    },
    {
      title: "Fundraising channels and tools",
    },
  ];
  return (
    <div className="storyteller__main__container__1">
      <SideMenu
        activeChapter={activeChapter}
        setActive={handleChapterChange}
        chapters={[
          {
            title: "Introduction to fundraising",
            locked: false,
          },
          {
            title: "Developing a fundraising strategy",
            locked: false,
          },
          {
            title: "Donor engagement and stewardship",
            locked: true,
          },
           {
            title: "Fundraising channels and tools",
            locked: true,
          },
        ]}
      />
      <div className="storyteller__main__container__2">
        <p className="storyteller__main__heading_text">
          Storytelling for Social Impact
        </p>
        <div className="storyteller__main__continer_inner">
          <p className="storyteller__main__continer_inner__text">
            Academic framework
          </p>
          <p className="storyteller__main__continer_inner__text__2">
            Explore the significance of fundraising in driving support for
            various organizations and causes. Delve into the ethical aspects and
            the necessity for transparency in fundraising efforts. Gain insights
            into what motivates donors to contribute and how to effectively
            engage them.
          </p>
        </div>
        <div className="storyteller__main__continer_inner_1">
          <div className="storyteller__main_inner_1__heaing_container">
            <p className="storyteller__main_inner_1__heading">
              Introduction to fundraising
            </p>
            <p className="storyteller__main_inner_1__text">
              Crafting Narratives that Inspire and Mobilize Master the art of
              storytelling to connect emotionally with supporters and convey
              your project&apos;s mission powerfully.
            </p>
          </div>
          <div className="storyteller__main_inner_1__border__container"></div>
          <div className="storyteller__main_inner_1__chapter_container">
            <p className="storyteller__main_inner_1__chapter">Chapter 1</p>
            <p className="storyteller__main_inner_1__heading">
              The power of storytelling in social impact
            </p>
            <p className="storyteller__main_inner_1__text">
              Explore why storytelling is essential for advocacy, fundraising,
              and community engagement. Learn how stories create emotional
              connections and drive action.
            </p>
          </div>
          <div className="course__detail__card__preview__video__wrapper">
            <video
              src="/Images/coursedetail/video.mp4"
              width={416}
              height={280}
              className="course__detail__card__preview__video1"
              controls
              poster="/Images/coursedetail/preview.jpg"
            >
              Your browser does not support the video tag.
            </video>
          </div>
          <div className="storyteller__main_inner_1__quiz__container">
            <p className="storyteller__main_inner_1__quiz">Quiz</p>
            <div className="storyteller__main_inner_1__quiz__container22">
              <div className="storyteller__main_inner_1__quiz__container22__inner">
                <Image
                  src="/Images/coursedetail/quizIcon.svg"
                  width={20}
                  height={20}
                  alt="quizIcon"
                />
                <p className="storyteller__main_inner_1__quiz__container22__inner__text">
                  Take quiz chapter one
                </p>
              </div>
              <Image
                src="/Images/coursedetail/arrowquiz.svg"
                width={20}
                height={20}
                alt="arrowquiz"
              />
            </div>
          </div>
          <div className="storyteller__whatnext__container">
            <p className="storyteller__whatnext__container__text">
              What&apos;s next?
            </p>
            <div className="storyteller__whatnext__container__inner__container_top">
              {nextChapters.map((chapter, index) => (
                <div
                  key={index}
                  className="storyteller__whatnext__container__inner"
                >
                  <Image
                    className="storyteller__whatnext__container__inner__image"
                    src={chapter.image}
                    width={208}
                    height={141}
                    alt="videoImage"
                  />
                  <p className="storyteller__whatnext__container__inner__text">
                    {chapter.chapter}
                  </p>
                  <p className="storyteller__whatnext__container__inner__text2">
                    {chapter.title}
                  </p>
                  <p className="storyteller__whatnext__container__inner__text3">
                    {chapter.duration}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="course__detail__accordion__wrapper1">
          {sections.map((section, idx) => (
            <Accordion
              key={idx}
              sx={{
                backgroundColor: "white",
                padding: { xs: "16px 15px", sm: "24px 32px" },
                border: "1px solidrgba(6, 6, 6, 0.04)",
                borderRadius: "20px",
                boxShadow: "4px 12px 24px 0px #0000000A",
                "&.MuiAccordion-root:first-of-type": {
                  borderTopLeftRadius: "20px",
                  borderTopRightRadius: "20px",
                },
                "&.MuiAccordion-root:last-of-type": {
                  borderBottomLeftRadius: "20px",
                  borderBottomRightRadius: "20px",
                },
                "&:before": {
                  display: "none",
                },
                "& .MuiAccordionSummary-root": {
                  padding: 0,
                  minHeight: "unset",
                },
                "& .MuiAccordionSummary-content": {
                  margin: 0,
                },
                "& .MuiAccordionDetails-root": {
                  padding: "16px 0",
                },
              }}
            >
              <AccordionSummary
                expandIcon={
                  <Image
                    src="/Images/coursedetail/expandmore.svg"
                    width={20}
                    height={20}
                    alt="expand icon"
                  />
                }
              >
                <Typography
                  sx={{
                    fontFamily: "ScandiaMedium",
                    fontWeight: 500,
                    fontSize: {
                      xs: "13px",
                      sm: "16px",
                    },
                    lineHeight: "24px",
                    verticalAlign: "middle",
                    color: "#303030",
                  }}
                >
                  {section.title}
                </Typography>
              </AccordionSummary>
            </Accordion>
          ))}
        </div>
      </div>
    </div>
  );
}

export default StoryTeller;
