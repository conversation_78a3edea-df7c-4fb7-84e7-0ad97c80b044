"use client";

import { useState } from "react";
import Image from "next/image";
import { MenuItem, Select, FormControl } from "@mui/material";
import Step2 from "./Step2";
import Step3 from "./Step3";
import Step4 from "./Step4";
import Step5 from "./Step5";
import { organizerRegister, uploadImage } from "@/services/redux/middleware/register";
import { AppDispatch } from "../../services/redux/store";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
export type FormData = {
  email: string;
  country: string;
  organizationName: string;
  OrganizationWebsite: string;
  arabicorganizationName: string; // PascalCase key
  license: string | null; // Changed from null to File | null
  contactPersonName: string;
  phoneNumber: string;
  position: string;
  contactEmail: string;
  goodGovernance: string; // "yes" | "no"
  transparencyReporting: string; // "yes" | "no"
  sustainableFunding: string; // "yes" | "no"
  impactMeasurement: string;
  profileImage: string;
  biodata: string;
  acceptedTerms: boolean;
  privacyPolicy: boolean;
  tags: string[];
};

export default function MultiStepRegistration() {
  const dispatch = useDispatch<AppDispatch>();
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;

  const stepTitles = [
    "Organization details",
    "Contact person",
    "Governance & accountability",
    "Organization bio",
  ];
  const countries = [
    "United States",
    "Canada",
    "United Kingdom",
    "Germany",
    "France",
  ];
  const [formData, setFormData] = useState<FormData>(() => {
    // Get stored data from localStorage
    const storedData = localStorage.getItem('registerData');
    const parsedData = storedData ? JSON.parse(storedData) : null;
    
    return {
      email: parsedData?.email || "",
      country: "",
      organizationName: parsedData?.organizationName || "",
      OrganizationWebsite: "",
      arabicorganizationName: "",
      license: "",
      contactPersonName: "",
      phoneNumber: "",
      position: "",
      contactEmail: "",
      goodGovernance: "",
      transparencyReporting: "",
      sustainableFunding: "",
      impactMeasurement: "",
      profileImage: "",
      biodata: "",
      acceptedTerms: false,
      privacyPolicy: false,
      tags: [],
    };
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [open, setOpen] = useState(false);

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (currentStep === 1) {
      if (
        !formData.country ||
        !formData.organizationName ||
        !formData.OrganizationWebsite ||
        !formData.arabicorganizationName ||
        !selectedFile
      ) {
        alert("Please fill in all fields in Organization details");
        return;
      }
    } else if (currentStep === 2) {
      if (
        !formData.contactPersonName ||
        !formData.phoneNumber ||
        !formData.position ||
        !formData.contactEmail
      ) {
        alert("Please fill in all fields in Contact person");
        return;
      }
    } else if (currentStep === 3) {
      if (
        !formData.goodGovernance ||
        !formData.transparencyReporting ||
        !formData.sustainableFunding ||
        !formData.impactMeasurement
      ) {
        alert("Please fill in all fields in Governance & accountability");
        return;
      }
    } else if (currentStep === 4) {
      if (
        !formData.profileImage ||
        !formData.biodata ||
        !formData.acceptedTerms ||
        !formData.privacyPolicy
      ) {
        alert("Please fill in all fields in Organization bio");
        return;
      }
      
      // Prepare the data for API submission
      const apiData = {
        email: formData.email,
        organizationName: formData.organizationName,
        organizationNameArabic: formData.arabicorganizationName,
        country: formData.country,
        website: formData.OrganizationWebsite,
        contactName: formData.contactPersonName,
        contactPhone: formData.phoneNumber,
        contactTitle: formData.position,
        contactEmail: formData.contactEmail,
        goodGovernance: formData.goodGovernance === "yes",
        transparencyReporting: formData.transparencyReporting === "yes",
        sustainableFunding: formData.sustainableFunding === "yes",
        impactMeasurement: formData.impactMeasurement === "yes", 
        shortBio: formData.biodata,
        organizationImage: formData.profileImage,
        organizationTags: formData.tags // Send as array directly
      };
      
      console.log("Sending registration data:", apiData);
      
      // Dispatch the API call
      dispatch(organizerRegister(apiData))
        .then((res) => {
          console.log("Registration API response:", res);
          if (res?.payload?.statusCode === 200) {
            localStorage.setItem("OrganizerData", JSON.stringify(res?.payload?.data))
            if (res?.payload?.message === "User already has an organization") {
              toast.error("You already have an organization registered");
            } else {
              toast.success("Registration successful!");
              nextStep(); 
            }
          } else {
            toast.error(res?.payload?.message || "Registration failed. Please try again.");
            console.error("Registration failed with response:", res);
          }
        })
        .catch((error) => {
          console.error("Registration error:", error);
          toast.error(error?.message || "An error occurred during registration.");
        });
      
      return; // Don't proceed to nextStep yet - wait for API response
    }

    // Log the current form data to see what's being sent
    console.log(`Step ${currentStep} data:`, formData);

    // For steps 1-3, just move to the next step
    nextStep();
  };
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      const validTypes = ["image/jpeg", "application/pdf"];
      if (!validTypes.includes(file.type)) {
        alert("Only JPG/JPEG or PDF files are allowed");
        return;
      }

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        alert("File size exceeds 2MB limit");
        return;
      }

      setSelectedFile(file);

      // Wrap the file in FormData with key 'photo'
      const formData = new FormData();
      formData.append("photo", file);

      dispatch(uploadImage(formData)).then((res) => {
        if (res?.payload?.data?.url) {
          console.log("Uploaded file URL:", res.payload.data.url);
          setFormData((prev) => ({
            ...prev,
            license: res.payload.data.url,
          }));
          toast.success("File uploaded successfully");
        } else {
          toast.error("Failed to upload file.");
        }
      });
    }
  };

  return (
    <div className="w-full flex justify-center items-center pt-[54px]">
      <div className="flex flex-col max-w-[900px] w-[100%] gap-[64px] max-[768px]:gap-[40px]  ">
        {currentStep !== 5 && (
          <div className="flex flex-col gap-[24px]">
            {currentStep > 1 && (
              <div
                className="flex gap-[6px] items-center cursor-pointer"
                onClick={prevStep}
              >
                <Image
                  src="/Images/registration/backArrow.svg"
                  alt="backArrow"
                  width={20}
                  height={20}
                />
                <p className="font-[scandiaMedium] font-medium text-[12px] leading-[16px] text-[#181916]">
                  Back
                </p>
              </div>
            )}
            <h1 className="font-[scandiaMedium] font-medium text-[22px] sm:text-[32px] leading-[32px] sm:leading-[40px] text-[#181916]">
              Register
            </h1>
            <div className="overflow-x-auto scrollbar-hide w-full">
              <div className="flex gap-[8px] w-max">
                {stepTitles.map((title, index) => {
                  const isCompleted = currentStep > index + 1;
                  const isCurrent = currentStep === index + 1;

                  return (
                    <div
                      key={index}
                      className={`flex gap-[6px] py-[8px] px-[12px] rounded-[80px] items-center justify-center whitespace-nowrap ${
                        isCurrent
                          ? "bg-[#2F3B31] text-white"
                          : "bg-white text-[#CFCFCF]"
                      }`}
                      style={{
                        border: isCompleted
                          ? "1px solid #303030"
                          : isCurrent
                          ? "none"
                          : "1px solid #CFCFCF",
                      }}
                    >
                      <Image
                        src={
                          isCompleted
                            ? "/Images/registration/greenTick.svg"
                            : isCurrent
                            ? "/Images/registration/whitetick.svg"
                            : "/Images/registration/graytick.svg"
                        }
                        alt="tick"
                        width={16}
                        height={16}
                      />
                      <div
                        className={`font-[scandiaMedium] font-medium text-[12px] leading-[16px] ${
                          isCompleted
                            ? "text-[#303030]"
                            : isCurrent
                            ? "text-[#FFFFFF]"
                            : "text-[#CFCFCF]"
                        }`}
                      >
                        {index + 1}. {title}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Progress Steps */}

        <form onSubmit={handleSubmit}>
          {/* Step 1: Account Information */}
          {currentStep === 1 && (
            <div className="flex flex-col gap-[32px]">
              <div className="flex justify-between items-center w-full gap-[30px] max-[850px]:flex-col  ">
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#CFCFCF]">
                    Email
                  </p>
                  <input
                    value={formData.email}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        email: e.target.value,
                      }))
                    }
                    className="w-full bg-transparent font-[scandia] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0 text-[#181916] cursor-text"
                  />
                </div>
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none ">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
                    Country
                  </p>
                  <FormControl
                    fullWidth
                    variant="standard"
                    sx={{
                      "& .MuiInputBase-root": {
                        fontFamily: formData.country
                          ? "ScandiaMedium"
                          : "Scandia",
                        color: formData.country ? "#232323" : "#CFCFCF",
                        cursor: "pointer",
                        fontSize: "14px",
                        lineHeight: "20px",
                      },
                      "& .MuiInput-underline:before": {
                        borderBottom: "1.5px solid #DEDEDE",
                      },
                      "& .MuiInput-underline:hover:before": {
                        borderBottom: "1.5px solid #DEDEDE",
                      },
                      "& .MuiInput-underline:after": {
                        borderBottom: "1.5px solid transparent",
                      },
                    }}
                  >
                    <Select
                      value={formData.country}
                      onChange={(e) =>
                        setFormData((prev) => ({
                          ...prev,
                          country: e.target.value,
                        }))
                      }
                      onOpen={() => setOpen(true)}
                      onClose={() => setOpen(false)}
                      displayEmpty
                      IconComponent={() => null} // remove default arrow
                      inputProps={{ "aria-label": "Select country" }}
                      MenuProps={{
                        PaperProps: {
                          elevation: 0,
                          sx: {
                            fontFamily: "ScandiaMedium",
                            boxShadow: "none",
                            "& .MuiMenuItem-root.Mui-selected": {
                              backgroundColor: "transparent !important",
                              fontFamily: "Scandia !important",
                            },
                          },
                        },
                      }}
                      sx={{
                        "& .MuiSelect-select": {
                          paddingRight: "30px", // space for custom icon
                        },
                        position: "relative",
                      }}
                    >
                      <MenuItem
                        value=""
                        disabled
                        sx={{ fontFamily: "ScandiaMedium" }}
                      >
                        Select country
                      </MenuItem>
                      {countries.map((name) => (
                        <MenuItem
                          key={name}
                          value={name}
                          sx={{
                            fontFamily: "Scandia",
                            fontSize: "14px",
                            lineHeight: "20px",
                            color: "#181916",
                            padding: "8px",
                            "&.Mui-selected": {
                              backgroundColor: "transparent !important",
                              fontFamily: "ScandiaMedium",
                            },
                            "&.Mui-selected:hover": {
                              backgroundColor: "transparent !important",
                            },
                            "&:focus": {
                              backgroundColor: "transparent !important",
                            },
                            "& .MuiTouchRipple-root": {
                              display: "none !important",
                            },
                          }}
                        >
                          {name}
                        </MenuItem>
                      ))}
                    </Select>

                    {/* Custom arrow icon */}
                    <div
                      style={{
                        position: "absolute",
                        right: "8px",
                        top: "50%",
                        transform: `translateY(-50%) rotate(${
                          open ? 180 : 0
                        }deg)`,
                        transition: "transform 0.3s ease",
                        pointerEvents: "none",
                      }}
                    >
                      <Image
                        src="/Images/registration/arrow-down.svg"
                        alt="arrow"
                        width={20}
                        height={20}
                      />
                    </div>
                  </FormControl>
                </div>
              </div>
              <div className="flex justify-between items-center w-full gap-[30px] max-[850px]:flex-col ">
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
                    Organization name
                  </p>
                  <input
                    value={formData.organizationName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        organizationName: e.target.value,
                      }))
                    }
                    placeholder="Enter organization name"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
                    Organization website
                  </p>
                  <input
                    value={formData.OrganizationWebsite}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        OrganizationWebsite: e.target.value,
                      }))
                    }
                    placeholder="Enter organization website"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
              </div>

              <div className="flex justify-between items-center w-full gap-[30px] max-[850px]:flex-col items-start">
                <div className="flex flex-col gap-[8px] justify-between w-full max-w-[400px] max-[850px]:max-w-none ">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
                    Organization name (Arabic)
                  </p>
                  <input
                    dir="rtl"
                    value={formData.arabicorganizationName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        arabicorganizationName: e.target.value,
                      }))
                    }
                    placeholder="أدخل اسم المنظمة هنا"
                    className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0  text-[#181916] cursor-default placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
                  />
                </div>
                <div className="flex flex-col gap-[8px] w-full max-w-[400px] max-[850px]:max-w-none">
                  <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
                    Organization license
                  </p>
                  <div className="inline-block relative">
                    <input
                      type="file"
                      id="organizationLicense"
                      accept=".jpg,.jpeg,.pdf"
                      onChange={handleFileChange}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <div className="flex items-center justify-between border-b-[1.5px] border-[#DEDEDE]">
                      <span
                        className={`font-[scandiaMedium] text-[14px] ${
                          selectedFile ? "text-[#232323]" : "text-[#D31B1B]"
                        } truncate max-w-[300px]`}
                      >
                        {selectedFile ? selectedFile.name : "Upload license"}
                      </span>
                      <Image
                        src="/Images/registration/upload-05.svg"
                        alt="upload"
                        width={20}
                        height={20}
                      />
                    </div>
                  </div>

                  <p className="font-[scandia] text-[12px] leading-[16px] text-[#CFCFCF]">
                    .jpg or .pdf with maximum size 2MB
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Step 2: Personal Information */}
          {currentStep === 2 && (
            <Step2 formData={formData} setFormData={setFormData} />
          )}

          {currentStep === 3 && (
            <Step3 formData={formData} setFormData={setFormData} />
          )}

          {currentStep === 4 && (
            <Step4 formData={formData} setFormData={setFormData} />
          )}

          {currentStep === 5 && <Step5 />}

          {currentStep !== 5 && (
            <button
              type="submit"
              className="bg-[#D31B1B] cursor-pointer w-fit rounded-[80px] py-[16px] px-[24px] border-none text-[#FFFFFF] font-[scandiaMedium] text-[14px] leading-[20px]"
              style={{
                marginTop: currentStep === 4 ? "24px" : "60px",
              }}
            >
              Continue
            </button>
          )}
        </form>
      </div>
    </div>
  );
}
