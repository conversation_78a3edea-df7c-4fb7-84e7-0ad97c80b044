import Button from "@/components/button/Button";
import Image from "next/image";
import React from "react";

function ConfrimPassMob() {
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
        <div className="flex flex-col items-center gap-[24px]">
          <div>
            <Image
              src="/Images/signin/elements.svg"
              alt="lock"
              width={40}
              height={40}
            />
          </div>
          <div className="flex flex-col gap-[8px]">
            <p className="text-[22px] max-[370px]:text-[20px] max-[370px]:leading-[30px] leading-[32px] font-[scandiaMedium] leading-[30px] text-[black] text-center">
              New password has been successfully created
            </p>
            <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
              You can now access your account and start contributing.
            </p>
          </div>
          <div className="flex flex-col gap-[8px]">
            <Button>
              <p className="text-[14px] font-[scandiaMedium] text-[white]">
                Sign in
              </p>
            </Button>
          </div>
        </div>
      </div>
      
    </div>
  );
}

export default ConfrimPassMob;
