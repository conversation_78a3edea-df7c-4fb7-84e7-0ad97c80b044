"use client";
import {
  FormControl,
  MenuItem,
  Select,
  SelectChangeEvent,
} from "@mui/material";
import Image from "next/image";
import React from "react";

interface Chapter {
  title: string;
  locked: boolean;
}

interface SideMenuProps {
  chapters: Chapter[];
  activeChapter: number;
  setActive: (index: number) => void;
}

const SideMenu = ({
  chapters,
  activeChapter,
  setActive,
}: SideMenuProps): React.ReactElement => {
  // Correct event type from MUI
  const handleChange = (event: SelectChangeEvent<number>) => {
    // Convert string value to number
    const selectedIndex = Number(event.target.value);

    // Ensure chapters array exists and selectedIndex is valid
    if (!chapters || !Array.isArray(chapters) || chapters.length === 0) {
      return;
    }

    // Ensure the index is within bounds and the chapter exists
    if (isNaN(selectedIndex) || selectedIndex < 0 || selectedIndex >= chapters.length || !chapters[selectedIndex]) {
      return;
    }

    if (!chapters[selectedIndex].locked) {
      setActive(selectedIndex);
    }
  };

  return (
    <>
      <div className="block md:hidden w-full">
        <FormControl fullWidth>
          <Select
            value={activeChapter}
            onChange={handleChange}
            renderValue={(selected) => {
              // Handle undefined, null, or invalid selected values during SSR
              if (selected === undefined || selected === null) {
                return "Select Chapter";
              }

              // Ensure chapters array exists and is not empty
              if (!chapters || !Array.isArray(chapters) || chapters.length === 0) {
                return "Select Chapter";
              }

              const selectedIndex = Number(selected);

              // Handle NaN case (when Number() fails)
              if (isNaN(selectedIndex)) {
                return "Select Chapter";
              }

              // Ensure the index is within bounds and the chapter exists
              if (selectedIndex < 0 || selectedIndex >= chapters.length || !chapters[selectedIndex]) {
                return "Select Chapter";
              }

              return chapters[selectedIndex].title ?? "Select Chapter";
            }}
            displayEmpty
            fullWidth
            MenuProps={{
              PaperProps: {
                sx: {
                  mt: "8px", // gap between dropdown and menu
                  borderRadius: "12px",
                  padding: "12px",
                  width: "100%", // match Select width
                  "& .MuiMenuItem-root": {
                    fontFamily: "ScandiaMedium",
                    fontSize: "16px",
                    textWrap: "wrap",
                    color: "#181916",
                    borderRadius: "100px",
                    padding: "12px",
                    display: "flex",
                    gap: "8px",
                    backgroundColor: "transparent",
                    "&.Mui-selected": {
                      backgroundColor: "#2F3B31",
                      color: "#fff",
                    },
                    "&.Mui-selected:hover": {
                      backgroundColor: "#2F3B31",
                    },
                    "&:hover": {
                      backgroundColor: "#f3f3f3",
                    },
                  },
                },
              },
              anchorOrigin: {
                vertical: "bottom",
                horizontal: "left",
              },
              transformOrigin: {
                vertical: "top",
                horizontal: "left",
              },
            }}
            sx={{
              borderRadius: "100px",
              backgroundColor: "#fff",
              fontFamily: "ScandiaMedium",
              fontSize: "16px",
              padding: "6px 16px",
              display: "flex",
              "& .MuiSelect-select": {
                padding: "10px 16px",
                borderRadius: "100px",
                display: "flex",
              },
            }}
          >
            {chapters && Array.isArray(chapters) &&
              chapters.length > 0 &&
              chapters.map((chapter, index) => {
                const isActive = index === activeChapter;

                return (
                  <MenuItem
                    key={index}
                    value={index}
                    disabled={chapter.locked}
                    sx={{
                      opacity: chapter.locked ? 0.5 : 1,
                      display: "flex",
                      alignItems: "center",
                      gap: "8px",
                    }}
                  >
                    <Image
                      src={
                        chapter.locked
                          ? "/Images/quiz/locked.png"
                          : isActive
                          ? "/Images/quiz/selectedChapter.png"
                          : "/Images/quiz/Chapter.png"
                      }
                      alt="icon"
                      height={16}
                      width={16}
                    />
                    {chapter.title}
                  </MenuItem>
                );
              })}
          </Select>
        </FormControl>
      </div>

      <div className="w-full hidden md:flex flex-col p-8 rounded-[20px] bg-white gap-8 max-w-full md:max-w-[328px] h-fit">
        <p className="font-[ScandiaMedium] text-[16px] md:text-[22px] text-[#181916]">
          Table of Contents
        </p>
        <div className="flex flex-col gap-2">
          {chapters && chapters.map((chapter, index) => {
            const isActive = index === activeChapter;

            return (
              <div
                key={index}
                onClick={() => !chapter.locked && setActive(index)}
                className={`flex items-center gap-3 px-6 py-4 transition-colors duration-200 rounded-full
                  ${
                    chapter.locked
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:opacity-90 cursor-pointer"
                  }
                  ${
                    isActive
                      ? "bg-[#2F3B31] text-white rounded-full"
                      : "text-[#181916] bg-transparent"
                  }`}
              >
                <div className="h-[18px] lg:h-[24px] w-[18px] lg:w-[24px] relative">
                  <Image
                    src={
                      chapter.locked
                        ? "/Images/quiz/locked.png"
                        : isActive
                        ? "/Images/quiz/selectedChapter.png"
                        : "/Images/quiz/Chapter.png"
                    }
                    alt="icon"
                    fill
                    className="object-contain"
                  />
                </div>
                <p className="font-[ScandiaMedium] text-[14px] lg:text-[16px]">
                  {chapter.title}
                </p>
              </div>
            );
          })}
        </div>
      </div>
    </>
  );
};

export default SideMenu;
