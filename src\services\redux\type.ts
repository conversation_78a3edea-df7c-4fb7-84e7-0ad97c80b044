// types.ts or inline in your middleware
export interface RegisterPayload {
  email: string;
  password: string;
  confirmPassword: string;
  isOrganization: boolean;
  organizationName?: string;
  FullName?: string;
  designation?: string;
}

export interface OrganizationRegisterPayload {
  email: string;
  organizationName: string;
  organizationNameArabic: string;
  country: string;
  website: string;
  contactName: string;
  contactPhone: string;
  contactTitle: string;
  contactEmail: string;
  goodGovernance: boolean;
  transparencyReporting: boolean;
  sustainableFunding: boolean;
  impactMeasurement: boolean;
  shortBio: string;
  organizationImage: string;
  organizationTags: string[];
}

export interface loginPayload {
  email: string;
  password: string;
}

export interface verifyPayload {
  email: string;
  code: string;
}

export interface resendPayload {
  email: string;
}