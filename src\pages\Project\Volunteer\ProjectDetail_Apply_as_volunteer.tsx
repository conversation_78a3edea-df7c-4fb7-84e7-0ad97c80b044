"use client";
import React from "react";
import { Card, CardContent, Box, Avatar } from "@mui/material";
import "../Donation/ProjectDetail"
import "./Volunteer.css"
import Image from "next/image";
import { useRouter } from 'next/navigation';
const ProjectDetail_Apply_as_volunteer = () => {
  const router = useRouter();

    return (
        <div>
        <Box mt={15} className="mcncghfjshhhh" mb={4} bgcolor="#FCFCFC">
            <p
                className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31] mb-[24px] flex items-center gap-1  dhakghghsfshjsbjhseee"
                style={{ fontFamily: "ScandiaMedium" }}
            >
                <span>Project</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
                <span>Rebuilding Children&apos;s Hospital in Aleppo</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
                <span>Apply as volunteer</span>
            </p>
            <div className="tynxrw3mauluilqyrrt">

                <div className="hsdjhhjhddjhfdfnn">
                    <Card sx={{ border: "none", boxShadow: "none", padding: "0px" }}>
                        <CardContent sx={{ padding: "0px" }}>
                            <Box display="flex" alignItems="center" gap={1} mb={2} mt={1}>
                                <Avatar src="/Img/city.png" sx={{ width: "48px", height: "48px" }} />
                                <div >
                                    <p
                                        className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >Rebuild Syria Network</p>
                                    <p
                                        className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                                        style={{ fontFamily: "Scandia" }}
                                    >Infrastructure & development</p>
                                </div>
                            </Box>
                            <Box borderRadius={4} mb={2} overflow="hidden">
                                <Image
                                    src="/Img/Syrian house.png"
                                    alt="Main Project"
                                    className="fsdhjsdfjdfhjjhsf"
                                    width={800}
                                    height={400}
                                />
                            </Box>
                            <div className="hdhhhyttrtrt">
                            <p
                                className=" sdfggsdfdhsgasdfhasdjajdjjdjd"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Rebuilding Children&apos;s Hospital in Aleppo</p>
                            <p className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mb-[16px] m-[0px]" style={{ fontFamily: "Scandia" }}>
                                Reconstructing homes for displaced Syrian families, focusing on sustainable and durable housing.
                            </p>
                            </div>
                            <Box mt={2}>
                                <p
                                    className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Details</p>
                                <div className="flex items-center gap-[30px]">
                                    <div className="flex items-center gap-[8px]">
                                        <Image src="/Img/loc.svg" alt="Location" width={24} height={24} />
                                        <div>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Location:</p>
                                            <p className="font-[400] leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Aleppo, Syria</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-[8px]">
                                        <Image src="/Img/calendar.svg" alt="Calendar" width={24} height={24} />
                                        <div>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Project timeline:</p>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>April–June 2025</p>
                                        </div>
                                    </div>
                                </div>

                            </Box>
                    
                        </CardContent>
                    </Card>
                </div>
{/* ....../ */}

<div className="tttggdgvsygvdhgsdvh">
    <div className="tttggdgvsygvdhgsdvhkkkkkk">
<h2 className="tttggdgvsygvdhgsdvhydyyddyp ">Apply as</h2>
      {/* Top Section */}
      <div className="flex justify-between items-center mb-6   ihahhahahh">
        <h2 className="tttggdgvsygvdhgsdvhydyyddyp1">Doctor</h2>
        <button className="applysjbb" style={{cursor:"pointer"}}  onClick={() => router.push("/Project/PD__Submit_application")} >
          Apply for this position
        </button>
      </div>
      </div>
      {/* Info Boxes */}
      <div className="hbdjhsbjhbjsyteye">
        <div className="hbdjhsbjhbjsyteye1">
          <Image src="/Img/projectImg/user.png" alt="Open positions" className="hbdjhsbjhbjsyteye1Img" width={24} height={24} />
          <span className="hbdjhsbjhbjsyteye1P">Open positions: 3</span>
        </div>
        <div className="hbdjhsbjhbjsyteye1">
          <Image src="/Img/projectImg/copy.png" alt="Applicants" className="hbdjhsbjhbjsyteye1Img" width={24} height={24} />
          <span className="hbdjhsbjhbjsyteye1P">Applicant: 16</span>
        </div>
        <div className="hbdjhsbjhbjsyteye1">
          <Image src="/Img/projectImg/clk.png" alt="Deadline" className="hbdjhsbjhbjsyteye1Img" width={24} height={24} />
          <span className="hbdjhsbjhbjsyteye1P">Deadline: 20th Mar 2025</span>
        </div>
      </div>

      {/* Description */}
      <div className="mb-6 tttggdgvsygvdhgsdvhkkkkkk">
        <h3 className="desbdjhbcjhebjhed" >Description</h3>
        <p className="desbxhjsbsjhbxsh ">
          The Syrian Orphan Charity, in partnership with local organizations, is working to restore and equip a
          children&apos;s hospital in Aleppo. This hospital will provide urgent medical care, vaccinations, trauma
          recovery, and long-term health support for displaced and orphaned children. As a volunteer doctor,
          you will play a key role in bringing hope and healing to this vulnerable community.
        </p>
      </div>

      {/* Who we need */}
      <div className="mb-6 tttggdgvsygvdhgsdvhkkkkkk">
        <h3 className="desbdjhbcjhebjhed">Who we need</h3>
        <p className="desbxhjsbsjhbxsh">
          We are seeking dedicated medical professionals to provide healthcare for children affected by the
          crisis in Aleppo. Your expertise will help restore life-saving pediatric care in a hospital currently
          under reconstruction.
        </p>
      </div>

      {/* Your Responsibilities */}
      <div className="mb-6 tttggdgvsygvdhgsdvhkkkkkk">
        <h3 className="desbdjhbcjhebjhed">Your Responsibilities</h3>
        <ul className="list-disc list-inside desbxhjsbsjhbxsh">
          <li>Provide medical consultations, diagnosis, and treatment for children and infants</li>
          <li>Assist in emergency cases and coordinate urgent medical procedures</li>
          <li>Administer vaccinations and routine checkups for children in need</li>
          <li>Support training sessions for local healthcare workers and medical students</li>
          <li>Collaborate with NGO teams to ensure proper medical supplies and logistics</li>
        </ul>
      </div>

      {/* Requirements */}
      <div className="mb-6 tttggdgvsygvdhgsdvhkkkkkk">
        <h3 className="desbdjhbcjhebjhed">Requirements</h3>
        <ul className="list-disc list-inside desbxhjsbsjhbxsh">
          <li>Licensed medical doctor with specialization in pediatrics, general medicine, or emergency care</li>
          <li>Minimum 2 years of clinical experience (field experience preferred)</li>
          <li>Ability to work in challenging environments with limited resources</li>
          <li>Willingness to adapt to emergency and humanitarian healthcare needs</li>
          <li>Arabic proficiency is a plus but not required (translators available)</li>
        </ul>
      </div>

      {/* What we provide */}
      <div className="mb-6 tttggdgvsygvdhgsdvhkkkkkk">
        <h3 className="desbdjhbcjhebjhed">What we provide</h3>
        <ul className="list-disc list-inside desbxhjsbsjhbxsh">
          <li>Accommodation & meals provided for on-site volunteers</li>
          <li>Basic medical supplies & equipment</li>
          <li>Security support & local coordination</li>
          <li>Certificate of volunteering & humanitarian service</li>
        </ul>
      </div>

      {/* Application Process */}
      <div>
        <h3 className="desbdjhbcjhebjhed">What we provide</h3>
        <ul className="list-disc list-inside desbxhjsbsjhbxsh">
          <li>
            Click <span className="desbdjhbcjhebjhed"  >Apply for this position</span>
          </li>
          <li>
            Submit your <span className="desbdjhbcjhebjhed">CV, medical license, and a short motivation letter</span>
          </li>
          <li>Our team will review your application and schedule a brief online interview</li>
          <li>If selected, you&apos;ll receive a volunteer briefing, logistics guide, and next steps</li>
        </ul>
      </div>
    </div>









          {/* ...... */}
            </div>
            
        </Box>
        <div className="sdghfsdhdgsjhsjsalg">
        <div className="retwytqeuwqryeralg">
          <p
            className="font-[500]  leading-[40px] tracking-[0%] text-[#ffffff]  hdskguqzqa"
            style={{ fontFamily: "ScandiaMedium" }}
          >
            Join Us. Shape the Future.
          </p>
          <div className="msuhkqwaidaalg">
            <button
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px] flex items-center justify-center text-center bg-[#ECE3E7] rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
              style={{ fontFamily: "ScandiaMedium" }}
            >
              As an Organization
            </button>
            <button
              className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-transparent rounded-[80px] border-0 w-[212px] h-[49px] mt-[24px] max-w-[212px]"
              style={{ fontFamily: "ScandiaMedium", border: "1px solid white" }}
            >
              As an Individual
            </button>
          </div>
        </div>
      </div>
</div>
    )
}

export default ProjectDetail_Apply_as_volunteer