@import "tailwindcss";

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

@font-face {
  font-family: "Scandia";
  src: url("/fonts/fonnts.com-Scandia.otf") format("opentype");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ScandiaMedium";
  src: url("/fonts/fonnts.com-Scandia_Medium.otf") format("opentype");
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "ScandiaBold";
  src: url("/fonts/fonnts.com-Scandia_Bold.otf") format("opentype");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "coheadlineRegular";
  src: url("/fonts/Co Headline.woff") format("woff");
}

@font-face {
  font-family: "coheadlineBold";
  src: url("/fonts/Co Headline Bold.woff") format("woff");
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: white;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  margin: 0px !important;
}

p,
h1,
h2,
h3 {
  margin: 0px !important;
}
.custom-footer-placeholder {
  font-family: "Scandia";
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  color: #ffffff;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 0 85% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 0 85% 52%;
    --primary-foreground: 210 40% 98%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
  .input {
    border: none !important;
    background-color: transparent !important;
  }
  input:-webkit-autofill {
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    background-color: transparent !important;
    -webkit-text-fill-color: rgba(35, 35, 35, 0.4) !important;
    transition: background-color 5000s ease-in-out 0s;
  }

  p,
  h1,
  h2,
  h3 {
    margin: 0 !important;
  }
}
/* In your global CSS or Tailwind config file */
.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.gap-spacing {
  gap: 84px;
}
.hiden-block-stlying {
  display: block;
}
.join-pading {
  padding: 0px 64px 0px 64px;
}
/* styles.css */
.mobile-only {
  display: none;
}
.desktop-only {
  display: block;
}

@media (max-width: 919px) {
  .mobile-only {
    display: block;
  }
  .desktop-only {
    display: none;
  }
}

@media (max-width: 1268px) {
  .gap-spacing {
    gap: 50px;
  }
  .hiden-block-stlying {
    display: none !important;
  }
  .join-pading {
    padding: 0px 24px 0px 24px;
  }
}

@media (max-width: 919px) {
  .gap-spacing {
    gap: 40px;
  }
    .join-pading {
    padding: 0px 0px 0px 0px;
  }
}

/* .... */
.css-j8yymo {
  position: unset !important;
}

.div {
  box-sizing: border-box;
}

input,
select,
textarea,
button {
  outline: 0;
}
.responsive-box {
  display: flex;
  max-width: 1000px; /* or whatever value you need */
}
    .responsive-box-mob {
    display: none;
  }

@media (max-width: 919px) {
  .responsive-box {
    display: none;
  }
    .responsive-box-mob {
    display: flex;
  }
}
