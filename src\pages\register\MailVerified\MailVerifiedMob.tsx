import Button from '@/components/button/Button'
import Image from 'next/image'
import { useRouter } from 'next/navigation';
import React from 'react'

function MailVerifiedMob() {
  const router = useRouter();
    const handlesignin = () =>{
    router.push("/Auth/SignIn/signin");
  }
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
    <div className="flex flex-col items-center gap-[24px]">
      <div>
        <Image
          src="/Images/signin/elements.svg"
          alt="lock"
          width={40}
          height={40}
        />
      </div>
      <div className="flex flex-col gap-[8px]">
        <p className="text-[22px] leading-[30px] font-[scandiaMedium] text-[black] text-center">
         Your email has been successfully verified
        </p>
        <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
         You can now access your account and start contributing.
        </p>
      </div>
      <div className="flex flex-col gap-[12px]">
        <Button>
          <p className="text-[14px] font-[scandiaMedium] text-[white]" onClick={handlesignin}>
    Sign in
          </p>
        </Button>
      </div>
    </div>
      </div>
    </div>
  )
}

export default MailVerifiedMob
