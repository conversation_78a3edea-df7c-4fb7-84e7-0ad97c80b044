.vfcghjvfghcvjd1 {
  display: flex;
  flex-direction: column;

  padding: 40px 100px 80px 100px;
  margin-top: 100px;
}
.sdfggsdfdhsgasdfhasdjahbcsjdhbcs {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 28px;
  line-height: 36px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #181916;
  margin: 0px 0px 32px 0px !important;
}

.hvhvhgvhgjv {
  margin-bottom: 25px;
}

.vjsghvjhsgjhbjhbv {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}
.behdcjehbcard {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 80px;
}
.bhjdbcjdhbcjhdbPPP {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  color: #181916;
  margin: 0px !important;
}
/* .... */
.bdkjdhbxjhbdthispos {
  padding: 9px 0px 10px 0px;
  display: flex;
  flex-direction: column;
  gap: 21px;
}
.bdkjdhbxjhbdthisposP {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #000000;
  margin: 0px !important;
}

.bdkjdhbxjhbdthisposhgvh {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
}
.bdkjdhbxjhbdthisposhgvhcard {
  max-width: 180px;
  border-radius: 20px;
  border-width: 1px;
  padding: 16px;
  background: #ffffff;
  border: 1px solid #ece7e3;
  box-shadow: 4px 12px 24px 0px #0000000a;
}
.vhgsvhgvdhsgvhsg {
  width: 24px;
  height: 24px;
  margin-bottom: 8px;
}
.bjfhdbjdhfb {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: left;
  vertical-align: middle;
  color: #181916;
  margin: 0px 0px 2px 0px !important;
}

.hjbdfjhebcvjde {
  font-family: Scandia;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0%;
  text-align: left;
  vertical-align: middle;
  color: #181916;
}

.ybshcdbcshbc {
  width: 126px;
  height: 44px;
  border-radius: 80px;
  background: #d31b1b;
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
  margin: 32px 0px 0px !important;
  border: none;
}
.bjhdbjhbjb {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  color: #2f3b31;
  margin: 0px !important;
}
.sdghfsdhdgsjhsjsalg {
  display: flex;
  justify-content: space-between;
  padding: 64px 100px;
  align-items: center;
  background-image: url("/Img/Become\ Partner.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.retwytqeuwqryeralg {
  display: flex;
  justify-content: space-between;
  gap: 40px;
  width: 100%;
  align-items: center;
}
.msuhkqwaidaalg {
  display: flex;
  align-items: center;
  gap: 20px;
}
/* .... */

.hsdjhhjhddjhfdfnn {
    height: 710px;
    padding: 32px;
    border-radius: 20px;
    border: 1px solid #0000000A;
    box-shadow: 4px 12px 24px 0px #0000000A;
}
.hbdjhsbjhbjsyteye {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}
.hbdjhsbjhbjsyteye1 {
  padding-top: 12px;
  padding-right: 16px;
  padding-bottom: 12px;
  padding-left: 16px;
  border-radius: 20px;
  border-width: 1px;
  background: #ffffff;
  border: 1px solid #ece7e3;
  display: flex;
  align-items: center;
  gap: 8px;
}
.hbdjhsbjhbjsyteye1P {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.sdfggsdfdhsgasdfhasdjajdjjdjd {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 22px;
  line-height: 32px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #181916;
}
.hbdjhsbjhbjsyteye1Img {
  width: 24px;
  height: 24px;
}
.applysjbb {
  width: 197px;
  height: 52px;
  border-radius: 80px;
  background: #d31b1b;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
}
.mcncghfjshhhh {
  padding: 32px 100px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dhakghghsfshjsbjhs {
  font-size: 14px;
}
.dhakghghsfshjsbjhseee {
    font-size: 14px;
  }
.hdhhhyttrtrt {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tttggdgvsygvdhgsdvh {
  max-width: 736px;
  border-radius: 20px;
  border-width: 1px;
  padding: 32px;
  border: 1px solid #0000000a;
}
.tttggdgvsygvdhgsdvhydyyddyp {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: left;
  color: #181916;
}
.tttggdgvsygvdhgsdvhkkkkkk {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.tttggdgvsygvdhgsdvhydyyddyp1 {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  letter-spacing: 0%;
  vertical-align: middle;
  text-align: left;
  color: #000000;
}
.desbdjhbcjhebjhed {
  font-family: "ScandiaMedium";
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: left;
  color: #181916;
}
.desbxhjsbsjhbxsh {
  font-family: Scandia;
  font-weight: 400;
  font-size: 13px;
  line-height: 20px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #303030;
  margin-left: 5px;
}
/* ..... */
.tttggdgvsygvdhgsdvhsat {
    border: 1px solid #0000000a;
    border-radius: 20px;
    max-width: 736px;
    padding: 32px;
}
.tttggdgvsygvdhgsdvhkkkkkksat {
    flex-direction: column;
    gap: 12px;
    display: flex
;
}
.hsdjhhjhddjhfdfnnsaty {
    padding: 32px;
    border-radius: 20px;
    border: 1px solid #0000000A;
    box-shadow: 4px 12px 24px 0px #0000000A;
    max-width: 436px;
}
.hsdjhhjhddjhfdfnnsatygggg2 {
    padding: 32px;
    border-radius: 20px;
    border: 1px solid #0000000A;
    box-shadow: 4px 12px 24px 0px #0000000A;
    display: flex;
    flex-direction: column;
    gap: 40px;
}
.yggtreww{
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 28px;
line-height: 36px;
letter-spacing: 0%;
vertical-align: middle;
color: #000000;

}
.tttggdgvsygvdhgsdvhkkkksatt1{
display: flex;
flex-wrap: wrap;
gap: 12px;
align-items: center;

}
.tttggdgvsygvdhgsdvhkkkksatt11{

    padding-top: 17px;
    padding-right: 24px;
    padding-bottom: 17px;
    padding-left: 24px;
    border-radius: 70px;
    background: #ECE7E3;
    display: flex;
    align-items: center;
    gap: 6px;
}
.tttggdgvsygvdhgsdvhkkkksatt11_second{

    padding-top: 17px;
    padding-right: 24px;
    padding-bottom: 17px;
    padding-left: 24px;
    border-radius: 70px;
    border: 1px solid #303030;
    display: flex;
    align-items: center;
    gap: 6px;
}
.tttggdgvsygvdhgsdvhkkkksatt111{
    width: 18px;
    height: 18px;
    
}
.yybcybcybcybyb{
    width: 180px;
    height: 52px;
    border-radius: 80px;
    background: #D31B1B;
    display: flex;
    flex-direction: row;
align-items: center;
justify-content: center;
    font-family: "ScandiaMedium";
font-weight: 500;
font-size: 14px;
line-height: 20px;
letter-spacing: 0%;
text-align: center;
vertical-align: middle;
color: #FFFFFF;

}
/* ... */
@media (max-width: 1400px) {

    .tttggdgvsygvdhgsdvh{
        max-width: 660px;
      }
      .tttggdgvsygvdhgsdvhsat {
        max-width: 660px;
    }
}


@media (max-width: 1350px) {
  .mcncghfjshhhh {
    padding: 25px 70px;
  }
  .hsdjhhjhddjhfdfnn{
    height: unset;
  }
  .tttggdgvsygvdhgsdvhsat {
    max-width: 100%;
}
 
}

@media (max-width: 1100px) {
  .tttggdgvsygvdhgsdvh {
    max-width: 100%;
  }
.hsdjhhjhddjhfdfnnsaty{
    max-width: 100%;
}
  .mcncghfjshhhh {
    padding: 15px;
  }
  .hsdjhhjhddjhfdfnn {
    padding: 15px;
}
.hsdjhhjhddjhfdfnnsaty {
    padding: 15px;
}
.hsdjhhjhddjhfdfnnsatygggg2{
    padding: 15px;
}
}
@media (max-width: 1024px) {
  .sdghfsdhdgsjhsjsalg {
    padding: 40px 40px;
  }
  .retwytqeuwqryeralg {
    flex-direction: column;
    gap: 0;
  }
}
@media (max-width: 768px) {
  .vfcghjvfghcvjd1 {
    padding: 40px;
  }
  .dhakghghsfs {
    font-size: 11px;
  }
  .dhakghghsfshjsbjhs {
    font-size: 11px;
  }
  .dhakghghsfshjsbjhseee {
    font-size: 11px;
  }
  .sdfggsdfdhsgasdfhasdjahbcsjdhbcs {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
  }
  .bhjdbcjdhbcjhdbPPP {
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
  }

  .tttggdgvsygvdhgsdvh{
    padding: 15px;
  }
  .tttggdgvsygvdhgsdvhsat {
    padding: 15px;
}
  .tttggdgvsygvdhgsdvhydyyddyp1{
    font-size: 30px;
    line-height: 36px;
  }
  .applysjbb {
    width: 170px;
    height: 44px;
    font-size: 13px;
    line-height: 18px;

  }
}
@media (max-width: 525px) {
    .tttggdgvsygvdhgsdvhydyyddyp1 {
        font-size: 27px;
        line-height: 30px;
    }
    .tttggdgvsygvdhgsdvhydyyddyp {   
        font-size: 13px;
        line-height: 14px;
    }
    .tttggdgvsygvdhgsdvhkkkkkk{
gap: 2px;
    }
    .ihahhahahh{
margin-bottom: 13px;
    }
    .hbdjhsbjhbjsyteye1 {
        gap: 5px;
        padding: 10px 10px;
    
    }
    .hbdjhsbjhbjsyteye1P {
       
        font-size: 11px;
       
    }
  .msuhkqwaidaalg {
    flex-direction: column;
    gap: 10px;
  }
  .hbdjhsbjhbjsyteye1Img {
    width: 18px;
    height: 18px;
}
.hbdjhsbjhbjsyteye {
    
    margin-bottom: 30px;
}
}
@media (max-width: 500px) {
  .vfcghjvfghcvjd1 {
    padding: 30px 20px;
  }

  .bjhdbjhbjb {
    font-size: 11px;
    line-height: 16px;
  }
  .hhhhhyuyguy {
    flex-wrap: wrap;
  }
  .dhakghghsfs {
    font-size: 9px;
  }
  .dhakghghsfshjsbjhs {
    font-size: 9px;
  }
  .bhjdbcjdhbcjhdbPPP {
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;
  }
  .desbxhjsbsjhbxsh {
    font-size: 12px;
    line-height: 17px;
}
.dhakghghsfshjsbjhseee {
    font-size: 8px;
}
.sdfggsdfdhsgasdfhasdjajdjjdjd {
    font-size: 15px;
    font-weight: 500;
    line-height: 21px;
}
.yggtreww {
    font-size: 20px;
    line-height: 26px;
}
.hsdjhhjhddjhfdfnnsatygggg2{
    gap: 15px;
}
}

@media (max-width: 400px) {
.yybcybcybcybyb{
    width: 100%;
}
}
@media (max-width: 370px) {
  .bjhdbjhbjb {
    font-size: 10px;
    line-height: 16px;
  }
  .bdkjdhbxjhbdthisposhgvhcard {
    width: 100%;
    max-width: 100%;
  }
  .ybshcdbcshbc {
    width: 100%;
  }
  .bhjdbcjdhbcjhdbPPP {
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
  }

  .tttggdgvsygvdhgsdvh {
    padding: 12px;
}

.tttggdgvsygvdhgsdvhydyyddyp1 {
    font-size: 20px;
    line-height: 30px;
}
.applysjbb {
    width: 146px;
    height: 40px;
    font-size: 12px;
    line-height: 18px;
}
/* .dhakghghsfshjsbjhseee {
    font-size: 6px;
  } */

  .tttggdgvsygvdhgsdvhkkkksatt11 {
    border-radius: 40px;
    gap: 4px;
    padding: 12px 12px;
    display: flex
;
}
    .tttggdgvsygvdhgsdvhkkkksatt11_second {
        border-radius: 40px;
        gap: 4px;
        padding: 11px 11px;
        display: flex
    }
.desbdjhbcjhebjhed{
    font-size: 13px;
}
}
