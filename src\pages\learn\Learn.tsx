"use client"
import React, { useEffect, useState } from "react";
import Hero from "./Hero";
import { CodeOfEthics } from "./Components/CodeOfEthics";
import {ExploreCourses} from "./Components/ExploreCourses";
import { ExploreFeatured } from "./Components/ExploreFeatured";

export const Learn = (): React.ReactElement => {
  const [isSignedIn, setIsSignedIn] = useState(false);

  useEffect(() => {
    const stored = localStorage.getItem("myKey");
    if (stored) setIsSignedIn(true);
  }, []);

  return (
    <>
      <Hero />
      <div className="py-[36px] md:py-[80px] px-[24px] md:px-[50px] lg:px-[100px]  bg-white flex flex-col gap-6 lg:gap-8">
        <div className="hidden md:block">
          <span>{isSignedIn ? <ExploreFeatured /> : <CodeOfEthics />}</span>
        </div>
        <ExploreCourses />
      </div>
    </>
  );
};

export default Learn;
