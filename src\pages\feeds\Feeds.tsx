"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
const comments = [
  {
    name: "<PERSON><PERSON><PERSON>",
    time: "18h",
    text: "Really love the vibe brother. Lets make some project!",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
  {
    name: "<PERSON><PERSON>",
    time: "18h",
    text: "Lets rebuild our Syria",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
  {
    name: "<PERSON>",
    time: "18h",
    text: "Really love the vibe brother. Lets make some project!",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
  {
    name: "<PERSON><PERSON>",
    time: "18h",
    text: "Lets rebuild our Syria",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
];
const comment = [
  {
    name: "<PERSON><PERSON>",
    time: "18h",
    text: "Lets rebuild our Syria",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
  {
    name: "Tariq Darwish",
    time: "18h",
    text: "Really love the vibe brother. Lets make some project!",
    avatar: "/Images/community/image.png",
    icon: "/Images/community/Frame 1597884065.svg",
  },
];

const buttonsGroup1 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
const buttonsGroup2 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
const buttonsGroup3 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
const buttonsGroup4 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];

function Feeds() {
  const [activeIndexGroup1, setActiveIndexGroup1] = useState<number | null>(null);
  const [activeIndexGroup2, setActiveIndexGroup2] = useState<number | null>(null);
  const [activeIndexGroup3, setActiveIndexGroup3] = useState<number | null>(null);
  const [activeIndexGroup4, setActiveIndexGroup4] = useState<number | null>(null);

  const router = useRouter();
  const handlecreate = () => {
    router.push("/Community/createpost");
  };
  return (
    <div className="flex flex-col max-[919px]:gap-[16px] gap-[20px] h-auto text-[white] flex justify-center items-center">
      {/* //create new post */}
      <div className="bg-[white] w-full max-[919px]:hidden block flex flex-row gap-[12px] text-[black] border border-[#0000000A] rounded-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
        <div className="w-[48px] h-[48px] rounded-full ">
          <Image
            src="/Images/community/image.svg"
            alt="profile"
            height={48}
            width={48}
          />
        </div>
        <div className="flex flex-col gap-[16px] w-full">
          <div className="flex gap-[16px] w-[100%]">
            <input
              type="text"
              placeholder="Start a post"
              className="w-full border h-[48px]  border-[#23232366] rounded-full px-[24px] py-[8.5] text-base placeholder-[#23232366] focus:outline-none focus:ring-[#23232366]]"
            />
            <button className="bg-[#D31B1B] cursor-pointer text-[black] items-center py-[16px] px-[17px] rounded-[80px]">
              <p className="text-[white] whitespace-nowrap text-[14px] leading-[20px] font-[scandiaMedium]">
                Create New Post
              </p>
            </button>
          </div>
          <div className="flex items-center gap-[8px]">
            <button className="bg-[#ECE7E3] cursor-pointer flex gap-[6px] text-[black] items-center py-[13px] px-[20px] rounded-[70px]">
              <Image
                src="/Images/community/image-01.svg"
                alt="profile"
                height={18}
                width={18}
              />
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Add photo{" "}
              </p>
            </button>
            <button className="bg-[#ECE7E3] cursor-pointer flex gap-[6px] text-[black] items-center py-[13px] px-[20px] rounded-[70px]">
              <Image
                src="/Images/community/video-02.svg"
                alt="profile"
                height={18}
                width={18}
              />
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Add video{" "}
              </p>
            </button>
          </div>
        </div>
      </div>
      <div className="flex gap-[16px] max-[919px]:block hidden w-[100%]">
        <input
          type="text"
          placeholder="Start a post"
          onClick={handlecreate}
          className="w-full border bg-[white] text-[#23232366] h-[48px] border-[#23232366] rounded-full px-[24px] py-[8.5] text-base placeholder-[#23232366] focus:outline-none focus:ring-[#23232366]]"
        />
      </div>
      {/* post */}
      <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
        <div className="flex justify-between gap-[12px]">
          <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
            <div className="w-[48px] h-[48px] rounded-full ">
              <Image
                src="/Images/community/image.svg"
                alt="profile"
                height={48}
                width={48}
              />
            </div>
            <div className="flex flex-col gap-[4px]">
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Rania Mustafa
              </p>
              <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                Lawyer
              </p>
            </div>
          </div>
          <div>
            <Image
              src="/Images/community/Frame 1597884065.svg"
              alt="profile"
              height={8}
              width={18}
              className="cursor-pointer"
            />
          </div>
        </div>

        <div className="flex flex-col gap-[4px]">
          {" "}
          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
            My heart is full today! I just became a volunteer teacher for the
            Schools of Tomorrow project. So many children have been out of
            school for years, and I can&apos;t wait to help them rediscover the joy
            of learning. Education is the foundation of a better future! 🎓✨
          </p>
          <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
            24 Feb 2025, 07.36 PM
          </p>
        </div>
        <div>
          <div>
            <div className="flex items-center gap-[8px]">
              {buttonsGroup1.map((item, index) => {
                const isActive = activeIndexGroup1 === index;
                return (
                  <button
                    key={index}
                    onClick={() => setActiveIndexGroup1(index)}
                    className={`${
                      isActive
                        ? "bg-[#2F3B31] text-white"
                        : "bg-[#ECE7E3] text-black"
                    } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                  >
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      {item.value}
                    </p>
                    <Image
                      src={isActive ? item.activeIcon : item.defaultIcon}
                      alt="icon"
                      height={18}
                      width={18}
                      className="w-[18px] h-[18px]"
                    />
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      {/* post -with img */}
      <div className="bg-[white] w-full flex flex-col gap-[16px] border border-[#0000000A] text-[black] rounded-[20px] max-[919px]:py-[20px] max-[919px]:px-[16px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
        <div className="flex justify-between gap-[12px]">
          <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
            <div className="w-[48px] h-[48px] rounded-full ">
              <Image
                src="/Images/community/image.svg"
                alt="profile"
                height={48}
                width={48}
              />
            </div>
            <div className="flex-flex-col gap-[4px]">
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Tariq Darwish
              </p>
              <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                Architect
              </p>
            </div>
          </div>
          <div>
            <Image
              src="/Images/community/Frame 1597884065.svg"
              alt="profile"
              height={8}
              width={18}
              className="cursor-pointer"
            />
          </div>
        </div>

        <div className="flex flex-col max-[919px]:gap-[12px] gap-[16px]">
          {" "}
          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
            Clean water is a right, not a privilege. 💧 Today, we installed a
            new water filtration system in a refugee camp, providing safe
            drinking water for over 500 people. Small steps, big impact! Let&apos;s
            keep pushing forward for a healthier, stronger Syria
          </p>
          <div className="flex max-[1080px]:flex-col gap-[16px] w-full">
            <div className=" max-[1200px]:w-fit w-[60%] h-[395px] max-[800px]:h-auto max-[1000px]:w-full w-[603px] rounded-[12px]">
              <img
                src="/Images/community/feeds1.png"
                alt="profile"
               
                className="w-[100%] object-cover h-[100%] rounded-[20px]"
              />
            </div>
            <div className="flex flex-col max-[1080px]:flex-row max-[1080px]:w-[100%] max-[1200px]:w-fit  w-[40%] max-[800px]:w-full gap-[16px]">
              <img
                src="/Images/community/water filtration work in Syria.png"
                alt="profile"
                className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]: max-[800px]:h-auto"
              />
              <img
                src="/Images/community/Frame **********.png"
                alt="profile"
                className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]:h-auto"
              />
            </div>
          </div>
          {/* <div className="flex flex-col responsive-box-mob max-[919px]:block hidden gap-[8px]">
            <div className="flex gap-[8px]">
              <img
                src="/Images/community/Frame **********.svg"
                alt="profile"
                className="w-[277px] max-[1080px]:w-[50%] object-cover h-[192.5px]  rounded-[12px]"
              />
              <Image
                src="/Images/community/Frame **********.svg"
                alt="profile"
                height={8}
                width={18}
                className="w-[50%] object-cover h-[50%]rounded-[12px]"
              />
            </div>
            <div className="flex w-full gap-[8px]">
              <Image
                src="/Images/community/Frame **********.svg"
                alt="profile"
                height={8}
                width={8}
                className="w-[50%] object-cover h-[50%] rounded-[12px]"
              />
              <Image
                src="/Images/community/Frame **********2222.svg"
                alt="profile"
                height={8}
                width={18}
                className="w-[50%] h-[50%]rounded-[12px]"
              />
            </div>
          </div> */}
          <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
            24 Feb 2025, 05.23 PM
          </p>
        </div>
        <div className="flex items-center gap-[8px]">
          {buttonsGroup2.map((item, index) => {
            const isActive = activeIndexGroup2 === index;
            return (
              <button
                key={index}
                onClick={() => setActiveIndexGroup2(index)}
                className={`${
                  isActive
                    ? "bg-[#2F3B31] text-white"
                    : "bg-[#ECE7E3] text-black"
                } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
              >
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  {item.value}
                </p>
                <Image
                  src={isActive ? item.activeIcon : item.defaultIcon}
                  alt="icon"
                  height={18}
                  width={18}
                  className="w-[18px] h-[18px]"
                />
              </button>
            );
          })}
        </div>
      </div>
      {/* with comments */}
      <div className="bg-[white] w-full flex flex-col gap-[16px] border border-[#0000000A] text-[black] rounded-[20px] pt-[20px] max-[919px]:pb-[28px] max-[919px]:px-[16px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
        <div className="flex justify-between gap-[12px]">
          <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
            <div className="w-[48px] h-[48px] rounded-full ">
              <Image
                src="/Images/community/image.svg"
                alt="profile"
                height={48}
                width={48}
              />
            </div>
            <div className="flex flex-col gap-[4px]">
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Tariq Darwish
              </p>
              <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                Architect
              </p>
            </div>
          </div>
          <div>
            <Image
              src="/Images/community/Frame 1597884065.svg"
              alt="profile"
              height={8}
              width={18}
              className="cursor-pointer"
            />
          </div>
        </div>
        <div className="flex flex-col gap-[4px]">
          {" "}
          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
            What a great day to start helping the community
          </p>
          <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
            24 Feb 2025, 08.28 AM
          </p>
        </div>
        <div>
          <div>
            <div className="flex items-center gap-[8px]">
              {buttonsGroup3.map((item, index) => {
                const isActive = activeIndexGroup3 === index;
                return (
                  <button
                    key={index}
                    onClick={() => setActiveIndexGroup3(index)}
                    className={`${
                      isActive
                        ? "bg-[#2F3B31] text-white"
                        : "bg-[#ECE7E3] text-black"
                    } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                  >
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      {item.value}
                    </p>
                    <Image
                      src={isActive ? item.activeIcon : item.defaultIcon}
                      alt="icon"
                      height={18}
                      width={18}
                      className="w-[18px] h-[18px]"
                    />
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <div
          style={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
          className="border border-b-[#ECE7E3] mb-[10px] pt-[10px]"
        >
          {" "}
        </div>
        <div className="w-full flex gap-[12px] text-[black]">
          <div className="max-[919px]:hidden block w-[48px] h-[48px] rounded-full ">
            <Image
              src="/Images/community/image.svg"
              alt="profile"
              height={48}
              width={48}
              className=""
            />
          </div>
          <div className="flex flex-col gap-[16px] w-full">
            <div className="flex  max-[360px]:flex-col max-[919px]:gap-[8px]  gap-[16px] border-none w-[100%]">
              <input
                type="text"
                placeholder="Leave a comment"
                className="w-full border h-[48px] border-[#23232366] max-[768px]:h-[44px] rounded-full max-[919px]:px-[20px] max-[919px]:py-[6.5px] px-[24px] py-[8.5] text-base placeholder-[#23232366]font-[scandia] placeholder:font-[scandia] font-[scandia] placeholder:text-[14px] text-[14px] focus:outline-none focus:ring-[#23232366]]"
              />
              <button className="bg-[#D31B1B] text-[black] max-[768px]:h-[44px] cursor-pointer items-center sm:py-[16px] py-[12px] sm:px-[35px] px-[20px] rounded-[80px]">
                <p className="text-[white] whitespace-nowrap text-[14px] leading-[20px] font-[scandiaMedium] ">
                  Post
                </p>
              </button>
            </div>
          </div>
        </div>
        {/* comments */}
        <div className="flex flex-col gap-[16px]">
          {comments.map((comment, idx) => (
            <div key={idx} className="flex justify-start gap-[12px]">
              <div className="rounded-full w-[auto]">
                <Image
                  src={comment.avatar}
                  alt="profile"
                  width={48}
                  height={48}
                  className="max-[919px]:w-[32px] max-[919px]:h-[32px] w-[55px] h-[48px]"
                />
              </div>
              <div className="flex justify-between max-[919px]:w-[80%] w-full max-[919px]:gap-[12px] gap-[16px]">
                <div className="flex flex-col gap-[4px]">
                  <p className="max-[919px]:text-[12px] text-[14px] flex gap-[8px] items-center leading-[20px] font-[scandiaMedium]">
                    {comment.name}
                    <span className="text-[#23232366] text-[12px] leading-[20px] font-[scandia]">
                      {comment.time}
                    </span>
                  </p>
                  <p className="text-[#303030] text-[14px] leading-[20px] font-[scandia]">
                    {comment.text}
                  </p>
                </div>
              </div>
              <div className="flex justify-end w-[15%]">
                <Image
                  src={comment.icon}
                  alt="icon"
                  width={18}
                  height={8}
                  className="cursor-pointer"
                />
              </div>
            </div>
          ))}
          {comments.length > 3 && (
            <p className="text-[14px] max-[919px]:pt-[8px] pt-0  leading-[20px] font-[scandiaMedium]">
              Load more
            </p>
          )}
        </div>
      </div>
      {/* comments on post*/}
      <div className="bg-[white] w-full flex flex-col gap-[16px] border border-[#0000000A] text-[black] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
        <div className="flex justify-between gap-[12px]">
          <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
            <div className="w-[48px] h-[48px] rounded-full ">
              <Image
                src="/Images/community/image.svg"
                alt="profile"
                height={48}
                width={48}
              />
            </div>
            <div className="flex flex-col gap-[2px]">
              <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                Tariq Darwish
              </p>
              <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                Architect
              </p>
            </div>
          </div>
          <div>
            <Image
              src="/Images/community/Frame 1597884065.svg"
              alt="profile"
              height={8}
              width={18}
              className="cursor-pointer"
            />
          </div>
        </div>
        <div className="flex flex-col gap-[12px]">
          {" "}
          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
            Today, I visited one of our Homes for Hope sites, where the first
            foundation has been laid! Seeing families smile at the prospect of
            having a place to call home is truly heartwarming. Every brick we
            lay is a step closer to rebuilding Syria. Thank you to everyone
            contributing to this journey! ❤️🏡
          </p>
          <div className="flex gap-[16px] w-full">
            <div className="max-[919px]:w-[100%] max-[1080px]:w-[80%] max-[919px]:h-[275px] h-[395px] w-[603px] rounded-[12px]">
              <Image
                src="/Images/community/mens.svg"
                alt="profile"
                height={8}
                width={18}
                className="w-[100%] max-[919px]:h-[275px] h-[100%] rounded-[20px]"
              />
            </div>
          </div>
          <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
            24 Feb 2025, 08.28 AM
          </p>
        </div>
        <div>
          <div>
            <div className="flex items-center gap-[8px]">
              {buttonsGroup4.map((item, index) => {
                const isActive = activeIndexGroup4 === index;
                return (
                  <button
                    key={index}
                    onClick={() => setActiveIndexGroup4(index)}
                    className={`${
                      isActive
                        ? "bg-[#2F3B31] text-white"
                        : "bg-[#ECE7E3] text-black"
                    } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                  >
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      {item.value}
                    </p>
                    <Image
                      src={isActive ? item.activeIcon : item.defaultIcon}
                      alt="icon"
                      height={18}
                      width={18}
                      className="w-[18px] h-[18px]"
                    />
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        <div
          style={{ borderTop: "none", borderLeft: "none", borderRight: "none" }}
          className="border border-b-[#ECE7E3] mb-[10px] pt-[10px]"
        >
          {" "}
        </div>
        <div className="w-full flex gap-[12px] text-[black]">
          <div className="w-[48px] max-[919px]:hidden block h-[48px] rounded-full ">
            <Image
              src="/Images/community/image.svg"
              alt="profile"
              height={48}
              width={48}
            />
          </div>
          <div className="flex flex-col gap-[16px] w-full">
            <div className="flex  max-[360px]:flex-col max-[919px]:gap-[8px] gap-[16px] border-none w-[100%]">
              <input
                type="text"
                placeholder="Leave a comment"
                className="w-full border h-[48px] border-[#23232366] rounded-full px-[24px] py-[8.5] text-base placeholder-[#23232366] focus:outline-none focus:ring-[#23232366]]"
              />
              <button className="bg-[#D31B1B] cursor-pointer text-[black] items-center py-[16px] px-[35px] rounded-[80px]">
                <p className="text-[white] whitespace-nowrap text-[14px] leading-[20px] font-[scandiaMedium]">
                  Post
                </p>
              </button>
            </div>
          </div>
        </div>
        {/* comments */}
        <div className="flex flex-col gap-[16px]">
          {comment.map((comment, idx) => (
            <div key={idx} className="flex justify-start gap-[12px]">
              <div className="rounded-full w-[auto]">
                <Image
                  src={comment.avatar}
                  alt="profile"
                  width={48}
                  height={48}
                  className="max-[919px]:w-[32px] max-[919px]:h-[32px] w-[55] h-[48px]"
                />
              </div>
              <div className="flex justify-between max-[919px]:w-[80%] w-full max-[919px]:gap-[12px] gap-[16px]">
                <div>
                  <p className="max-[919px]:text-[12px] text-[14px] flex gap-[8px] items-center leading-[20px] font-[scandiaMedium]">
                    {comment.name}
                    <span className="text-[#23232366] text-[12px] leading-[20px] font-[scandia]">
                      {comment.time}
                    </span>
                  </p>
                  <p className="text-[#303030] text-[14px] leading-[20px] font-[scandia]">
                    {comment.text}
                  </p>
                </div>
              </div>
              <div className="flex justify-end w-[15%]">
                <Image
                  src={comment.icon}
                  alt="icon"
                  width={18}
                  height={8}
                  className="cursor-pointer"
                />
              </div>
            </div>
          ))}
          {comment.length > 3 && (
            <p className="text-[14px]  leading-[20px] font-[scandiaMedium]">
              Load more
            </p>
          )}
        </div>
      </div>
    </div>
  );
}

export default Feeds;
