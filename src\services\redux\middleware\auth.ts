// import { createAsyncThunk } from "@reduxjs/toolkit";
// import api from "../../apiInterceptor";
// import { API_URL } from "../../client";
// import { RegisterPayload } from "../type"; // Define this in types file

// export const register = createAsyncThunk(
//   "auth/register",
//   async (data: RegisterPayload, { rejectWithValue }) => {
//     try {
//       const response = await api.post(`${API_URL}/api/auth/register`, data);
//       return response.data; // usually you want the data part
//     } catch (error: any) {
    
//       return rejectWithValue(error.response?.data || { message: error.message });
//     }
//   }
// );

// // Replace the 'any' type with a more specific type
// // For example, if it's an error object, you might use:
// // Error: Unexpected any. Specify a different type.  @typescript-eslint/no-explicit-any

// // If it's on line 12, it might look something like this:
// const handleError = (error: Error | unknown) => {
//   // Your error handling logic
// }

// // Or if it's for a function parameter:
// function processData(data: Record<string, unknown>) {
//   // Your data processing logic
// }

// // Or if it's for a variable:
// const config: Record<string, string | number | boolean> = {
//   // Your configuration
// }
