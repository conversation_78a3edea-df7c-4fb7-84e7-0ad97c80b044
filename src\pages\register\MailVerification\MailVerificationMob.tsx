import Button from "@/components/button/Button";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

function MailVerificationMob() {
  const [seconds, setSeconds] = useState<number>(60);
  const router = useRouter();

  useEffect(() => {
    if (seconds <= 0) {
      router.push("/Auth/Register/verified-mail");
      return;
    }

    const interval = setInterval(() => {
      setSeconds((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [seconds, router]);
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
        <div className="flex flex-col items-center gap-[24px]">
          <div>
            <Image
              src="/Images/signin/mail-receive-01.svg"
              alt="lock"
              width={40}
              height={40}
            />
          </div>
          <div className="flex flex-col gap-[8px]">
            <p className="text-[22px] leading-[30px] font-[scandiaMedium] text-[black] text-center">
              Email verification
            </p>
            <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
              Email activation has been sent to
              <span className="text-[14px] cursor-pointer font-[scandiaMedium] text-[black]">
                {" "}
                <EMAIL>.
              </span>
              <br className="hidden xl:block" /> Check your inbox to activate.
            </p>
          </div>
          <div className="flex flex-col gap-[8px]">
            <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
              Didn’t receive any email?
            </p>
            <Button>
              <p className="text-[14px] font-[scandiaMedium] text-[white]">
                Resend in {seconds}s
              </p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MailVerificationMob;
