.course__detail__main__container {
  background: #ece7e3;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 100px 100px;
}
.course__detail__container__1 {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-top: 20px;
}
.course__detail__container__1__p {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #303030;
}
.course__detail__container__2 {
  background-color: white;
  padding: 32px;
  border-radius: 20px;
}
.course__detail__card__container__top {
  display: flex;
  gap: 32px;
}
.course__detail__card__header__wrapper {
  padding: 32px;
  border: 1px solid #0000000a;
  box-shadow: 4px 12px 24px 0px #0000000a;
  border-radius: 20px;
  width: 40%;
  height: 649px;
}

.course__detail__card__header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-bottom: 34px;
  border-bottom: 1px solid #0000000a;
}

.course__detail__card__avatar__wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #ece7e3;
  display: flex;
  align-items: center;
  justify-content: center;
}
.course__detail__card__header__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.course__detail__card__org {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  vertical-align: middle;
  color: #000000;
}
.course__detail__card__category {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #303030;
}

/* Details Section */
.course__detail__card__details {
  margin-top: 24px;
}
.course__detail__card__details__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #000;
  margin-bottom: 8px;
}
.course__detail__card__details__content {
  display: flex;
  gap: 40px;
}
.course__detail__card__details__item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.course__detail__card__details__item__info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.course__detail__card__details__item__label {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #23232366;
}
.course__detail__card__details__item__value {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}

/* Course Preview */
.course__detail__card__preview__section {
  margin-bottom: 18px;
  margin-top: 24px;
}
.course__detail__card__preview__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #181916;
  margin-bottom: 8px;
}

.course__detail__card__preview__video {
  width: 100%;
  height: 280px;
  object-fit: cover;
  display: block;
  border-radius: 16px;
}
.course__detail__card__preview__play {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.7);
  border-radius: 50%;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Take Course Button */
.course__detail__card__button {
  width: 100%;
  background: #d31b1b;

  color: #fff;
  font-size: 15px;
  font-weight: 600;
  border: none;
  border-radius: 80px;
  padding: 12px 0;
  margin-top: 18px;
  cursor: pointer;
  transition: background 0.2s;
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #fff;
}
.course__detail__card__button:hover {
  background: #c62828;
}
.course__detail__card__container__bottom {
  padding: 32px;
  border-radius: 20px;
  border: 1px solid #0000000a;
  width: 60%;
}
.course__detail__card__container__bottom__left {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}
.course__detail__card__container__bottom__left__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  vertical-align: middle;
  color: #000000;
}
.course__detail__info__wrapper {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.course__detail__tags {
  display: flex;
  gap: 12px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}
.course__detail__tag {
  background: #ece7e3;
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;
  color: #181916;
  padding: 12px 16px;
  border-radius: 20px;

  display: flex;
  align-items: center;
  gap: 8px;
}
.course__detail__learn {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.course__detail__learn__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #181916;
}
.course__detail__learn__desc {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #303030;
}

.course__detail__features {
  display: flex;
  gap: 18px;
  gap: 40px;
}
.course__detail__features__content__1 {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.course__detail__features__wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.course__detail__features__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #181916;
}
.course__detail__feature {
  font-size: 14px;
  color: #303030;
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  font-family: ScandiaMedium;
}
.course__detail__accordion__wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.course__detail__accordion {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
.course__detail__accordion__title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #181916;
}
.course__detail__feature__desc {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.course__detail__accordion__item {
  background: #faf9f7;
  border-radius: 14px;
  border: 1px solid #ece7e3;
  padding: 0 0 0 0;
  overflow: hidden;
}
.course__detail__accordion__item summary {
  font-family: ScandiaMedium;
  font-size: 16px;
  color: #181916;
  padding: 18px 20px;
  cursor: pointer;
  outline: none;
  list-style: none;
}
.course__detail__accordion__item[open] summary {
  border-bottom: 1px solid #ece7e3;
}
.course__detail__accordion__chapter {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f1ef;
}
.course__detail__accordion__chapter:last-child {
  border-bottom: none;
}
.course__detail__accordion__chapter__img img {
  border-radius: 10px;
  object-fit: cover;
}
.course__detail__accordion__chapter__info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}
.course__detail__accordion__chapter__number {
  font-family: ScandiaMedium;
  font-size: 13px;
  color: #d31b1b;
}
.course__detail__accordion__chapter__title {
  font-family: ScandiaMedium;
  font-size: 15px;
  color: #181916;
}
.course__detail__accordion__chapter__preview {
  font-family: Scandia;
  font-size: 13px;
  color: #00a86b;
  margin-top: 2px;
}
.course__detail__accordion__chapter__duration {
  font-family: ScandiaMedium;
  font-size: 13px;
  color: #303030;
  margin-left: 12px;
}
@media (max-width: 1200px) {
  .course__detail__main__container {
    padding: 100px 50px;
  }
}
@media (max-width: 1100px) {
  .course__detail__card__container__top,
  .course__detail__card__container__bottom {
    width: 100%;
    flex-direction: column;
  }
  .course__detail__card__header__wrapper {
    width: 100%;
  }
  .course__detail__features {
    gap: 15px;
  }
  .course__detail__features__content__1 {
    gap: 15px;
  }
  .course__detail__main__container {
    padding: 100px 50px;
  }
}
@media (max-width: 900px) {
  .course__detail__card__container__top {
    flex-direction: column;
    gap: 24px;
  }
  .course__detail__card__header__wrapper {
    width: 100%;
  }
  .course__detail__card__container__bottom {
    width: 100%;
    margin-top: 24px;
  }
}
@media (max-width: 700px) {
  .course__detail__main__container {
    padding: 100px 40px;
  }
  .course__detail__features,
  .course__detail__features__content__1 {
    flex-wrap: wrap;
  }
}
@media (max-width: 600px) {
  .course__detail__main__container {
    padding: 100px 24px 50px;
  }
  .course__detail__container__2 {
    padding: 16px 16px;
  }
  .course__detail__card__details__content {
    flex-direction: column;
    gap: 12px;
  }
  .course__detail__card__preview__video__wrapper {
    max-width: 100%;
  }
  .course__detail__card__container__bottom {
    padding: 12px 12px;
    border-radius: 12px;
  }
  .course__detail__info__wrapper {
    gap: 18px;
  }
  .course__detail__tags {
    gap: 6px;
  }
  
  .course__detail__features,
  .course__detail__features__content__1 {
    gap: 8px;
  }
  .course__detail__features {
    gap: 8px;
  }
  .course__detail__accordion__item summary {
    font-size: 15px;
    padding: 12px 10px;
  }
  .course__detail__accordion__chapter {
    padding: 10px 10px;
    gap: 8px;
  }
  .course__detail__accordion__chapter__img img {
    width: 40px !important;
    height: 40px !important;
  }
  .course__detail__accordion__image {
    width: 100%;
  }
  .course__detail__card__header__wrapper {
    padding: 12px;
  }
  .course__detail__card__header {
    padding-bottom: 20px;
  }
}
@media(max-width: 375px)
{
  .course__detail__tag {
    font-size: 13px;
  }
}