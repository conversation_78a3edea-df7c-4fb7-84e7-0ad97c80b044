"use client";
import React, { useState } from "react";

import "./Projects.css";
import Image from "next/image";
import { useRouter } from "next/navigation";
import {
  Box,
  
  Card,
  CardContent,
  
  Chip,
  LinearProgress,
} from "@mui/material";

export default function Projects() {
  const [selected, setSelected] = useState("All projects");
  const [showModal, setShowModal] = useState(false);
  const route = useRouter();

  const handleApplyNowClick = () => {
    route.push("/Project/Volunteer");
  };

  return (
    <>
      <div className="main-container   jjdjjjjjjjj ">
        <div className="flex   items-center hsdjshfhdh">
          <Image
            src="/Img/Volunteering Card.png"
            alt="Volunteering Card"
            className="dshjhsdjadhsdjasjh"
            width={800}
            height={400}
          />
          <div className="flex  flex-col   items-start  flex-nowrap dshjhsdjadhsdjasjh2">
            <div className="flex flex-col items-start ">
              <div className="flex justify-center items-center dshjhsdjadhsdjasjh2_div1">
                Featured course
              </div>
              <div className="flex flex-col gap-[8px] items-start dshjhsdjadhsdjasjh2_div2 ">
                <p className="dshjhsdjadhsdjasjh2_div2_p1">
                  Rebuilding Children`&apos;`s Hospital in Aleppo
                </p>
                <span className="dshjhsdjadhsdjasjh2_div2_p2">
                  This project aims to restore and modernize a critical
                  children`&apos;`s hospital in Aleppo.
                </span>
              </div>
            </div>
            <div className="flex flex-col gap-[20px] items-start dshjhsdjadhsdjasjh3">
              <div className="flex gap-[8px] items-center dshjhsdjadhsdjasjh3_div1">
                <div className="w-[48px] h-[48px] bg-[url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-20/xF9kqh98YP.png)] bg-cover bg-no-repeat " />
                <div className="flex flex-col gap-[2px] items-start ">
                  <p className="h-[24px]   text-[16px] font-medium leading-[24px] text-[#000]  text-left dshjhsdjadhsdjasjh3_div1_P1">
                    Rebuild Syria Network
                  </p>
                  <p className="h-[20px]   text-[14px] font-normal leading-[20px] text-[#2f2f2f] text-left dshjhsdjadhsdjasjh3_div1_P2">
                    Infrastructure & development
                  </p>
                </div>
              </div>
              <div className="flex gap-[12px] items-center  dshjhsdjadhsdjasjh4">
                <div className="flex pt-[16px] pr-[20px] pb-[16px] pl-[20px] gap-[8px] items-center bg-[#d2dad2] rounded-[20px] ">
                  <div>
                    <div className="w-[17.5px] h-[21.5px] bg-[url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-20/iYapmccj0N.png)] bg-cover bg-no-repeat" />
                  </div>
                  <div className="flex flex-col justify-center items-start">
                    <span className="flex justify-center items-center font-['Scandia'] text-[16px] font-medium leading-[24px] text-[#181916]  text-center gsvjxvsvdgvsP">
                      Doctor (3)
                    </span>
                  </div>
                </div>
                <div className="flex pt-[16px] pr-[20px] pb-[16px] pl-[20px] gap-[8px] items-center flex-nowrap bg-[#d2dad2] rounded-[20px]">
                  <div>
                    <div className="w-[20.498px] h-[20.5px] bg-[url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-20/GyQ1zYTHc7.png)] bg-cover bg-no-repeat ml-[1.75px]" />
                  </div>
                  <div className="flex  flex-col justify-center items-start">
                    <span className="flex justify-center items-center font-['Scandia'] text-[16px] font-medium leading-[24px] text-[#181916] text-center gsvjxvsvdgvsP">
                      Accountant (3)
                    </span>
                  </div>
                </div>
                <div className="flex pt-[16px] pr-[20px] pb-[16px] pl-[20px] gap-[8px] items-center bg-[#d2dad2] rounded-[20px]">
                  <div>
                    <div className="w-[21.499px] h-[20.5px] bg-[url(https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-20/KktCfTfCQ9.png)] bg-cover bg-no-repeat  mt-[1.75px]  ml-[1.25px]" />
                  </div>
                  <div className="flex flex-col justify-center items-start ">
                    <span className="flex justify-center items-center  font-['Scandia'] text-[16px] font-medium leading-[24px] text-[#181916] relative text-center gsvjxvsvdgvsP">
                      Project Manager (3)
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex gap-[8px] items-center flex-nowrap   gsvjxvsvdgvsBtn">
              <div 
              onClick={handleApplyNowClick}
              className="flex pt-[16px] pr-[64px] pb-[16px] pl-[64px] gap-[12.317px] justify-center items-center  bg-[#d31b1b] rounded-[80px] gsvjxvsvdgvsBtn cursor-pointer">
                <span className="flex justify-center items-center  font-['Scandia'] text-[14px] font-medium leading-[20px] text-[#fff]  text-center">
                  Apply now
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-start  ndxjswbdjhhbsjhdbxjhsMain">
          <div className="ndxjswbdjhhbsjhdbxjhsMain_1">
            <div className="ndxjswbdjhhbsjhdbxjhsMain_1_1">
              <p className="ndxjswbdjhhbsjhdbxjhsMain_1_1_p">
                Explore Projects
              </p>
              <p className="ndxjswbdjhhbsjhdbxjhsMain_1_1_p2">
                Discover initiatives to rebuild and uplift Syrian communities.
              </p>
            </div>

            <div className="ndxjswbdjhhbsjhdbxjhsMain_1_2">
              <div className="ndxjswbdjhhbsjhdbxjhsMain_1_2__1">
                <div
                  onClick={() => setSelected("All projects")}
                  className={
                    selected === "All projects"
                      ? "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1"
                      : "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2"
                  }
                >
                  All projects
                </div>
                <div
                  onClick={() => setSelected("Education")}
                  className={
                    selected === "Education"
                      ? "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1"
                      : "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2"
                  }
                >
                  Education
                </div>
                <div
                  onClick={() => setSelected("Food")}
                  className={
                    selected === "Food"
                      ? "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1"
                      : "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2"
                  }
                >
                  Food
                </div>
                <div
                  onClick={() => setSelected("Housing")}
                  className={
                    selected === "Housing"
                      ? "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1"
                      : "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2"
                  }
                >
                  Housing
                </div>
                <div
                  onClick={() => setSelected("Voluntering")}
                  className={
                    selected === "Voluntering"
                      ? "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_1"
                      : "ndxjswbdjhhbsjhdbxjhsMain_1_2__1_b2"
                  }
                >
                  Voluntering
                </div>
              </div>
              <div className="ndxjswbdjhhbsjhdbxjhsMain_1_2__2">
                <div className="ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1">
                  <Image
                    src="/Img/projectImg/search-01.png"
                    alt="searchicon"
                    className="bhabdhsser"
                    width={18}
                    height={18}
                    
                  />
                  <input
                    type="text"
                    placeholder="Search"
                    className="ndxjswbdjhhbsjhdbxjhsMain_1_2__2_1_input"
                  />
                </div>
                <div
                  className="ndxjswbdjhhbsjhdbxjhsMain_1_2__2_2"
                  onClick={() => setShowModal(true)}
                >
                  <Image
                    src="/Img/projectImg/filter.png"
                    alt="filtericon"
                    className="bhabdhsser"
                    width={18}
                    height={18}
                  />
                  <span className="ndxjswbdjhhbsjhdbxjhsMain_1_2__2_2_span">
                    Filter
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* <div></div> */}
          <Box
            display="grid"
            gridTemplateColumns={{
              xs: "1fr",
              sm: "repeat(2, 1fr)",
              lg: "repeat(4, 1fr)",
              marginTop: "32px",
            }}
            gap={2}
          >
            {[
              {
                icon: "/Img/orp1.png",
                title: "Safe haven for orphans",
                org: "Syrian Orphan Charity",
                desc: "Building a secure shelter for Syrian orphans with access to education...",
                goal: "25,000 USD",
                image: "/Img/projectImg/1.png",
              },
              {
                icon: "/Img/orp1.png",
                title: "Homes for Hope",
                org: "Syrian Orphan Charity",
                desc: "Reconstructing homes for displaced Syrian families, focusing on sustainab...",
                goal: "400,000 USD",
                image: "/Img/projectImg/2.png",
              },
              {
                icon: "/Img/orp2.png",
                title: "Schools of Tomorrow",
                org: "Green Syria Initiative",
                desc: "Establishing schools with essential learning materials for war-affected...",
                goal: "200,000 USD",
                image: "/Img/projectImg/3.png",
              },
              {
                icon: "/Img/orp3.png",
                title: "Mobile clinics for rural areas",
                org: "Women for Change",
                desc: "Providing urgent medical aid and health screenings in underserved...",
                image: "/Img/projectImg/4.png",
                tags: ["Doctor", "Nurse", "+5 More"],
              },
              {
                icon: "/Img/orp1.png",
                title: "Skills for change",
                org: "Syrian Orphan Charity",
                desc: "Vocational training for Syrian women to rebuild lives and gain financial...",
                goal: "25,000 USD",
                image: "/Img/projectImg/5.png",
              },
              {
                icon: "/Img/orp1.png",
                title: "Reforest Syria",
                org: "Syrian Orphan Charity",
                desc: "Restoring forests and green spaces damaged by war.",
                goal: "400,000 USD",
                image: "/Img/projectImg/6.png",
              },
              {
                icon: "/Img/orp2.png",
                title: "Stories of Syria",
                org: "Green Syria Initiative",
                desc: "Documenting personal stories of resilience to raise awareness and...",
                image: "/Img/projectImg/7.png",
                tags: ["Photographer", "Journalist", "+3 More"],
              },
              {
                icon: "/Img/orp3.png",
                title: "Digital bridges project",
                org: "Women for Change",
                desc: "Providing tech education and digital tools to Syrian youth.",
                image: "/Img/projectImg/8.png",
                tags: ["Security engineer", "+2 More"],
              },
            ].map((proj, i) => (
              <Card
                key={i}
                sx={{
                  borderRadius: "20px",
                  border: "1px solid #0000000A",
                  boxShadow: "4px 12px 24px 0px #0000000A",
                  padding: "20px",
                  cursor: "pointer",
                }}
                onClick={() => route.push("/Project/ProjectDetail")} 
              >
                <Image
                  src={proj.image}
                  alt={proj.title}
                  width={400}
                  height={300}
                  style={{
                    width: "100%",
                    height: 160,
                    objectFit: "cover",
                    borderRadius: "12px",
                  }}
                />
                <CardContent sx={{ padding: "16px 0px" }}>
                  <div className="vjsghvjhsgv">
                    <Image
                      src={proj?.icon}
                      alt=""
                      height={24}
                      width={24}
                    />
                    <p
                      className="font-[500] text-[12px] leading-[15px] tracking-[0%] text-[#000000] m-[0px]"
                      style={{ fontFamily: "ScandiaMedium" }}
                    >
                      {proj.org}
                    </p>
                  </div>
                  <p
                    className="font-[500] leading-[32px] tracking-[0%] text-[#000000] m-[0px] okjyyaa"
                    style={{ fontFamily: "ScandiaMedium" }}
                  >
                    {proj.title}
                  </p>
                  <p
                    className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mt-[8px] m-[0px]"
                    style={{ fontFamily: "Scandia" }}
                  >
                    {proj.desc}
                  </p>

                  {[3, 6, 7].includes(i) ? null : (
                    <Box className="flex  flex-col items-start bg-[#ECE7E3] rounded-[12px] py-[10px] px-[16px] mt-[16px]">
                      <div className="flex items-center justify-between w-full">
                        <p
                          className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#181916]  m-[0px]"
                          style={{ fontFamily: "Scandia" }}
                        >
                          Fundraising goal
                        </p>
                        <p
                          className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px]"
                          style={{ fontFamily: "ScandiaMedium" }}
                        >
                          {proj.goal}
                        </p>
                      </div>
                      <LinearProgress
                        variant="determinate"
                        value={25}
                        sx={{
                          mt: 2,
                          height: 12,
                          borderRadius: 5,
                          width: "100%",
                          backgroundColor: "#D9D3CD",
                          "& .MuiLinearProgress-bar": {
                            backgroundColor: "#2F3B31",
                          },
                        }}
                      />
                    </Box>
                  )}

                  {proj.tags && (
                    <Box mt={2} display="flex" flexWrap="wrap" gap={1}>
                      {proj.tags.map((tag, i) => (
                        <Chip key={i} label={tag} size="small" />
                      ))}
                    </Box>
                  )}
                </CardContent>
              </Card>
            ))}
          </Box>
        </div>
        <p className="lasthbvsjabvhjx">Load more</p>
      </div>
      {/* Modal */}
      {showModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="filter-buttons">
              <h2 className="filter_buttons_p1">Filter project</h2>
              <div className="filter_buttonsbtnDiv">
                <button className="reset-btn">
                  <Image
                    src="/Img/projectImg/fillter.png"
                    alt="reset"
                    className="restimg"
                  />
                  Reset filter
                </button>
                <button className="apply-btn">Apply filter</button>
              </div>
            </div>
            <div className="filter-sections">
              <div className="filtersection1">
                <h4 className="filtersection1_p">By type</h4>
                <div className="filtersection1_div">
                  <p className="filtersection1_div_p">Donation</p>
                  <p className="filtersection1_div_p">Volunteering</p>
                </div>
              </div>
              <div className="filtersection2">
                <h4 className="filtersection1_p">By category</h4>
                <div className="filtersection1_div">
                  <p className="filtersection1_div_p">Education</p>
                  <p className="filtersection1_div_p">Food</p>
                  <p className="filtersection1_div_p">Housing</p>
                  <p className="filtersection1_div_p">Health</p>
                  <p className="filtersection1_div_p">Welfare</p>
                  <p className="filtersection1_div_p">Orphans</p>
                  <p className="filtersection1_div_p">Women</p>
                  <p className="filtersection1_div_p">Tech</p>
                  <p className="filtersection1_div_p">Advocation</p>
                </div>
              </div>
              <div className="filtersection3">
                <h4 className="filtersection1_p">By city</h4>
                <div className="filtersection1_div">
                  <p className="filtersection1_div_p">
                    <strong>Damascus</strong> ✓
                  </p>
                  <p className="filtersection1_div_p">
                    <strong>Aleppo</strong> ✓
                  </p>
                  <p className="filtersection1_div_p">Homs</p>
                  <p className="filtersection1_div_p">Hama</p>
                  <p className="filtersection1_div_p">Latakia</p>
                  <p className="filtersection1_div_p">Tartus</p>
                  <p className="filtersection1_div_p">Raqqa</p>
                  <p className="filtersection1_div_p">Idlib</p>
                  <p className="filtersection1_div_p">Daraa</p>
                </div>
              </div>
            </div>

            <button className="close-btn" onClick={() => setShowModal(false)}>
              ×
            </button>
          </div>
        </div>
      )}
    </>
  );
}
