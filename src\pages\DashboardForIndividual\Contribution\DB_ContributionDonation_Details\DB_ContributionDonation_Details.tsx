// DB_ContributionDonation.tsx
import React, {  } from "react";
import "../DB_ContributionDonation/DB_ContributionDonation.css";
import "./DB_ContributionDonation_Details.css";
import Image from "next/image";

const DB_ContributionDonation_Details = () => {
  return (
    <div className="hdjsbdjbsjhDB_db">
      {/* <CommunityMain/> */}
      <div className="hdhhdhdhh"></div>
      <div className=" max-w-[960px] DB_ContributionDonation_Main2">
        <p className="DB_ContributionDonation_Main2hfdbjhfbdnew">
          <span>Contribution</span>
          <Image
            src={"/Images/contribution/arrowright.png"}
            alt=""
            width={24}
            height={24}
          />
          <span>Transaction details</span>
        </p>
        <div className=" hbdjbjshbjshbsetetettetx">
          <p className="hbdjbjshbjshbsetetettetxtdd">Transaction details</p>
          <div className="DB_ContributionDonation_Details_MainDiv1">



          </div>

          {/* .... */}
        </div>
      </div>
    </div>
  );
};

export default DB_ContributionDonation_Details;
