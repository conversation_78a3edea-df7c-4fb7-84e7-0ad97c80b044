import Button from "@/components/button/Button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

function CreateNewPassMob() {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();

  const validatePassword = (password: string) => {
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 8;
    return hasLetter && hasNumber && hasSpecialChar && isLongEnough;
  };

  const handleNewPass = () => {
    setError(""); // reset error first

    if (!newPassword || !confirmPassword) {
      setError("Please fill in both fields.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (!validatePassword(newPassword)) {
      setError(
        "Password must contain at least one letter, one number, and one special character."
      );
      return;
    }
    // If all checks pass, redirect
    router.push("/Auth/SignIn/new-password-confrim");
  };
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
        <div className="flex flex-col gap-[20px]">
          <p className="text-[22px] leading-[32px] font-[scandiaMedium] text-[black]">
            Create new password
          </p>
        </div>
        <div>
          <form
            className="space-y-[52px] md:space-y-[24px]"
            onSubmit={(e) => {
              e.preventDefault();
              handleNewPass();
            }}
          >
            <div className="flex flex-col gap-[32px]">
              <div className="space-y-[8px] flex flex-col">
                <label
                  htmlFor="email"
                  className=" text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
                  style={{ color: "rgba(48, 48, 48, 1)" }}
                >
                  New password
                </label>
                <input
                  id="new-password"
                  type="password"
                  placeholder="Enter password"
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  style={{
                    borderTop: "none",
                    borderLeft: "none",
                    borderRight: "none",
                    borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                    color: "rgba(48, 48, 48, 1)",
                    paddingTop: "0px",
                    paddingBottom: "8px",
                  }}
                  className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
                />
              </div>

              <div className="flex flex-col gap-[8px]">
                <label
                  htmlFor="password"
                  className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
                  style={{ color: "rgba(48, 48, 48, 1)" }}
                >
                  Confirm new password
                </label>
                <input
                  id="confirm-password"
                  type="password"
                  placeholder="Enter password"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  style={{
                    borderTop: "none",
                    borderLeft: "none",
                    borderRight: "none",
                    borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                    color: "rgba(48, 48, 48, 1)",
                    paddingTop: "0px",
                    paddingBottom: "8px",
                  }}
                  className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
                />
                {error && (
                  <p
                    style={{ color: "#D31B1B" }}
                    className="text-sm text-left font-[scandiaMedium] text-center"
                  >
                    {error}
                  </p>
                )}
              </div>
            </div>

            <div className="flex flex-col gap-[24px] justify-center text-center">
              <Button type="submit">
                {/* <div onClick={handleNewPass} > */}
                <p className="text-[14px] text-[white] font-[scandiaMedium]">
                  Continue
                </p>
                {/* </div> */}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default CreateNewPassMob;
