import Image from "next/image";
import React from "react";

function Createpost() {
  return (
    <div className="h-[100vh] flex flex-col gap-[16px] mt-[100px] bg-[white] text-[black] py-[24px] px-[20px]">
      <div className="flex flex-row items-center justify-between">
        <p className="text-[22px] leading-[32px] font-[scandiaMedium]">
          Create post
        </p>
        <button className="bg-[#D31B1B] text-[black] cursor-pointer items-center py-[12px] px-[20px] rounded-[80px]">
          <p className="text-[white] whitespace-nowrap text-[14px] leading-[20px] font-[scandiaMedium]">
            Post
          </p>
        </button>
      </div>
      <div className="border flex flex-col justify-between border-[#ECE7E3] rounded-[12px] h-[626px] p-[20px]">
        {/* <div className="flex gap-[16px] w-[100%]"> */}
        <textarea
          placeholder="Write something"
          className="w-full border border-none px-[24px] py-[8.5px] text-base placeholder-[#23232366] focus:outline-none focus:ring-[#23232366] h-[200px] resize-none"
        />

        {/* </div> */}
        <div className="flex items-center max-[375px]:flex-wrap gap-[8px]">
          <button className="bg-[#ECE7E3] cursor-pointer w-full justify-center flex gap-[6px] text-[black] items-center py-[13px] px-[20px] rounded-[70px]">
            <Image
              src="/Images/community/image-01.svg"
              alt="profile"
              height={18}
              width={18}
            />
            <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
              Add photo{" "}
            </p>
          </button>
          <button className="bg-[#ECE7E3] cursor-pointer w-full justify-center flex gap-[6px] text-[black] items-center py-[13px] px-[20px] rounded-[70px]">
            <Image
              src="/Images/community/video-02.svg"
              alt="profile"
              height={18}
              width={18}
            />
            <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
              Add video{" "}
            </p>
          </button>
        </div>
      </div>
    </div>
  );
}

export default Createpost;
