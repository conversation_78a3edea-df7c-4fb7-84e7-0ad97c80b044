"use client";
import { FormControlLabel, Radio, RadioGroup } from "@mui/material";
import { useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import Image from "next/image";

interface QuestionType {
  title: string;
  option: {
    a: string;
    b: string;
    c?: string;
    d?: string;
  };
  correctAnswer?: string;
}

interface QuizType {
  title: string;
  questions: QuestionType[];
  percentageGained?: number;
}

interface QuestionProps {
  question: QuestionType;
  index: number;
  setAnswer: (index: number, answer: string) => void;
  selected?: string;
  completed: boolean;
}

const Question = ({
  question,
  index,
  setAnswer,
  selected = "",
  completed
}: QuestionProps) => {
  const [selectedOption, setSelectedOption] = useState<string>(selected);

  useEffect(() => {
    setSelectedOption(selected);
  }, [selected]);

  const handleChange = (value: string) => {
    setSelectedOption(value);
    setAnswer(index, value);
  };

  return (
    <div className="flex flex-col gap-[10px]">
      <div>
        {Object.keys(question?.option || {}).length === 2 && (
          <p className="font-[ScandiaMedium] text-[#232323] text-[14px]">
            True or False:
          </p>
        )}
        <p className="font-[ScandiaMedium] text-[#232323] text-[14px]">
          {question?.title}
        </p>
      </div>

      <RadioGroup
        value={selectedOption}
        onChange={(e) => handleChange(e.target.value)}
      >
        <div className="flex flex-col gap-[10px]">
          {Object.entries(question?.option || {}).map(
            ([, value], index) => (
              <FormControlLabel
                key={index}
                value={value}
                control={
                  <Radio
                    sx={{
                      color: "#303030",
                      "&.Mui-checked": {
                        color: "#181916",
                      },
                      width: 20,
                      height: 20,
                      padding: "2px",
                    }}
                    icon={
                      <Image 
                        src="/Images/quiz/unChecked.png" 
                        alt="unchecked"
                        width={20}
                        height={20}
                      />
                    }
                    checkedIcon={
                      <Image 
                        src="/Images/quiz/Checked.png" 
                        alt="checked"
                        width={20}
                        height={20}
                      />
                    }
                    disabled={completed}
                  />
                }
                label={
                  <span className="font-[Scandia] text-[#303030] text-[14px] ml-[10px]">
                    {value}
                  </span>
                }
                sx={{ alignItems: "center", gap: "8px", margin: 0 }}
              />
            )
          )}
        </div>
      </RadioGroup>
    </div>
  );
};

interface MainSectionProps {
  quiz: QuizType;
  completed: boolean;
  chapterIndex: number;
  defaultAnswers: string[];
  onSubmit: (
    chapterIndex: number,
    answers: string[],
    percentage: number
  ) => void;
  onRetake: () => void;
}
const MainSection = ({
  quiz,
  completed,
  chapterIndex,
  defaultAnswers,
  onSubmit,
  onRetake,
}: MainSectionProps) => {
  const [answers, setAnswers] = useState<string[]>(
    defaultAnswers.length
      ? defaultAnswers
      : Array(quiz?.questions.length).fill("")
  );
  const router = useRouter();

  const correctAnswers = quiz?.questions.map((q) => q.correctAnswer);
  const setAnswer = (index: number, answer: string) => {
    const updatedAnswers = [...answers];
    updatedAnswers[index] = answer;
    setAnswers(updatedAnswers);
  };

  const calculateScore = () => {
    if (answers.every((a) => a !== "")) {
      const correctCount = answers.reduce((acc, answer, index) => {
        return answer === correctAnswers[index] ? acc + 1 : acc;
      }, 0);
      const percentage = ((correctCount / correctAnswers.length) * 100).toFixed(
        2
      );
      onSubmit(chapterIndex, answers, Number(percentage));
    } else {
      toast.error("Please complete all the questions to submit the quiz");
    }
  };

  return (
    <div className="max-w-[960px] bg-white rounded-[20px] py-6 px-3 md:py-10 md:px-8 flex flex-col gap-[28px] lg:gap-[40px]">
      {!completed ? (
        // answers.some((a) => a === "") && (
          <div className="flex flex-col gap-1">
            <p className="font-[ScandiaMedium] text-[16px] md:text-[22px] text-[#181916]">
              {quiz?.title}
            </p>
            <p className="font-[ScandiaMedium] text-[12px] md:text-[16px] text-[#181916]">
              The power of storytelling in social impact
            </p>
          </div>
        // )
      ) : (
        <div className="flex flex-col gap-[40px]">
          <div className="py-[60px] lg:py-[74px] px-4 lg:px-16  flex flex-col gap-6 items-center justify-center bg-[#F8F8F8] rounded-xl">
            <Image
              src="/Images/quiz/completed.png"
              alt="win"
              width={64}
              height={64}
              className="h-16 w-16"
            />
            <div className="flex flex-col gap-3 items-center">
              <span className="flex items-center flex-col justify-center gap-1">
                <p className="font-[ScandiaMedium] text-[32px] text-[#181916]">
                  {quiz?.percentageGained ?? 0}
                </p>
                <p className="font-[ScandiaMedium] text-[22px] md:text-[28px] lg:text-[32px] text-[#181916]">
                  {(quiz?.percentageGained ?? 0) === 100
                    ? "Perfect Score!"
                    : (quiz?.percentageGained ?? 0) < 100 &&
                      (quiz?.percentageGained ?? 0) >= 60
                    ? "Great Score!"
                    : (quiz?.percentageGained ?? 0) < 60 &&
                      (quiz?.percentageGained ?? 0) >= 30
                    ? "Average Score!"
                    : "You can Do better"}
                </p>
              </span>
              <p className="font-[Scandia] text-[12px] md:text-[14px] text-[#303030] text-center">
                {(quiz?.percentageGained ?? 0) === 100
                  ? "Your understanding of storytelling for social impact is right on point."
                  : (quiz?.percentageGained ?? 0) < 100 && (quiz?.percentageGained ?? 0) >= 60
                  ? "Your understanding of storytelling for social impact is almost on point."
                  : (quiz?.percentageGained ?? 0) < 60 && (quiz?.percentageGained ?? 0) >= 30
                  ? "Your understanding of storytelling for social impact can be improved."
                  : "Your understanding of storytelling for social impact needs to be imporved, retake quiz!"}
              </p>
            </div>
            <div className="flex gap-3 flex-col md:flex-row ">
              <button
                onClick={onRetake}
                className="py-4 px-6 rounded-full text-[#181916] font-[ScandiaMedium] text-[12px] lg:text-[14px] cursor-pointer border border-[#181916]"
              >
                Retake quiz
              </button>
              <button
                onClick={() => router.push("/course-detail")}
                className="py-4 px-6 rounded-full text-white font-[ScandiaMedium] text-[12px] md:text-[14px] cursor-pointer bg-[#D31B1B]"
              >
                Back to Course
              </button>
            </div>
          </div>
          <p className="font-[ScandiaMedium] text-[22px] text-[#181916]">
            See Results:
          </p>
        </div>
      )}

      <div className="flex flex-col gap-[22px]">
        {quiz?.questions?.map((question, index) => (
          <div key={index} className="flex flex-col gap-[22px]">
            <Question
              question={question}
              index={index}
              setAnswer={setAnswer}
              selected={answers[index]} // pass selected answer
              completed={completed}
            />
            {index !== quiz?.questions?.length - 1 && (
              <div className="w-full h-[1px] bg-[#ECE7E3]"></div>
            )}
          </div>
        ))}
      </div>

      {!completed && (
        <button
          onClick={calculateScore}
          className="bg-[#D31B1B] px-6 py-4 font-[ScandiaMedium] text-[14px] text-white rounded-full w-fit cursor-pointer"
        >
          Submit Quiz
        </button>
      )}
    </div>
  );
};

export default MainSection;
