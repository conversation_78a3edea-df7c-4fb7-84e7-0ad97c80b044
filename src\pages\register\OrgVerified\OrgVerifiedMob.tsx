'use client";'

import Button from "@/components/button/Button"
import Image from "next/image"

function OrgVerifiedMob() {
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
    <div className="flex flex-col items-center gap-[24px]">
      <div>
        <Image
          src="/Images/signin/elements.svg"
          alt="lock"
          width={40}
          height={40}
        />
      </div>
      <div className="flex flex-col gap-[8px]">
        <p className="text-[22px] leading-[32px] font-[scandiaMedium] text-[black] text-center">
          Your organization email has been successfully verified
        </p>
        <p className="text-[14px] font-[scandia] text-[black] text-center">
          Verify your organization and complete the registration process
        </p>
      </div>
      <div className="flex flex-col gap-[12px]">
        <Button>
          <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[white]">
            Complete registration
          </p>
        </Button>
      </div>
    </div>
      </div>
    </div>
  )
}

export default OrgVerifiedMob
