'use client';
// Remove unused imports
// import Button from "@/components/button/Button";
// import React, { useEffect, useState } from "react";
// import { Bell, Menu, ChevronDown } from "lucide-react";
import React from "react";
import SigninMobForm from "./SigninMobForm";

interface SignInMobProps {
  active: "signIn" | "register";
  setActive: React.Dispatch<React.SetStateAction<"signIn" | "register">>;
}

function SignInMob({ active, setActive }: SignInMobProps) {
  return (
    <SigninMobForm active={active} setActive={setActive} />
  );
}

export default SignInMob;
