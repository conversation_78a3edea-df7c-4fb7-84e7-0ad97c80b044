import Image from "next/image";

type YesNoSelectorProps = {
  title: string;
  question: string;
  value: string;
  onChange: (value: string) => void;
};

export default function YesNoSelector({ title, question, value, onChange }: YesNoSelectorProps) {
  return (
    <div className="flex flex-col gap-[12px]">
      <p className="font-[scandiaMedium] font-medium max-[390px]:text-[14px] text-[16px] leading-[24px] text-[#181916]">
        {title}
      </p>

      <div className="flex flex-col gap-[8px]">
        <p className="font-[scandiaMedium] font-medium max-[390px]:text-[12px] text-[14px] leading-[20px] text-[#232323]">
          {question}
        </p>

        <div className="flex gap-[8px] cursor-pointer" onClick={() => onChange("yes")}>
          <Image
            src={
              value === "yes"
                ? "/Images/registration/tickSquare.svg"
                : "/Images/registration/emptybox.svg"
            }
            alt="checkbox"
            width={24}
            height={24}
          />
          <p className="font-[scandia] text-[14px] leading-[20px] text-[#303030]">Yes</p>
        </div>

        <div className="flex gap-[8px] cursor-pointer" onClick={() => onChange("no")}>
          <Image
            src={
              value === "no"
                ? "/Images/registration/tickSquare.svg"
                : "/Images/registration/emptybox.svg"
            }
            alt="checkbox"
            width={24}
            height={24}
          />
          <p className="font-[scandia] text-[14px] leading-[20px] text-[#303030]">No</p>
        </div>
      </div>
    </div>
  );
}
