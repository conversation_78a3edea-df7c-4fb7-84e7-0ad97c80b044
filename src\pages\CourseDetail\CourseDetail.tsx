import React, { useState } from "react";
import Image from "next/image";
import Navbar from "../../components/navbar/Navbar";
import StoryTeller from "@/components/StoryTeller/StoryTeller";
import {
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Typography,
  Box,
} from "@mui/material";
import "./CourseDetail.css";
import Breadcrumbs from "@/components/breadcrumbs/Breadcrumbs";

// Course content data structure
const courseContent = [
  {
    title: "Introduction to fundraising",
    chapters: [
      {
        number: "Chapter 1",
        title: "The power of storytelling in social impact",
        preview: "Preview available",
        duration: "10:00",
        image: "/Images/coursedetail/chapter1.jpg",
      },
      {
        number: "Chapter 2",
        title: "Building a compelling narrative",
        duration: "12:23",
        image: "/Images/coursedetail/chapter2.jpg",
      },
      {
        number: "Chapter 3",
        title: "Delivering your story for maximum impact",
        duration: "09:46",
        image: "/Images/coursedetail/chapter3.jpg",
      },
    ],
  },
  {
    title: "Developing a fundraising strategy",
    chapters: [],
  },
  {
    title: "Donor engagement and stewardship",
    chapters: [],
  },
  {
    title: "Fundraising channels and tools",
    chapters: [],
  },
];

function CourseDetail() {
  const [showStoryTeller, setShowStoryTeller] = useState(false);

  const handleStoryTellerClick = () => {
    setShowStoryTeller(true);
  };

  return (
    <div className="course__detail__main__container">
      <Navbar />
      {/* <div className="course__detail__container__1" >
        <p className="course__detail__container__1__p ">Learn</p>
        <Image
          src="/Images/coursedetail/arrow.svg"
          width={24}
          height={24}
          alt="arrow"
        />
        <p
          style={{ cursor: "pointer" }}
          onClick={handleStoryTellerClick}
          className="course__detail__container__1__p "
        >
          Storytelling for social impact
        </p>
      </div> */}
      <Breadcrumbs />
      {showStoryTeller ? (
        <StoryTeller />
      ) : (
        <>
          <div className="course__detail__container__2">
            <div className="course__detail__card__container__top">
              {/* Card Header */}
              <div className="course__detail__card__header__wrapper">
                <div className="course__detail__card__header">
                  <div className="course__detail__card__avatar__wrapper">
                    <Image
                      src="/Images/coursedetail/syreaicon.svg"
                      width={48}
                      height={48}
                      alt="syreaicon"
                    />
                  </div>
                  <div className="course__detail__card__header__info">
                    <p className="course__detail__card__org">
                      Syrian Voices Alliance
                    </p>
                    <p className="course__detail__card__category">
                      Advocacy & media
                    </p>
                  </div>
                </div>
                {/* Details Section */}
                <div className="course__detail__card__details">
                  <p className="course__detail__card__details__title">
                    Details
                  </p>
                  <div className="course__detail__card__details__content">
                    <div className="course__detail__card__details__item">
                      <Image
                        src="/Images/coursedetail/studenticon.svg"
                        width={24}
                        height={24}
                        alt="studenticon"
                      />
                      <div className="course__detail__card__details__item__info">
                        <p className="course__detail__card__details__item__label">
                          Students
                        </p>
                        <p className="course__detail__card__details__item__value">
                          1,245 students
                        </p>
                      </div>
                    </div>
                    <div className="course__detail__card__details__item">
                      <Image
                        src="/Images/coursedetail/globe.svg"
                        width={24}
                        height={24}
                        alt="globeicon"
                      />
                      <div className="course__detail__card__details__item__info">
                        <p className="course__detail__card__details__item__label">
                          Language
                        </p>
                        <p className="course__detail__card__details__item__value">
                          English, Arabic
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Course Preview */}
                <div className="course__detail__card__preview__section">
                  <div className="course__detail__card__preview__title">
                    Course preview
                  </div>
                  <div className="course__detail__card__preview__video__wrapper">
                    <video
                      src="/Images/coursedetail/video.mp4"
                      width={416}
                      height={280}
                      className="course__detail__card__preview__video"
                      controls
                      poster="/Images/coursedetail/preview.jpg"
                    >
                      Your browser does not support the video tag.
                    </video>
                  </div>
                </div>
                {/* Take Course Button */}
                <button className="course__detail__card__button">
                  Take course
                </button>
              </div>
              <div className="course__detail__card__container__bottom">
                <div className="course__detail__card__container__bottom__left">
                  <p className="course__detail__card__container__bottom__left__title">
                    Storytelling for Social Impact
                  </p>
                  <Image
                    src="/Images/coursedetail/social.svg"
                    width={24}
                    height={24}
                    alt="socialIcon"
                  />
                </div>
                {/* Course Details Section - Start */}
                <div className="course__detail__info__wrapper">
                  {/* Tags */}
                  <div className="course__detail__tags">
                    <div className="course__detail__tag">
                      <Image
                        src="/Images/coursedetail/socialtick.svg"
                        width={20}
                        height={20}
                        alt="socialtick icon"
                      />
                      Storytelling
                    </div>
                    <div className="course__detail__tag">
                      <Image
                        src="/Images/coursedetail/socialtick.svg"
                        width={20}
                        height={20}
                        alt="socialtick icon"
                      />
                      Advocacy & Awareness
                    </div>
                    <div className="course__detail__tag">
                      <Image
                        src="/Images/coursedetail/socialtick.svg"
                        width={20}
                        height={20}
                        alt="socialtick icon"
                      />
                      Narrative Strategy
                    </div>
                  </div>
                  {/* What you will learn */}
                  <div className="course__detail__learn">
                    <h2 className="course__detail__learn__title">
                      What you will learn
                    </h2>
                    <p className="course__detail__learn__desc">
                      Explore the significance of fundraising in driving support
                      for various organizations and causes. Delve into the
                      ethical aspects and the necessity for transparency in
                      fundraising efforts. Gain insights into what motivates
                      donors to contribute and how to effectively engage them.
                    </p>
                  </div>
                  {/* Course Features */}
                  <div className="course__detail__features__wrapper">
                    <p className="course__detail__features__title">
                      This course include
                    </p>
                    <div className="course__detail__features__content__1">
                      <div className="course__detail__features">
                        <div className="course__detail__feature">
                          <Image
                            src="/Images/coursedetail/youtube.svg"
                            width={24}
                            height={24}
                            alt="youtube icon"
                          />
                          <p className="course__detail__feature__desc">
                            4 hours on-demand video
                          </p>
                        </div>
                        <div className="course__detail__feature">
                          <Image
                            src="/Images/coursedetail/download.svg"
                            width={24}
                            height={24}
                            alt="download icon"
                          />
                          <p className="course__detail__feature__desc">
                            1 downloadable resources
                          </p>
                        </div>
                      </div>
                      <div className="course__detail__features">
                        <div className="course__detail__feature">
                          <Image
                            src="/Images/coursedetail/article.svg"
                            width={24}
                            height={24}
                            alt="article icon"
                          />
                          <p className="course__detail__feature__desc">
                            3 articles
                          </p>
                        </div>
                        <div className="course__detail__feature">
                          <Image
                            src="/Images/coursedetail/certificate.svg"
                            width={24}
                            height={24}
                            alt="certificate icon"
                          />
                          <p className="course__detail__feature__desc">
                            Certificate of completion
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  {/* Course Content Accordion */}
                  <div className="course__detail__accordion__wrapper">
                    <p className="course__detail__accordion__title">
                      Course content
                    </p>

                    <div className="course__detail__accordion">
                      {courseContent.map((section, index) => (
                        <Accordion onClick={handleStoryTellerClick}
                          key={index}
                          defaultExpanded={index === 0}
                          sx={{
                            backgroundColor: "white",
                            padding: { xs: "16px 15px", sm: "24px 32px" },
                            border: "1px solid #0000000A",
                            borderRadius: "20px",
                            boxShadow: "4px 12px 24px 0px #0000000A",
                            "&.MuiAccordion-root:first-of-type": {
                              borderTopLeftRadius: "20px",
                              borderTopRightRadius: "20px",
                            },
                            "&.MuiAccordion-root:last-of-type": {
                              borderBottomLeftRadius: "20px",
                              borderBottomRightRadius: "20px",
                            },
                            "&:before": {
                              display: "none",
                            },
                            "& .MuiAccordionSummary-root": {
                              padding: 0,
                              minHeight: "unset",
                            },
                            "& .MuiAccordionSummary-content": {
                              margin: 0,
                            },
                            "& .MuiAccordionDetails-root": {
                              padding: "16px 0",
                            },
                          }}
                        >
                          <AccordionSummary
                            expandIcon={
                              <Image
                                src="/Images/coursedetail/expandmore.svg"
                                width={20}
                                height={20}
                                alt="expand icon"
                              />
                            }
                          >
                            <Typography
                              sx={{
                                fontFamily: "ScandiaMedium",
                                fontWeight: 500,
                                fontSize: {
                                  xs: "13px",
                                  sm: "16px",
                                },
                                lineHeight: "24px",

                                verticalAlign: "middle",
                                color: "#303030",
                              }}
                            >
                              {section.title}
                            </Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            {section.chapters.map((chapter, chapterIndex) => (
                              <Box
                                key={chapterIndex}
                                sx={{
                                  display: "flex",
                                  alignItems: "center",
                                  gap: "20px",
                                  padding: "16px 0",
                                  flexDirection: {
                                    xs: "column",
                                    sm: "row",
                                  },
                                }}
                              >
                                <Image
                                  className="course__detail__accordion__image"
                                  src={
                                    chapterIndex === 0
                                      ? "/Images/coursedetail/videoImage.svg"
                                      : "/Images/coursedetail/videoLock.svg"
                                  }
                                  width={128}
                                  height={80}
                                  alt={`Chapter ${chapterIndex + 1}`}
                                />
                                <Box
                                  sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    width: "100%",
                                  }}
                                >
                                  <Box
                                    sx={{
                                      display: "flex",
                                      flexDirection: "column",
                                      gap: "4px",
                                    }}
                                  >
                                    <Typography
                                      sx={{
                                        fontFamily: "ScandiaMedium",
                                        fontWeight: 500,
                                        fontSize: "12px",
                                        lineHeight: "16px",
                                        verticalAlign: "middle",
                                        color: "#D31B1B",
                                      }}
                                    >
                                      {chapter.number}
                                    </Typography>
                                    <Typography
                                      sx={{
                                        fontFamily: "ScandiaMedium",
                                        fontWeight: 500,
                                        fontSize: "16px",
                                        lineHeight: "24px",
                                        verticalAlign: "middle",
                                        color: "#181916",
                                      }}
                                    >
                                      {chapter.title}
                                    </Typography>
                                    {chapter.preview && (
                                      <Typography
                                        sx={{
                                          fontFamily: "Scandia",
                                          fontWeight: 400,
                                          fontSize: "12px",
                                          lineHeight: "16px",
                                          verticalAlign: "middle",
                                          color: "#303030",
                                        }}
                                      >
                                        {chapter.preview}
                                      </Typography>
                                    )}
                                  </Box>
                                  <Typography
                                    sx={{
                                      fontFamily: "Scandia",
                                      fontWeight: 400,
                                      fontSize: "14px",
                                      lineHeight: "20px",
                                      verticalAlign: "middle",
                                      color: "#303030",
                                    }}
                                  >
                                    {chapter.duration}
                                  </Typography>
                                </Box>
                              </Box>
                            ))}
                          </AccordionDetails>
                        </Accordion>
                      ))}
                    </div>
                  </div>
                </div>
                {/* Course Details Section - End */}
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}

export default CourseDetail;
