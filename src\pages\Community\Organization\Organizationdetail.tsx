/* eslint-disable */
"use client";
import React, { useState } from "react";
import ProjectCard from "@/components/Community-detail-project-card/projectCard";
import RoleCard from "@/components/Community-detail-project-card/roleCard";
import Image from "next/image";
import "../community.css";

// interface OrganizationDetailProps {
//   organization: {
//     id: string;
//     name: string;
//     title: string;
//     imageUrl: string;
//   };
//   onBack: () => void;
//   onNavigate: (page: "list" | "details" | "donate" | "confirm" | "submit") => void;

// }
const buttonsGroup1 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
const buttonsGroup2 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function OrganizationDetail({ organization, onBack, onNavigate }: any) {
  const [activeIndexGroup2, setActiveIndexGroup2] = useState<number | null>(
    null
  );
  const [activeIndexGroup1, setActiveIndexGroup1] = useState<number | null>(
    null
  );
  return (
    <div className="flex flex-col gap-[24px] ">
      <div className="sm:flex hidden gap-[8px] items-center">
        <p
          onClick={onBack}
          className="font-[scandiaMedium] cursor-pointer text-[#303030] text-[14px] leading-[20px]"
        >
          Organization
        </p>
        <Image
          src="/Images/CommunityMain/arrowRight.svg"
          alt="back arrow"
          width={20}
          height={20}
        />
        <p className="font-[scandiaMedium] cursor-pointer text-[#2F3B31] text-[14px] leading-[20px]">
          {organization?.name}
        </p>
      </div>
      <div className="sm:rounded-[20px] rounded-[0px] bg-[#FFFFFF] 2xl:max-w-[1400px] maintopdiv shadow-[4px_12px_24px_0px_rgba(0,0,0,0.04)] max-[1000px]:shadow-none">
        <Image
          className="backgroundOrganizationcommunitydetail"
          src="/Images/CommunityMain/org-detail/org-bg.png"
          alt="OrgBg"
          width={1200}
          height={400}
          style={{
            objectFit: "cover",
            borderTopRightRadius: "20px",
            borderTopLeftRadius: "20px",
          }}
        />
        <div className="flex flex-col gap-[40px] sm:p-[32px] p-[0px]">
          <div className="flex flex-col gap-[16px]">
            <div className="flex flex-col gap-[18px] mt-[-100px] max-[769px]:mt-[-80px] max-[640px]:mt-[-65px]">
              <Image
                className="organizationLogo-detailorg max-[640px]:ml-[16px]"
                style={{
                  border: "1px solid #ECE7E3",
                  borderRadius: "100%",
                  height: "120px",
                  width: "120px",
                }}
                src={organization?.imageUrl}
                alt="Image"
                width={120}
                height={120}
              />
              <div className="w-full flex justify-between max-[1140px]:flex-col max-[1140px]:gap-[20px]">
                <div className="flex flex-col gap-[8px]">
                  <div className="flex gap-[12px] items-center">
                    <p className="font-[scandiaMedium] text-[#000000] sm:text-[22px] text-[16px] sm:leading-[32px] leading-[24px]">
                      {organization?.name}
                    </p>
                    <div
                      className="rounded-[60px] px-[12px] py-[4px]"
                      style={{ border: "1px solid rgba(35, 35, 35, 0.4)" }}
                    >
                      <p
                        className="font-[scandiaMedium] text-[10px] sm:text-[12px] leading-[16px] "
                        style={{ color: "rgba(35, 35, 35, 0.4)" }}
                      >
                        Following
                      </p>
                    </div>
                  </div>
                  <p className="font-[scandiaMedium] text-[#303030] text-[14px] leading-[20px]">
                    Sidney, Australia
                  </p>
                </div>
                <div className="flex gap-[8px]">
                  <button className="bg-[#ECE7E3] border-none rounded-[70px] h-[44px] flex items-center px-[24px] font-[scandiaMedium] cursor-pointer text-[#181916] text-[12px] sm:text-[14px] leading-[20px] ">
                    Unfollow
                  </button>
                  <button
                    className="bg-[#D31B1B] border-none rounded-[80px] h-[44px] flex gap-[8px] items-center px-[16px] sm:px-[24px] font-[scandiaMedium] cursor-pointer text-[#FFFFFF] text-[12px] sm:text-[14px] leading-[20px] "
                    onClick={() => onNavigate("donate")}
                  >
                    <img
                      src="Images/CommunityMain/org-detail/healtcare.svg"
                      alt="contribute"
                    />{" "}
                    Contribute
                  </button>
                </div>
              </div>
              <div className="flex justify-between max-[1140px]:flex-col max-[1140px]:items-start max-[1140px]:gap-[20px] ">
                <div className="flex flex-col gap-[8px]">
                  <p className="font-[scandia] text-[#303030] text-[14px] leading-[20px]">
                    Tags:
                  </p>
                  <div className="flex gap-[4px] flex-wrap">
                    <div className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center">
                      <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                        Non-profit
                      </p>
                    </div>
                    <div className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center">
                      <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                        Volunteering
                      </p>
                    </div>
                    <div className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center ">
                      <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                        Child protection
                      </p>
                    </div>
                    <div className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center">
                      <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                        Food & nutrition
                      </p>
                    </div>
                    <div className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center">
                      <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                        +3
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col gap-[8px] items-end  max-[1140px]:items-start">
                  <p className="font-[scandiaMedium] text-[#181916] text-[16px] leading-[24px]">
                    2000+ Followers
                  </p>

                  <div className="flex gap-[8px]  ">
                    <p className="font-[scandiaMedium] max-[1200px]:w-[350px] max-[600px]:w-[100%] w-[264px] text-[#000000] max-[1140px]:text-start text-[12px] leading-[16px] text-end">
                      Rami Al-Saleh, Tariq Darwish, and 10 others{" "}
                      <span className="font-[scandia]">
                        followed this organization
                      </span>
                    </p>
                    <div className="flex -space-x-3">
                      <Image
                        src="/Images/CommunityMain/p1.png"
                        alt="avatar1"
                        width={32}
                        height={32}
                        className="w-[32px] h-[32px] rounded-full border-2 border-white object-cover"
                      />
                      <Image
                        src="/Images/CommunityMain/p1.png"
                        alt="avatar2"
                        width={32}
                        height={32}
                        className="w-[32px] h-[32px] rounded-full border-2 border-white object-cover"
                      />
                      <Image
                        src="/Images/CommunityMain/p1.png"
                        alt="avatar3"
                        width={32}
                        height={32}
                        className="w-[32px] h-[32px] rounded-full border-2 border-white object-cover"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Image
              style={{ width: "100%", marginTop: "10px", marginBottom: "10px" }}
              src="/Images/CommunityMain/seperator.png"
              alt="seperator"
              width={1200}
              height={2}
            />
            <div className="flex flex-col gap-[4px]">
              <p className="font-[scandiaMedium] text-[#000000] text-[16px] leading-[24px]">
                About
              </p>
              <p className="font-[scandia] text-[#303030] text-[14px] leading-[20px]">
                Syrian Orphan Charity is dedicated to supporting and empowering
                orphaned children affected by the Syrian crisis. Through
                education, healthcare, and essential aid, we provide a nurturing
                environment to help them rebuild their futures with dignity and
                hope.
              </p>
            </div>
            <Image
              style={{ width: "100%", marginTop: "10px", marginBottom: "10px" }}
              src="/Images/CommunityMain/seperator.png"
              alt="seperator"
              width={1200}
              height={2}
            />
          </div>
          <div className="flex flex-col gap-[20px]">
            <div className="flex w-full justify-between ">
              <p className="font-[scandiaMedium] text-[#181916] text-[22px] leading-[32px]">
                Projects
              </p>
              <div className="flex gap-[8px]">
                <Image
                  src="/Images/CommunityMain/org-detail/arrowleft.svg"
                  alt="arrow left"
                  width={24}
                  height={24}
                  className="cursor-pointer"
                />
                <Image
                  src="/Images/CommunityMain/org-detail/arrowright.svg"
                  alt="arrow right"
                  width={24}
                  height={24}
                  className="cursor-pointer"
                />
              </div>
            </div>

            <div
              className="flex gap-[20px] overflow-x-auto"
              style={{
                width: "100%",
                overflowX: "scroll",
                scrollbarWidth: "none",
                msOverflowStyle: "none" /* For Internet Explorer and Edge */,
                WebkitOverflowScrolling:
                  "touch" /* For smooth scrolling on iOS */,
                paddingBottom: "20px",
              }}
            >
              <ProjectCard
                imageUrl="/Images/CommunityMain/org-detail/projectc1.png"
                orgImage={organization?.imageUrl}
                orgName={organization?.name}
                title="Safe haven for orphans"
                description="Building a secure shelter for Syrian orphans with access to education, food, and care."
                goalAmount="25,000 USD"
                progressPercent={40}
              />
              <ProjectCard
                imageUrl="/Images/CommunityMain/org-detail/projectc1.png"
                orgImage={organization?.imageUrl}
                orgName={organization?.name}
                title="Education for a brighter future"
                description="Building schools, training teachers, and providing resources so orphans receive quality education and future opportunities."
                tags={["Teachers", " Curriculum developer"]}
              />
              <ProjectCard
                imageUrl="/Images/CommunityMain/org-detail/projectc1.png"
                orgImage={organization?.imageUrl}
                orgName={organization?.name}
                title="Safe haven for orphans"
                description="Building a secure shelter for Syrian orphans with access to education, food, and care."
                goalAmount="25,000 USD"
                progressPercent={40}
              />
              {/* You can add more ProjectCard components here */}
            </div>
            <div className="flex flex-col gap-[20px]">
              <div className="flex w-full justify-between ">
                <p className="font-[scandiaMedium] text-[#181916] text-[22px] leading-[32px]">
                  Open roles
                </p>
                <div className="flex gap-[8px]">
                  <Image
                    src="/Images/CommunityMain/org-detail/arrowleft.svg"
                    alt="arrow left"
                    width={24}
                    height={24}
                    className="cursor-pointer"
                  />
                  <Image
                    src="/Images/CommunityMain/org-detail/arrowright.svg"
                    alt="arrow right"
                    width={24}
                    height={24}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div
                className="flex gap-[20px] overflow-x-auto"
                style={{
                  width: "100%",
                  overflowX: "scroll",
                  scrollbarWidth: "none" /* For Firefox */,
                  msOverflowStyle: "none" /* For Internet Explorer and Edge */,
                  WebkitOverflowScrolling:
                    "touch" /* For smooth scrolling on iOS */,
                }}
              >
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />

                <RoleCard
                  role="Nurse"
                  openPositions={5}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={false}
                  onApply={() => console.log("Apply clicked for Nurse")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <RoleCard
                  role="Doctor"
                  openPositions={3}
                  imageSrc="/Images/CommunityMain/org-detail/doctor.svg"
                  isPaid={true}
                  onApply={() => console.log("Apply clicked for Doctor")}
                />
                <div></div>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-[16px]">
            <p className="text-[22px] pb-[4px] leading-[32px] font-[scandiaMedium]">
              {" "}
              Recent posts{" "}
            </p>
            <div className="bg-[white] w-full flex flex-col gap-[16px] border border-[#0000000A] text-[black] rounded-[20px] max-[919px]:py-[20px] max-[919px]:px-[16px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex-flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Tariq Darwish
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Architect
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/waterfilter.svg"
                    alt="message icon"
                    width={18}
                    height={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col max-[919px]:gap-[12px] gap-[16px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  Clean water is a right, not a privilege. 💧 Today, we
                  installed a new water filtration system in a refugee camp,
                  providing safe drinking water for over 500 people. Small
                  steps, big impact! Let&apos;s keep pushing forward for a
                  healthier, stronger Syria
                </p>
                <div className="flex max-[1080px]:flex-col gap-[16px] w-full">
                  <div className=" max-[1200px]:w-fit w-[60%] h-[395px] max-[800px]:h-auto max-[1000px]:w-full w-[603px] rounded-[12px]">
                    <img
                      src="/Images/community/feeds1.png"
                      alt="profile"
                      className="w-[100%] object-cover h-[100%] rounded-[20px]"
                    />
                  </div>
                  <div className="flex flex-col max-[1080px]:flex-row max-[1080px]:w-[100%] max-[1200px]:w-fit  w-[40%] max-[800px]:w-full gap-[16px]">
                    <img
                      src="/Images/community/water filtration work in Syria.png"
                      alt="profile"
                      className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]: max-[800px]:h-auto"
                    />
                    <img
                      src="/Images/community/Frame 1597884070.png"
                      alt="profile"
                      className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]:h-auto"
                    />
                  </div>
                </div>
                {/* <div className="flex-col  responsive-box-mob max-[919px]:block hidden gap-[8px]">
                  <div className="flex gap-[8px]">
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={8}
                      className="w-[50%] object-cover h-[50%] rounded-[12px]"
                    />
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={18}
                      className="w-[50%] object-cover h-[50%]rounded-[12px]"
                    />
                  </div>
                  <div className="flex w-full gap-[8px]">
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={8}
                      className="w-[50%] object-cover h-[50%] rounded-[12px]"
                    />
                    <Image
                      src="/Images/community/Frame 15978840702222.svg"
                      alt="profile"
                      height={8}
                      width={18}
                      className="w-[50%] h-[50%]rounded-[12px]"
                    />
                  </div>
                </div> */}
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 05.23 PM
                </p>
              </div>
              <div className="flex items-center gap-[8px]">
                {buttonsGroup2.map((item, index) => {
                  const isActive = activeIndexGroup2 === index;
                  return (
                    <button
                      key={index}
                      onClick={() => setActiveIndexGroup2(index)}
                      className={`${
                        isActive
                          ? "bg-[#2F3B31] text-white"
                          : "bg-[#ECE7E3] text-black"
                      } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                    >
                      <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                        {item.value}
                      </p>
                      <Image
                        src={isActive ? item.activeIcon : item.defaultIcon}
                        alt="icon"
                        height={18}
                        width={18}
                        className="w-[18px] h-[18px]"
                      />
                    </button>
                  );
                })}
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="message icon"
                    width={18}
                    height={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item.activeIcon : item.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="message icon"
                    width={18}
                    height={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item.activeIcon : item.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="message icon"
                    width={18}
                    height={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item.activeIcon : item.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default OrganizationDetail;
