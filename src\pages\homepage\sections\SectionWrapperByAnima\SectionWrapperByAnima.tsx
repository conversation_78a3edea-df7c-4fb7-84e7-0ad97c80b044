import React, { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import Image from "next/image";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

interface Testimonial {
  id: number;
  name: string;
  title: string;
  size: "small" | "medium" | "large";
  opacity: string;
  hasPlayButton?: boolean;
}

const sizeClasses = {
  small: "w-[210px] h-auto",
  medium: "w-[243px] h-[368px]",
  large: "w-[270px] h-[429px]",
  active: "w-[310px] h-[473px]",
} as const;

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    title: "Influencer & Mental Health Advocate",
    size: "small",
    opacity: "opacity-70",
  },
  {
    id: 2,
    name: "<PERSON> <PERSON><PERSON>",
    title: "Religious Imam",
    size: "medium",
    opacity: "opacity-80",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    title: "Actor and Activist",
    size: "large",
    hasPlayButton: true,
    opacity: "opacity-100",
  },
  {
    id: 4,
    name: "Rana Hatem",
    title: "Syrian Filmmaker & Cultural Storyteller",
    size: "medium",
    opacity: "opacity-80",
  },
  {
    id: 5,
    name: "Dr. Jad Saad",
    title: "Biomedical Researcher & Women's Health Advocate",
    size: "small",
    opacity: "opacity-70",
  },
];

const SectionWrapperByAnima = () => {
  const [testimonialList, setTestimonialList] = useState<Testimonial[]>(testimonials);
  const [activeIndex, setActiveIndex] = useState(2);
  const containerRef = useRef<HTMLDivElement>(null);
  const cardRefs = useRef<(HTMLDivElement | null)[]>([]);

  useEffect(() => {
    if (!containerRef.current || !cardRefs.current[activeIndex]) return;

    const container = containerRef.current;
    const activeCard = cardRefs.current[activeIndex];

    if (!activeCard) return;

    const containerRect = container.getBoundingClientRect();
    const cardRect = activeCard.getBoundingClientRect();

    const scrollLeft =
      activeCard.offsetLeft -
      container.offsetLeft -
      (containerRect.width / 2 - cardRect.width / 2);

    container.scrollTo({ left: scrollLeft, behavior: "smooth" });
  }, [activeIndex]);

  const handleNext = () => {
    const newIndex = (activeIndex + 1) % testimonialList.length;
    updateActivePlayButton(newIndex);
  };

  const handlePrevious = () => {
    const newIndex =
      (activeIndex - 1 + testimonialList.length) % testimonialList.length;
    updateActivePlayButton(newIndex);
  };

  const updateActivePlayButton = (index: number) => {
    setActiveIndex(index);
    setTestimonialList((prevList) =>
      prevList.map((item, i) => ({
        ...item,
        hasPlayButton: i === index,
      }))
    );
  };

  return (
    <section className="gap-6 pt-20 pb-16 px-16 flex flex-col items-center justify-center relative self-stretch w-full flex-[0_0_auto] bg-almost-white no-scrollbar">
      <div className="flex flex-col items-center gap-10 relative self-stretch w-full flex-[0_0_auto]">
        <header className="h-[76px] items-center gap-2 self-stretch w-full flex flex-col relative">
          {/* header code unchanged */}
          <p className="font-[ScandiaMedium] text-[22px] lg:text-[28px] text-[#181916]">What They Say</p>
          <p className="font-[Scandia] text-[14px] lg:text-[16px] text-[#303030]">Leading voices sharing their vision for a stronger, united Syria.</p>

        </header>

        <div
          className="w-full overflow-x-auto overflow-y-hidden no-scrollbar"
          ref={containerRef}
        >
          <div className="flex flex-nowrap gap-[10px] min-w-max items-center justify-center">
            {" "}
            {/* Added items-start here to align top */}
            {testimonialList.map((testimonial, index) => {
              const distance = Math.abs(index - activeIndex);

              // Width: 310px → 270px → 230px → min 190px
              const maxWidth = 310;
              const widthStep = 40;
              const calculatedWidth = Math.max(
                maxWidth - distance * widthStep,
                190
              );

              // Height: 475px → 435px → 395px → min 315px
              const maxHeight = 473;
              const heightStep = 40;
              const calculatedHeight = Math.max(
                maxHeight - distance * heightStep,
                315
              );

              return (
                <Card
                  ref={(el) => {
                    cardRefs.current[index] = el;
                  }}
                  key={testimonial.id}
                  className={`flex flex-col ${
                    testimonial.hasPlayButton
                      ? sizeClasses["active"]
                      : sizeClasses[testimonial.size]
                  } items-start justify-end gap-3.5 p-5 relative rounded-[20px] overflow-hidden border-solid border-[#0000000a] shadow-card-shadow [background:linear-gradient(0deg,rgba(0,0,0,0.2)_0%,rgba(0,0,0,0.2)_100%)] ${
                    testimonial.hasPlayButton
                      ? "opacity-100"
                      : testimonial.opacity
                  }`}
                  style={{
                    width: `${calculatedWidth}px`,
                    height: `${calculatedHeight}px`,
                    transform: "scale(1)",
                    transition: "all 0.3s ease",
                    backgroundImage:`url("/Images/homepage/womenWater.png")`,
                    overflow:"hidden",
                    objectFit:"cover"
                  }}
                >
                  {/* Gradient overlay */}
                  <div
                    className="absolute left-0 bottom-0 bg-gradient-to-b from-transparent to-black/80 z-10"
                    style={{
                      width: `${calculatedWidth}px`,
                      height: `${calculatedHeight * 0.4}px`, // ~40% of card height
                    }}
                  />

                  {/* ${
                      testimonial.size === "small"
                        ? "w-[170.06px] mr-[-10.06px]"
                        : testimonial.size === "medium"
                        ? "w-[203.17px] mr-[-3.17px]"
                        : "w-full"
                    } */}
                  <CardContent
                    className={`flex flex-col items-start gap-[${
                      testimonial.size === "small"
                        ? "12.45px"
                        : testimonial.size === "medium"
                        ? "14.41px"
                        : "16px"
                    }] relative flex-[0_0_auto] w-full p-0`}
                  >
                    <Image
                      className={`relative ${
                        testimonial.size === "small"
                          ? "w-[31.12px] h-[1.56px] mt-[-1.56px]"
                          : testimonial.size === "medium"
                          ? "w-[36.03px] h-[1.8px] mt-[-1.80px]"
                          : "w-10 h-0.5 mt-[-2.00px]"
                      }`}
                      alt="Line"
                      src={`/line-80${
                        testimonial.id === 1
                          ? "-3"
                          : testimonial.id === 2 || testimonial.id === 4
                          ? "-1"
                          : testimonial.id === 5
                          ? "-4"
                          : ""
                      }.svg`}
                      width={40}
                      height={2}
                    />

                    <div
                      className={`flex flex-col items-start gap-[${
                        testimonial.size === "small"
                          ? "3.11px"
                          : testimonial.size === "medium"
                          ? "3.6px"
                          : "4px"
                      }] relative self-stretch w-full flex-[0_0_auto]`}
                    >
                      <h3
                        className={`relative self-stretch ${
                          testimonial.size === "small" ||
                          testimonial.size === "medium"
                            ? "font-[ScandiaMedium] text-[14px] text-white z-10"
                            : "font-[ScandiaMedium] text-[14px] text-white z-10"
                        } text-white ${
                          testimonial.size === "small"
                            ? "mt-[-0.78px]"
                            : testimonial.size === "medium"
                            ? "mt-[-0.90px]"
                            : "mt-[-1.00px]"
                        }`}
                      >
                        {testimonial.name}
                      </h3>

                      <p
                        className={`relative self-stretch ${
                          testimonial.size === "small" ||
                          testimonial.size === "medium"
                            ? "font-[Scandia] text-[14px] text-white z-10"
                            : "font-[Scandia] text-[12px] text-white z-10"
                        } text-almost-white`}
                      >
                        {testimonial.title}
                      </p>
                    </div>
                  </CardContent>

                  {testimonial.hasPlayButton && (
                    <div className="absolute w-12 h-12 left-[40%] top-[40%] cursor-pointer">
                      <div className="relative w-[46px] h-11 top-0.5 left-0.5">
                        <Image
                          className="absolute w-8 h-[35px] top-1 left-3"
                          alt="Play button"
                          src="/Images/homepage/play.png"
                          width={32}
                          height={35}
                        />
                      </div>
                    </div>
                  )}
                </Card>
              );
            })}
          </div>
        </div>
      </div>

      {/* Navigation buttons unchanged */}
      <div className="flex flex-col items-center justify-center gap-10 pt-5 pb-0 px-0 relative self-stretch w-full flex-[0_0_auto]">
        <div className="inline-flex gap-5 items-center relative flex-[0_0_auto]">
          <Button
            variant="ghost"
            className="relative w-[38px] h-[38px] p-0 cursor-pointer"
            onClick={handlePrevious}
          >
            <Image
              src="/Images/homepage/arrowLeft.svg"
              alt="goto previous"
              width={38}
              height={38}
            />
          </Button>

          <Button
            variant="ghost"
            className="relative w-[38px] h-[38px] p-0 rounded-full cursor-pointer"
            onClick={handleNext}
          >
            <Image
              src="/Images/homepage/arrowRIght.svg"
              alt="goto next"
              width={38}
              height={38}
            />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default SectionWrapperByAnima;

