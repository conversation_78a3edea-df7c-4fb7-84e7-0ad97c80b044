{"name": "il<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@headlessui/react": "^2.2.4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/line-clamp": "^0.4.4", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "lucide-react": "^0.511.0", "next": "^15.3.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "slick-carousel": "^1.8.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5"}}