"use client";
import Image from "next/image";
import React from "react";

const Hero = () => {
  return (
    <div className="mt-[100px] main-container flex flex-col lg:flex-row w-full pt-16 pb-16 px-6 sm:px-8 md:px-10 xl:px-[100px] gap-12 lg:gap-16 justify-between items-center bg-[#fdf3d8] relative">
      {/* Left Image Section */}
      <div
        className="w-full lg:min-w-[502px] lg:w-[602px] h-[300px] sm:h-[400px] lg:h-[489px] rounded-[20px] bg-cover bg-no-repeat relative shrink-0"
        style={{
          backgroundImage:
            "url('https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/VmNUCevStF.png')",
        }}
      />

      {/* Right Content */}
      <div className="flex flex-col gap-10 w-full justify-center items-start rounded-[20px] relative z-[1]">
        {/* Header */}
        <div className="flex flex-col gap-4 sm:gap-6 items-start w-full">
          <div className="py-1.5 px-3 sm:py-2 sm:px-4 bg-[rgba(0,0,0,0.08)] rounded-full">
            <span className="md:text-sm text-[12px] font-[ScandiaMedium] font-medium text-[rgba(34,34,34,0.6)]">
              Featured course
            </span>
          </div>

          <div className="flex flex-col gap-2">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-[ScandiaMedium] text-[#181916] leading-tight">
              Storytelling for social impact
            </h2>
            <p className="text-base sm:text-lg font-[Scandia] text-[#2f2f2f]">
              Crafting narratives that inspire and mobilize — master the art of
              storytelling to connect emotionally with supporters and convey
              your project&apos;s mission powerfully.
            </p>
          </div>
        </div>

        {/* Organization Details */}
        <div className="flex flex-col gap-5 w-full">
          <div className="flex gap-3 items-center">
            <div className="w-12 h-12 relative">
              <Image
                src="/Images/learn/hero.png"
                alt="organization"
                fill
                className="object-cover rounded-full"
              />
            </div>
            <div className="flex flex-col">
              <span className="text-base font-[ScandiaMedium] text-[#000]">
                Syrian Voices Alliance
              </span>
              <span className="text-sm font-[Scandia] text-[#2f2f2f]">Advocacy & media</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-3">
            {[
              {
                title: "Storytelling",
                icon: "/Images/learn/feature.png",
              },
              {
                title: "Advocacy & Awareness",
                icon: "/Images/learn/feature.png",
              },
              {
                title: "Narrative Strategy",
                icon: "/Images/learn/feature.png",
              },
            ].map((tag, idx) => (
              <div
                key={idx}
                className="flex items-center gap-2 bg-[#fbe7b1] rounded-[20px] px-4 py-[10px] sm:py-4"
              >
                <div className="h-5 w-5 sm:w-6 sm:h-6 relative">
                  <Image
                    src={tag.icon}
                    alt={tag.title}
                    width={20}
                    height={20}
                    className="object-contain"
                  />
                </div>
                <span className="text-[12px] sm:text-[14px] font-[ScandiaMedium] font-medium text-[#181916]">
                  {tag.title}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Learn More Button */}
        <div className="w-full sm:w-auto">
          <button className="w-full sm:w-[207px] px-6 sm:px-16 py-3 sm:py-4 bg-[#d31b1b] h-auto text-white text-sm sm:text-[12px] font-[ScandiaMedium] font-medium rounded-full">
            Learn more
          </button>
        </div>
      </div>
    </div>
  );
};

export default Hero;


