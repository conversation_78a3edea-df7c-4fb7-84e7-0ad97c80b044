"use client";
import React from "react";
import { useState } from "react";
import MemberCard from "@/components/memberCard/memberCard";
// import OrganizationDetail from "../Organization/Organizationdetail";
import MemberDetail from "./Memberdetail";
import Image from "next/image";

interface Member {
  id: string;
  name: string;
  title: string;
  imageUrl: string;
  actionLabel: string;
}

interface MemberProps {
  onNavigate: (page: "list" | "details" | "create") => void;
}

function CommunityMembers({ onNavigate }: MemberProps) {
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const members: Member[] = [
    {
      id: "1",
      name: "<PERSON><PERSON>",
      title: "Software Engineer",
      imageUrl: "/Images/CommunityMain/profile.png",
      actionLabel: "Connect"
    },
    {
      id: "2",
      name: "<PERSON><PERSON><PERSON>",
      title: "Architect",
      imageUrl: "/Images/CommunityMain/p1.png",
      actionLabel: "Connect"
    },
    {
      id: "3",
      name: "<PERSON><PERSON>",
      title: "PHD Student",
      imageUrl: "/Images/CommunityMain/p2.png",
      actionLabel: "Connect"
    },
    {
      id: "4",
      name: "<PERSON>",
      title: "Designer",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    },
    {
      id: "5",
      name: "Lina Yasmin",
      title: "Teacher",
      imageUrl: "/Images/CommunityMain/p4.png",
      actionLabel: "Connect"
    },
    {
      id: "6",
      name: "Yousef Najjar",
      title: "Researcher",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    },
    {
      id: "7",
      name: "Ahmad Fadel",
      title: "Entrepreneur",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    },
    // Duplicates from your second column (remove if not needed)
    {
      id: "8",
      name: "Rami Al-Saleh",
      title: "Software Engineer",
      imageUrl: "/Images/CommunityMain/profile.png",
      actionLabel: "Connect"
    },
    {
      id: "9",
      name: "Tariq Darwish",
      title: "Architect",
      imageUrl: "/Images/CommunityMain/p1.png",
      actionLabel: "Connect"
    },
    {
      id: "10",
      name: "Faresh Najar",
      title: "PHD Student",
      imageUrl: "/Images/CommunityMain/p2.png",
      actionLabel: "Connect"
    },
    {
      id: "11",
      name: "Omar Al-Hakim",
      title: "Designer",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    },
    {
      id: "12",
      name: "Lina Yasmin",
      title: "Teacher",
      imageUrl: "/Images/CommunityMain/p4.png",
      actionLabel: "Connect"
    },
    {
      id: "13",
      name: "Yousef Najjar",
      title: "Researcher",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    },
    {
      id: "14",
      name: "Ahmad Fadel",
      title: "Entrepreneur",
      imageUrl: "/Images/CommunityMain/p3.png",
      actionLabel: "View Profile"
    }
  ];

  const handleCardClick = (member: Member) => {
    setSelectedMember(member);
    onNavigate("details");
  };

  const handleBackToList = () => {
    setSelectedMember(null);
    onNavigate("list");
  };

  if (selectedMember) {
    return (
      <MemberDetail
        member={selectedMember}
        onBack={handleBackToList}
      />
    );
  }
  return (
    <div className="bg-[#FFFFFF] rounded-[20px] flex flex-col md:gap-[34px] gap-[19px]  p-[32px] max-[1000px]:px-[0] shadow-[4px_12px_24px_0px_rgba(0,0,0,0.04)] max-[1000px]:shadow-none">
      <div className="max-[1000px]:hidden flex gap-[8px]">
        <button style={{
          fontFamily: "ScandiaMedium",
          background: "#ECE7E3"
        }} className="font-[scandiaMedium] flex gap-[6px] items-center cursor-pointer text-[#181916] text-[14px] leading-[20px] rounded-[70px] border-none py-[16px] px-[24px]  ">
          <Image src="/Images/CommunityMain/filter.svg" alt="filter" width={24} height={24} />   Filter
        </button>
        <div className="flex items-center gap-[8px] w-[285px] rounded-[99px] border" style={{ border: "1px solid #23232366" }}>
          <Image src="/Images/CommunityMain/search.svg" alt="search" width={16} height={16} className="ml-[24px]" />
          <input
            className="bg-transparent flex-1 focus:outline-none focus:ring-0 placeholder:font-[scandia] text-[14px] font-[ScandiaMedium] py-[13px] pr-[24px] placeholder-[#23232366]"
            placeholder="Enter email"
          />
        </div>
      </div>
      <div className="max-[1000px]:flex hidden justify-between">
        <p className="text-[#181916] text-[22px] leading-[32px] font-[ScandiaMedium]">
          Member
        </p>
        <div className="flex gap-[12px]">
          <Image src="/Images/CommunityMain/search.svg" alt="search" width={24} height={24} className="cursor-pointer" />
          <Image src="/Images/CommunityMain/filter.svg" alt="filter" width={24} height={24} className="cursor-pointer" />
        </div>
      </div>
      <Image style={{ width: "100%" }} src="/Images/CommunityMain/seperator.png" alt="seperator" width={1200} height={2} />
      <div className="flex flex-col sm:gap-[32px]  gap-[18px]">
        <div className="flex gap-[64px] max-[1300px]:gap-[30px] max-[768px]:gap-[8px] max-[1150px]:flex-col">
          {[members.slice(0, Math.ceil(members.length / 2)), members.slice(Math.ceil(members.length / 2))].map(
            (columnMembers, columnIndex) => (
              <div key={columnIndex} className="flex flex-col gap-[8px] w-full">
                {columnMembers.map((member) => (
                  <MemberCard
                    key={member.id}
                    name={member.name}
                    title={member.title}
                    imageUrl={member.imageUrl}
                    actionLabel={member.actionLabel}
                    onClick={() => handleCardClick(member)}
                  />
                ))}
              </div>
            )
          )}
        </div>

        <Image
          style={{ width: "100%" }}
          className="block sm:hidden"
          src="/Images/CommunityMain/seperator.png"
          alt="seperator"
          width={1200}
          height={2}
        />
        <div className="flex w-full flex-col items-center">
          <p className="text-[#303030] text-[14px] leading-[20px] font-[ScandiaMedium] cursor-pointer">Load more</p>
        </div>
      </div>
    </div>
  );
}

export default CommunityMembers;
