.storyteller__main__container__1 {
  display: flex;
  gap: 24px;
}

.storyteller__main__container__2 {
  padding: 40px 32px;
  border-radius: 20px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: 960px;
  width:100%
}
.storyteller__main__heading_text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  vertical-align: middle;
  color: #181916;
}
.storyteller__main__continer_inner {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.storyteller__main__continer_inner__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  vertical-align: middle;
  color: #303030;
}
.storyteller__main__continer_inner__text__2 {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #303030;
}
.storyteller__main__continer_inner_1 {
  padding: 24px 32px;
  background: #ffffff;
  border: 1px solid #0000000a;
  box-shadow: 4px 12px 24px 0px #0000000a;

  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.storyteller__main_inner_1__heaing_container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.storyteller__main_inner_1__heading {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 22px;
  line-height: 32px;
  vertical-align: middle;
  color: #181916;
}
.storyteller__main_inner_1__text {
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #303030;
}
.storyteller__main_inner_1__border__container {
  margin-top: 10px;
  border-top: 1px solid #ece7e3;
  width: 100%;
}
.storyteller__main_inner_1__chapter_container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.storyteller__main_inner_1__chapter {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  vertical-align: middle;
  color: #d31b1b;
}

.course__detail__card__preview__video1 {
  margin-top: 8px;
  object-fit: cover;
  border-radius: 16px;
  width: 100%;
  /* height: 280px; */
  display: block;
  height: 564px;
}
.storyteller__main_inner_1__quiz__container {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.storyteller__main_inner_1__quiz {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 22px;
  line-height: 32px;
  vertical-align: middle;
  color: #181916;
}
.storyteller__main_inner_1__quiz__container22 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px;
  background: #ece7e3;
  padding: 12px;
  border: none;
}
.storyteller__main_inner_1__quiz__container22__inner {
  display: flex;
  gap: 8px;
  align-items: center;
}
.storyteller__main_inner_1__quiz__container22__inner__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #303030;
}
.storyteller__whatnext__container {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.storyteller__whatnext__container__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 22px;
  line-height: 32px;
  vertical-align: middle;
  color: #181916;
}
.storyteller__whatnext__container__inner__container_top {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}
.storyteller__whatnext__container__inner {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 208px;
}
.storyteller__whatnext__container__inner__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
  vertical-align: middle;
  color: #d31b1b;
  margin-top: 6px !important;
}
.storyteller__whatnext__container__inner__image {
  margin-bottom: 6px;
}
.storyteller__whatnext__container__inner__text2 {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  vertical-align: middle;
  color: #181916;
}
.storyteller__whatnext__container__inner__text3 {
  font-family: Scandia;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  vertical-align: middle;
  color: #303030;
}
.course__detail__accordion__wrapper1 {
  margin-top: 8px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}
@media (max-width: 768px) {
  .storyteller__main__container__1 {
    flex-direction: column;
  }
}
@media (max-width: 600px) {
  .storyteller__main__container__2 {
    padding: 24px 16px;
  }
  .storyteller__main__heading_text {
    font-size: 32px;
    line-height: 40px;
  }
  .storyteller__main__continer_inner_1 {
    padding: 16px 24px;
  }
  .course__detail__card__preview__video1 {
    height: auto;
  }
}
