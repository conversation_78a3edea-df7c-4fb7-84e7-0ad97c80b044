"use client";
import React, { useState } from "react";
import { Card, CardContent, Box, Avatar } from "@mui/material";
import { Progress } from "@/components/ui/progress";
import { useRouter } from "next/navigation";
import Image from "next/image";

export const CourseCard = ({
  image,
  icon,
  title,
  user,
  description,
  students,
  languages,
  isFeatured,
  featured,
  onClick,
}: {
  image: string;
  icon: string;
  title: string;
  user: string;
  description: string;
  students: string;
  languages: string;
  isFeatured: boolean;
  featured?: { completed: number; total: number };
  onClick?: () => void;
}) => {
  const [saved, setSaved] = useState(false);

  const toggleSaved = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSaved((prev) => !prev);
  };
  return (
    <Card
      onClick={onClick}
      sx={{
        width: "100%",
        maxWidth: 310,
        borderRadius: "20px",
        p: 2.5,
        display: "flex",
        flexDirection: "column",
        gap: 2.25,
        boxShadow: "0px 0px 24px rgba(0, 0, 0, 0.04)",
        border: "1px solid rgba(0, 0, 0, 0.04)",
        position: "relative",
        cursor: onClick ? "pointer" : "default",
      }}
    >
      <Box
        sx={{
          height: 186,
          borderRadius: "12px",
          backgroundImage: `url(${image})`,
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
        }}
      />
      <CardContent
        sx={{ p: 0, display: "flex", flexDirection: "column", gap: 1.25 }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Avatar src={icon} sx={{ width: 24, height: 24 }} />
          <p className="text-[#2f2f2f] font-[ScandiaMedium] text-[12px]">
            {user}
          </p>
        </Box>
        <p className="text-[#181916] text-[18px] md:text-[22px] font-[ScandiaMedium]">
          {title}
        </p>
        <p className="text-[#181916] font-[Scandia] text-[12px] md:text-[14px] line-clamp-2">
          {description}
        </p>

        {!isFeatured && (
          <Box className="flex justify-between opacity-60 pt-[25px] md:pt-[40px]">
            <Box display="flex" alignItems="center" gap={0.5}>
              <Image
                src="/Images/learn/student.png"
                width={16}
                height={16}
                alt="students"
              />
              <p className="font-[Scandia] text-[#303030] text-[12px] md:text-[14px]">
                {students} students
              </p>
            </Box>
            <Box display="flex" alignItems="center" gap={0.5}>
              <Image
                src="/Images/learn/languages.png"
                width={16}
                height={16}
                alt="languages"
              />
              <p className="font-[Scandia] text-[#303030] text-[12px] md:text-[14px]">
                {languages}
              </p>
            </Box>
          </Box>
        )}

        {isFeatured && (
          <div className="flex flex-col gap-2 px-6 py-4 bg-[#FDDB83] rounded-xl">
            <div className="flex justify-between items-center">
              <p className="font-[Scandia] text-[12px] text-[#181916]">
                Learning Process
              </p>
              <p className="font-[ScandiaMedium] text-[14px] text-[#181916]">
                {featured?.completed || 0}/{featured?.total || 0} section
              </p>
            </div>
            <Progress
              value={
                ((featured?.completed || 0) / (featured?.total || 1)) * 100
              }
              className="h-[11px] bg-[#23232366]"
            />
          </div>
        )}
      </CardContent>
      <Image
        onClick={toggleSaved}
        src={
          saved ? "/Images/learn/bookmarked.png" : "/Images/learn/bookmark.png"
        }
        alt="badge"
        width={18}
        height={22}
        style={{
          position: "absolute",
          cursor: "pointer",
          top: 29,
          left: 255,
        }}
      />
    </Card>
  );
};

export const CodeOfEthics = (): React.ReactElement => {
  const router = useRouter();
  const handleCourseDetailClick = () => {
    router.push("/learn/course-detail");
  };

  const courses = [
    {
      image:
        "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/x9JVboQRcP.png",
      icon: "/Images/learn/card.png",
      title: "Ethics for Doctors",
      user: "Iltezam",
      description:
        "Explore the core ethical principles that guide doctors in patient care, confidentiality, informed consent, and professional integrity.",
      students: "245",
      languages: "English, Arabic",
      isFeatured: false,
    },
    {
      image:
        "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/BuV7aZ1awR.png",
      icon: "/Images/learn/card.png",
      title: "Teacher's Ethics",
      user: "Iltezam",
      description:
        "Learn how to navigate ethical challenges in the classroom, foster student trust, and uphold professional conduct in academic environments.",
      students: "445",
      languages: "English, Arabic",
      isFeatured: false,
    },
    {
      image:
        "https://codia-f2c.s3.us-west-1.amazonaws.com/image/2025-05-22/jCUDLppRQN.png",
      icon: "/Images/learn/card.png",
      title: "Lawyer's Ethics",
      user: "Iltezam",
      description:
        "Understand the ethical responsibilities of legal professionals, from client confidentiality to courtroom integrity.",
      students: "1,211",
      languages: "English, Arabic",
      isFeatured: false,
    },
  ];

  return (
    <Box width="100%" mx="auto" display="flex" flexDirection="column" gap={4}>
      <Box display="flex" flexDirection="column" gap={1}>
        <p className="font-[ScandiaMedium] text-[#181916] text-[22px] lg:text-[28px]">
          Code of Ethics
        </p>
        <p className="text-[#2f2f2f] font-[Scandia]">
          Certify your commitment to ethical, responsible work through Iltezam&apos;s
          professional standards course.
        </p>
      </Box>

      <Box
        sx={{
          display: "flex",
          gap: 3,
          overflowX: "auto",
          flexWrap: "nowrap",
          paddingBottom: 1, // optional spacing
          scrollbarWidth: "none", // Firefox
          "&::-webkit-scrollbar": {
            display: "none", // Chrome/Safari
          },
        }}
      >
        {courses.map((course, i) => (
          <Box key={i} sx={{ flex: "0 0 auto" }}>
            <CourseCard
              {...course}
              onClick={i === 0 ? handleCourseDetailClick : undefined}
            />
          </Box>
        ))}
      </Box>

      <p className="font-[ScandiaMedium] text-[14px] text-[#181916] w-full text-center">
        See all
      </p>
    </Box>
  );
};

export default CodeOfEthics;
