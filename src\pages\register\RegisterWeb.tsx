'use client';
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

function RegisterWeb() {
  const router = useRouter();
  const handleSign = () => {
    router.push("/Auth/SignIn/signin");
  };
  const handleInd = () => {
    router.push(`/Auth/Register/Indi-register?type=individual`);
  };
  
  const handleOrg = () => {
    router.push(`/Auth/Register/Indi-register?type=organization`);
  };
  
  const [hoverIndex, setHoverIndex] = useState(null);
  const cards = [
    {
      label: "As individual",
      img: "/Images/signin/user-circle.svg",
      onClick: handleInd,
    },
    {
      label: "As organization",
      img: "/Images/signin/office.svg",
      onClick: handleOrg,
    },
  ];
  return (
    <div className="flex flex-col w-[35%] lg:w-[40%] gap-[40px]">
      <div className="flex flex-col gap-[12px]">
        <p className="text-[black] leading-[40px] font-[scandiaMedium] text-[32px]">
          Register
        </p>
        <p
          style={{ color: "rgba(48, 48, 48, 1)" }}
          className="text-[14px] leading-[20px] font-[scandia]"
        >
          Already have an account?
          <span
            className=" font-[scandiaMedium] cursor-pointer"
            style={{ color: "rgba(211, 27, 27, 1)" }}
            onClick={handleSign}
          >
            {" "}
            Sign in
          </span>
        </p>
      </div>
      <div className="flex gap-[32px]">
        {cards.map((card, index) => (
          <div
            key={index}
            className="cursor-pointer rounded-[20px] w-[180px] h-[160px] items-center gap-[8px] justify-center flex-col flex transition-all duration-300"
            style={{
              border: "1px solid rgba(236, 231, 227, 1)",
              boxShadow:
                hoverIndex === index
                  ? "4px 12px 24px 0px rgba(0, 0, 0, 0.04)"
                  : "none",
            }}
            onMouseEnter={() => setHoverIndex(index as unknown as null)}
            onMouseLeave={() => setHoverIndex(null)}
            onClick={card.onClick || undefined}
          >
            <Image src={card.img} alt="user-img" height={24} width={24} />
            <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[black]">
              {card.label}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}

export default RegisterWeb;
