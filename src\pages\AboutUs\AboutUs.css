.aboutus_main__container {
  display: flex;
  flex-direction: column;
  gap: 80px;
  background-color: transparent;
}
.aboutus__container__1 {
  position: relative;
  background: url("/Images/ourpartner/backgroundourpartner.png") center
    center/cover no-repeat;
  min-height: 560px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100px 20px 40px;
}
.aboutus__hading__container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  justify-content: center;
  max-width: 900px;
}
.aboutus__hading__1 {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
}
.aboutus__para__1 {
  font-family: Scandia;
  font-weight: 400;
  font-size: 22px;
  line-height: 32px;
  text-align: center;
  vertical-align: middle;
  color: #fcfcfc;
}
.our__vision__main__container {
  display: flex;
  flex-direction: column;
  gap: 80px;
  padding: 0px 40px;
  justify-content: center;
  align-items: center;
}
.our__vision__container {
  display: flex;
  gap: 80px;
  align-items: center;
}
.our__vision__hading__container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 656px;
}
.our__vision__hading {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 28px;
  line-height: 36px;
  text-align: left;
  vertical-align: middle;
  color: #d31b1b;
}
.our__vision__hading__para {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  vertical-align: middle;
  color: #181916;
}
.our__vision__para {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  vertical-align: middle;
  color: #303030;
}
.our__pillars__manin__container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 64px;
  background: #ece7e3;
  padding: 128px 40px;
}
.our__pillars__hading {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.our__pillars__container {
  display: flex;
  gap: 24px;
  flex-wrap: wrap;
}
.our__pillars__card {
  padding: 40px;
  border-radius: 20px;
  display: flex;
  flex-direction: column;
  gap: 32px;
  background: #ffffff;
  box-shadow: 4px 12px 24px 0px #0000000a;
  align-items: center;
  justify-content: center;
}
.our__pillars__card__heading_container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 322px;
}
.our__pillars__card__heading {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 28px;
  line-height: 36px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.our__pillars__card__para {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  vertical-align: middle;
  color: #303030;
}
.our__team__main__container__about {
  display: flex;
  padding: 20px 158px 100px;
  background-color: #ffffff;
}
.our__team__container__about {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 64px;
  max-width: 1200px;
  margin: 0 auto;
}
.our__team__image__container__about {
  flex: 0 0 440px;
  height: 560px;
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  display: flex;
  align-items: flex-end;
}
.our__team__image__container__about__text_container__top {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-left: 32px;
}
.our__team__image__container__about__text_container__top__line {
  width: 40px;
  border: 2px;
  border: 2px solid #ffffff;
}
.our__team__image__container__about__text_container {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding-bottom: 32px;
}
.our__team__image__container__about__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 22px;
  line-height: 32px;
  color: #ffffff;
  vertical-align: middle;
}
.our__team__image__container__about__text__12 {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: #fcfcfc;
}
.our__team__hading__container__about {
  display: flex;
  flex-direction: column;
  gap: 24px;
  flex: 1;
  max-width: 600px;
}
.our__team__content__about {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.our__team__hading__about__22 {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 28px;
  line-height: 36px;
  text-align: left;
  vertical-align: middle;
  color: #d31b1b;
  margin: 0;
}
.our__team__para__about___22 {
  font-family: Scandia;
  font-weight: 400;
  font-size: 22px;
  line-height: 32px;
  vertical-align: middle;
  color: #303030;
  margin: 0;
}
/* Join Us Section Styles */
.joinus__section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #2f3b31;
  padding: 64px 100px;
  gap: 64px;
  height: 300px;
  border-bottom: 1px solid #2f3b31
}

.joinus__content {
  flex: 1;
  display: flex;
  align-items: center;
}

.joinus__heading {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  vertical-align: middle;
  color: #fff;
}

.joinus__buttons {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.joinus__button {
  min-width: 180px;
  padding: 10px 0;
  border-radius: 20px;
  font-size: 0.95rem;
  font-family: Scandia, sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
}

.joinus__button--filled {
  padding: 16px 40px;
  border-radius: 70px;
  background: #ece7e3;

  border: none;
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}

.joinus__button--outline {
  padding: 16px 51px;
  background: transparent;
  border-radius: 60px;
  border: 1px solid #ffffff;
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  vertical-align: middle;
  color: #fff;
}
@media (max-width: 1200px) {
  .our__team__main__container__about {
    padding: 80px 40px;
  }
}
@media screen and (max-width: 1024px) {
  .aboutus_main__container {
    gap: 60px;
  }

  .aboutus__container__1 {
    padding: 80px 24px 30px;
  }

  .aboutus__hading__1 {
    font-size: 36px;
    line-height: 44px;
  }

  .aboutus__para__1 {
    font-size: 20px;
    line-height: 30px;
  }
  .our__vision__hading__container {
    max-width: unset;
  }
  .our__vision__main__container {
    gap: 60px;
    padding: 60px 40px;
  }
  .our__vision__image {
    width: 100%;
  }
  .our__vision__container {
    gap: 60px;
    flex-direction: column;
  }

  .our__vision__hading__para {
    font-size: 36px;
    line-height: 44px;
  }
  .our__pillars__container {
    flex-direction: column;
  }
  .our__pillars__card {
    width: 100%;
  }
  .our__pillars__card__heading_container {
    max-width: unset;
  }
  .our__team__main__container__about {
    padding: 80px 40px;
  }

  .our__team__container__about {
    gap: 40px;
  }

  .our__team__image__container__about {
    flex: 0 0 360px;
    height: 460px;
    width: 100% !important;
  }
  .our__team__container__about {
    flex-direction: column;
  }
  .our__team__hading__container__about {
    max-width: unset;
  }
  .joinus__section {
    flex-direction: column;
    align-items: flex-start;
    padding: 24px 16px;
    gap: 20px;
    height:unset
  }
  .joinus__content {
    width: 100%;
    justify-content: flex-start;
  }
  .joinus__buttons {
    width: 100%;
    flex-direction: row;
    gap: 12px;
  }
  .joinus__button {
    flex: 1;
    min-width: 0;
  }
}

@media screen and (max-width: 768px) {
  .aboutus_main__container {
    gap: 40px;
  }

  .aboutus__container__1 {
    padding: 60px 24px 20px;
  }

  .aboutus__hading__container {
    max-width: 100%;
    gap: 24px;
  }

  .aboutus__hading__1 {
    font-size: 28px;
    line-height: 36px;
  }

  .aboutus__para__1 {
    font-size: 18px;
    line-height: 26px;
  }

  .our__vision__main__container {
    gap: 40px;
    padding: 40px 0px;
  }

  .our__vision__container {
    flex-direction: column;
    gap: 40px;
    padding: 0 24px;
  }

  .our__vision__hading__container {
    max-width: 100%;
  }

  .our__vision__hading {
    font-size: 24px;
    line-height: 32px;
  }

  .our__vision__hading__para {
    font-size: 32px;
    line-height: 40px;
  }

  .our__vision__para {
    font-size: 15px;
    line-height: 22px;
  }
  .our__pillars__manin__container {
    padding: 60px 40px;
  }
  .our__team__main__container__about {
    padding: 30px 24px;
  }

  .our__team__container__about {
    flex-direction: column;
    gap: 32px;
  }

  .our__team__image__container__about {
    height: 400px;
    width: 100% !important;
  }

  .our__team__hading__container__about {
    max-width: 100%;
  }

  .our__team__hading__about__22 {
    font-size: 24px;
    line-height: 32px;
  }

  .our__team__para__about___22 {
    font-size: 18px;
    line-height: 28px;
  }
}

@media screen and (max-width: 480px) {
  .aboutus_main__container {
    gap: 30px;
  }

  .aboutus__container__1 {
    padding: 80px 24px 16px;
  
  }

  .aboutus__hading__container {
    gap: 20px;
  }

  .aboutus__hading__1 {
    font-size: 28px;
    line-height: 32px;
  }

  .aboutus__para__1 {
    font-size: 16px;
    line-height: 24px;
  }

  .our__vision__main__container {
    gap: 30px;
    padding: 30px 0px;
  }

  .our__vision__container {
    gap: 30px;
    padding: 0 16px;
  }

  .our__vision__hading {
    font-size: 24px;
    line-height: 28px;
  }

  .our__vision__hading__para {
    font-size: 28px;
    line-height: 36px;
  }

  .our__vision__para {
    font-size: 14px;
    line-height: 20px;
  }
  .our__pillars__manin__container {
    padding: 40px 24px;
    gap: 40px;
  }
  .our__pillars__card__heading {
    font-size: 25px;
    line-height: 30px;
  }
  .our__pillars__card__para {
    font-size: 15px;
    line-height: 20px;
  }
  .our__pillars__hading {
    font-size: 30px;
    line-height: 36px;
  }
   .joinus__section {
    flex-direction: column;
    align-items: stretch;
    padding: 16px 24px;
    gap: 12px;
  }
  .joinus__heading {
    font-size: 1.2rem;
    text-align: left;
  }
  .joinus__illustration img {
    width: 100px;
    height: 100px;
  }
  .joinus__buttons {
    gap: 8px;
    flex-direction: column;
  }
  .joinus__button
  {
    padding: 10px 20px;
    font-size: 12px;

  }
}



