// components/OrganizationCard.tsx
"use client";

import React from "react";
import Image from "next/image";

type OrganizationCardProps = {
  name: string;
  title: string;
  imageUrl: string;
  actionLabel: string;
  onClick?: () => void;
};

export default function OrganizationCard({
  name,
  title,
  imageUrl,
  actionLabel,
  onClick,
}: OrganizationCardProps) {
  const isFollow = actionLabel === "Follow";

  return (
    <div className="py-[8px] flex justify-between items-center w-full gap-[20px] w-full max-[1030px]:flex-col max-[1030px]:items-start">
      <div className="flex gap-[20px] items-center max-[1030px]:items-start w-full">
        <div className="relative h-[48px] w-[48px] max-[377px]:h-[30px] max-[377px]:w-[30px] rounded-full border border-[#ECE7E3] overflow-hidden">
          <Image
            src={imageUrl}
            alt={name}
            layout="fill"
            objectFit="cover"
            className="rounded-full"
          />
        </div>
        <div className="flex justify-between w-full items-center gap-[10px] max-[768px]:items-start max-[768px]:flex-col">
        <div className="flex flex-col gap-[4px] max-w-[615px]">
          <p className="font-[scandiaMedium] text-[#000000] text-[16px] max-[377px]:text-[14px] max-[377px]:leading-[22px] leading-[24px]">
            {name}
          </p>
          <p className="font-[scandia] text-[#303030] text-[14px] max-[377px]:text-[12px] leading-[20px]">
            {title}
          </p>
        </div>
     

      <button
        onClick={onClick}
        style={{
          border: isFollow ? "none" : "1px solid #303030",
        }}
        className={`h-[32px] cursor-pointer flex items-center py-[8px]  whitespace-nowrap px-[12px] text-[12px] leading-[16px] rounded-[80px] font-[scandiaMedium] ${
          isFollow ? "bg-[#D31B1B] text-white" : "bg-white text-[#303030]"
        }`}
      >
        {actionLabel}
      </button>
      </div>
       </div>
    </div>
  );
}
