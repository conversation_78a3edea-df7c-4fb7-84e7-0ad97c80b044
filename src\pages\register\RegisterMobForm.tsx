import Button from "@/components/button/Button";
import { ChevronDown } from "lucide-react";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { Listbox } from "@headlessui/react";

type Props = {
  // Keep the type definition but remove 'active' from the component parameters
  active: "signIn" | "register";
  setActive: (value: "signIn" | "register") => void;
};
const options = [
  { name: "Individual", value: "individual" },
  { name: "Organization", value: "organization" },
];
function RegisterMobForm({ setActive }: Props) {
  const [selected, setSelected] = useState(options[0]);
  const router = useRouter();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const handleregister = () => {
    // Add validation to use setErrors
    const newErrors = {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    };
    
    let isValid = true;
    
    if (!formData.name) {
      newErrors.name = "Name is required";
      isValid = false;
    }
    
    if (!formData.email) {
      newErrors.email = "Email is required";
      isValid = false;
    }
    
    if (!formData.password) {
      newErrors.password = "Password is required";
      isValid = false;
    }
    
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }
    
    setErrors(newErrors);
    
    if (isValid) {
      // Use setActive here to update the parent component state
      setActive("register");
      router.push("/Auth/Register/mail-verification");
    }
  };
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };
  return (
    <div>
      <form className="space-y-[52px] md:space-y-[40px]">
        <div className="flex flex-col gap-[32px]">
          <div className="flex flex-col">
            <label
              htmlFor="name"
              className=" text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Register as
            </label>
            <div className="relative w-full">
              <Listbox value={selected} onChange={setSelected}>
                <div className="relative">
                  <Listbox.Button className="w-full text-[#a9a9a9] border-b border-[#d9d9d9] py-2 pr-8 text-left bg-transparent focus:outline-none">
                    {selected.name}
                    <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <ChevronDown size={16} className="text-[#a9a9a9]" />
                    </span>
                  </Listbox.Button>
                  <Listbox.Options className="absolute mt-1 w-full bg-white shadow-md rounded-[10px] z-10">
                    {options.map((option, idx) => (
                      <Listbox.Option
                        key={idx}
                        className={({ active }) =>
                          `cursor-pointer text-[#232323] px-4 py-2 ${
                            active ? "bg-gray-100" : "bg-white"
                          } rounded-[10px]`
                        }
                        value={option}
                      >
                        {option.name}
                      </Listbox.Option>
                    ))}
                  </Listbox.Options>
                </div>
              </Listbox>
            </div>
          </div>
          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="name"
              className=" text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Name
            </label>
            <input
              id="name"
              type="text"
              placeholder="Enter name"
              value={formData.name}
              onChange={handleChange}
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] placeholder:leading-[20px] leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.name && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.name}
              </span>
            )}
          </div>
          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="email"
              className=" text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="Enter email"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] placeholder:leading-[20px] leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.email && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.email}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Enter password"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.password && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.password}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]
                "
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Confrim password
            </label>
            <input
              id="confirmPassword"
              type="password"
              value={formData.confirmPassword}
              onChange={handleChange}
              placeholder="Enter password again"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.confirmPassword && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left font-[scandiaMedium] text-center"
              >
                {errors.confirmPassword}
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-[24px] justify-center text-center">
          <Button>
            <div onClick={handleregister}>
              <p className="text-[14px] text-[white] font-[scandiaMedium]">
                Register
              </p>
            </div>
          </Button>
        </div>
      </form>
    </div>
  );
}

export default RegisterMobForm;
