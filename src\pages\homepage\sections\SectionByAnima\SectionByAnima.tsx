import React from "react";
import Image from "next/image";
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardFooter,
  CardHeader,
} from "../../../../components/ui/card";

const SectionByAnima = (): React.ReactElement => {
  // Card data for mapping
  const cards = [
    {
      id: 1,
      icon: "/Images/homepage/donate.png",
      iconClass: "w-[64px] h-[64px] top-[3px] left-[9px]",
      title: "Projects",
      description:
        "Discover, support, or launch initiatives focused on rebuilding Syria—one home, school, or clinic at a time.",
      buttonText: "Explore projects",
    },
    {
      id: 2,
      icon: "/Images/homepage/community.png",
      iconClass: "w-[64px] h-[64px] top-[3px] left-[9px]",
      title: "Community",
      description:
        "Join a global network of Syrians and allies working together, sharing stories, and creating real impact.",
      buttonText: "Join community",
    },
    {
      id: 3,
      icon: "/Images/homepage/learn.png",
      iconClass: "w-[64px] h-[64px] top-[3px] left-[9px]",
      title: "Learn",
      description:
        "Access courses, tools, and practical guidance from experts to help you contribute meaningfully to Syria's future.",
      buttonText: "Start learning",
    },
  ];

  return (
<section className="flex flex-col items-center justify-center py-20 px-4 pt-0 md:px-16 bg-[#FCFCFC] w-full no-scrollbar">
  <div className="flex flex-col items-center gap-10 w-full relative -top-6 no-scrollbar">
    <div className="relative w-full overflow-x-auto scroll-smooth scrollbar-hide">
      <div className="flex items-center gap-6 min-w-max justify-center">
        {cards.map((card) => (
          <Card
            key={card.id}
            className="flex flex-col items-start justify-center max-w-[445px] h-[375px] min-w-[280px] sm:min-w-[320px] md:min-w-[360px] lg:min-w-[400px] xl:min-w-[445px] rounded-[20px] shadow-2xl shadow-[#00000004]"
          >
            <CardHeader className="pb-0">
              <div className="relative">
                <div className={`${card.iconClass}`}>
                  <Image
                    className="w-full h-full top-0 left-0"
                    alt={`${card.title} icon`}
                    src={card.icon}
                    width={64}
                    height={64}
                  />
                </div>
              </div>
            </CardHeader>

            <CardContent className="flex flex-col items-start gap-2 pt-8">
              <h4 className="self-stretch font-[ScandiaMedium] text-[#181916] md:text-[20px] lg:text-[28px]">
                {card.title}
              </h4>
              <p className="self-stretch font-[Scandia] text-[#303030] text-[12px] lg:text-[14px]">
                {card.description}
              </p>
            </CardContent>

            <CardFooter className="pt-6 pb-10">
              <Button className="px-6 py-4 h-auto bg-[#D31B1B] hover:bg-[#D31B1B]/90 rounded-[30px] md:rounded-[80px] cursor-pointer">
                <span className="mt-[-1.23px] font-[ScandiaMedium] text-[12px] md:text-[14px] text-white">
                  {card.buttonText}
                </span>
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  </div>
</section>

  );
};
export default SectionByAnima;

