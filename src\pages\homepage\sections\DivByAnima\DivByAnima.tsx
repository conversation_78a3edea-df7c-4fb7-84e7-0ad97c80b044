import {  ClockIcon } from "lucide-react";
import React, { useState } from "react";
import { Button } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import Image from "next/image";

const DivByAnima = () => {
  // Member data for mapping
  const recentMembers = [
    { name: "<PERSON><PERSON><PERSON><PERSON>", profession: "Software Engineer" },
    { name: "<PERSON><PERSON><PERSON>", profession: "Architecture" },
    { name: "<PERSON><PERSON><PERSON>", profession: "Doctor" },
    { name: "<PERSON><PERSON>", profession: "Teacher" },
    { name: "<PERSON>", profession: "Entrepreneur" },
  ];

  const [active, setActive] = useState("Individual");

  const handleActive = (value: string) => {
    if (value) {
      setActive(value);
    }
  };
  return (
    <section className="gap-16 py-10 px-6 lg:px-[100px] lg:py-16 flex flex-col items-center justify-center relative self-stretch w-full bg-almost-white">
      <div className="flex-col items-start gap-5 md:gap-6 self-stretch w-full flex relative">
        <header className="items-start gap-2 self-stretch w-full flex flex-col relative">
          <h2 className="relative w-fit mt-[-1.00px] font-[ScandiaMedium] text-[22px] md:text-[28px] text-[#181916] ">
            Recently Joined
          </h2>

          <p className="relative self-stretch hidden md:block font-[Scandia] text-[16px] text-[#303030]">
            Welcome our newest members contributing to the mission.
          </p>
        </header>

        <div className="flex flex-col items-start gap-6 relative self-stretch w-full">
          <div className="flex items-center justify-between relative self-stretch w-full">
            <div className="flex items-center gap-2 relative">
              <Button
                onClick={() => handleActive("Individual")}
                className={`px-6 py-2 md:py-4 bg-app-primary border border-solid border-[#2f2f2f] rounded-[80px] h-auto cursor-pointer ${
                  active === "Individual" ? "bg-[#2F3B31]" : "transaprent"
                }`}
              >
                <span
                  className={`mt-[-1.23px] text-center ${
                    active === "Individual" ? "text-white" : "text-[#181916]"
                  } font-[ScandiaMedium] text-[14px]`}
                >
                  Individual
                </span>
              </Button>

              <Button
                onClick={() => handleActive("Organization")}
                variant="outline"
                className={`px-6 py-2 md:py-4 rounded-[60px] border border-solid border-[#2f2f2f] h-auto cursor-pointer ${
                  active === "Organization" ? "bg-[#2F3B31]" : "transparent"
                }`}
              >
                <span
                  className={`mt-[-1.23px] text-center ${
                    active === "Organization" ? "text-white" : "text-[#181916]"
                  } font-[ScandiaMedium] text-[12px] md:text-[14px]`}
                >
                  Organization
                </span>
              </Button>
            </div>

            <div className=" gap-5 items-center relative hidden md:flex">
              <Button
                variant="ghost"
                size="icon"
                className="w-[38px] h-[38px] p-0 opacity-50 cursor-pointer"
              >
                <Image
                  width={34}
                  height={34}
                  src="/Images/homepage/arrowLeft.svg"
                  alt="arrow left"
                />
              </Button>

              <Button
                variant="ghost"
                size="icon"
                className="w-[38px] h-[38px] p-0 cursor-pointer"
              >
                <Image
                  width={34}
                  height={34}
                  src="/Images/homepage/arrowRIght.svg"
                  alt="arrow right"
                />
              </Button>
            </div>
          </div>

          <div className="flex items-start gap-5 w-full overflow-x-auto no-scrollbar px-1 py-1">
            {recentMembers.map((member, index) => (
              <Card
                key={index}
                className="flex-1 border max-w-[204px] border-solid border-[#0000000a] shadow-card-shadow rounded-[20px]"
              >
                <CardContent className=" w-[204px] flex flex-col items-center gap-[19px] px-5 py-[30px]">
                  <div className="flex flex-col items-center gap-3">
                    <div className="relative w-[55.81px] h-[55.81px] bg-cover bg-[50%_50%]">
                      <div className="relative w-4 h-4 left-10 bg-[#d31b1b4c] rounded-[7.8px] z-10">
                        <div className="relative w-[11px] h-[11px] top-0.5 left-0.5 bg-[#D31B1B] rounded-[5.57px] " />
                      </div>
                      <Image
                        src="/Images/homepage/omar.png"
                        alt="user"
                        width={55.81}
                        height={55.81}
                        className="relative top-[-15px] h-[55.81px] w-[55.81px] "
                      />
                    </div>

                    <div className="flex flex-col items-center justify-center">
                      <h3 className="relative w-fit mt-[-1.00px] font-[ScandiaMedium] text-[16px] lg:text-[22px] text-[#181916]">
                        {member.name}
                      </h3>

                      <p className="relative w-fit font-[Scandia] text-[14px] text-[#303030]">
                        {member.profession}
                      </p>
                    </div>
                  </div>

                  <div className="flex justify-center gap-1 items-center">
                    <ClockIcon className="w-4 h-4 text-grey" />
                    <span className="relative w-fit mt-[-1.00px] font-[Scandia] text-[14px] text-[#303030] whitespace-nowrap">
                      Yesterday
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <div className="flex flex-col lg:flex-row w-full items-start justify-start md:items-center md:justify-center gap-16 py-3 px-6 md:px-16 md:py-[60px] relative bg-[#2F3B31] rounded-[30px] overflow-hidden">
        <div className="relative w-[216px] h-[216px] max-h-[268px]">
          <div className="relative top-6 left-1 hidden lg:flex">
            <Image 
              alt="Vector" 
              className="h-[216px] w-[216px]" 
              src="/Images/homepage/vector.png"
              width={216}
              height={216}
            />
          </div>

          <div className="relative top-6 left-1 flex lg:hidden">
            <Image 
              alt="Vector" 
              src="/Images/homepage/vectorsm.png"
              width={216}
              height={216}
            />
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-[24px] md:gap-20 flex-1 items-center relative">
          <div className="flex flex-col items-start relative flex-1">
            <h2 className="relative self-stretch mt-[-1.00px] font-[ScandiaMedium] text-white text-[32px]">
              Join to give the impact
            </h2>

            <p className="relative self-stretch font-[Scandia] text-white text-[14px] md:text-[16px]">
              Join a community dedicated to rebuilding, empowering, and
              inspiring a brighter future for Syria.
            </p>
          </div>

          <div className="flex flex-col w-full md:w-fit items-start gap-5 relative mb-[28px] md:mb-0">
            <Button className="w-fit md:w-[239px] px-6 py-3 md:px-16 md:py-4 bg-[#D31B1B] rounded-[80px] h-auto cursor-pointer">
              <span className="mt-[-1.23px] font-[ScandiaMedium] text-[14px] md:text-[16px] text-white">
                As Organization
              </span>
            </Button>

            <Button
              variant="default"
              className="w-fit md:w-[239px] px-6 py-3 md:px-16 md:py-4 bg-[#ECE7E3] rounded-[80px] h-auto cursor-pointer"
            >
              <span className="mt-[-1.23px] font-[ScandiaMedium] text-[14px] md:text-[16px] text-[#181916]">
                As Individual
              </span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
export default DivByAnima;
