"use client";
import React, { useState } from 'react';
import Image from 'next/image';
import {
    Box,
    Typography,
    Checkbox,
    Divider,
    Avatar,
    FormControlLabel
} from "@mui/material";
import "./ProjectDetail.css";

const paymentMethods = [
    {
        value: "crypto",
        label: "Crypto payment",
        img: "/Img/confirmPayment/unselected.svg",
        imgSelected: "/Img/confirmPayment/Selected.svg",
        text: "Crypto payment",
        icon: "/Img/arrow.svg",
    },
    {
        value: "mastercard",
        label: "MasterCard ···· 2251",
        img: "/Img/confirmPayment/unselected.svg",
        imgSelected: "/Img/confirmPayment/Selected.svg",
        text: "MasterCard ···· 2251",
        icon: "/Img/mastercrd.svg",
    },
    {
        value: "visa",
        label: "Visa ···· 7895",
        img: "/Img/confirmPayment/unselected.svg",
        imgSelected: "/Img/confirmPayment/Selected.svg",
        text: "Visa ···· 7895",
        icon: "/Img/visa.svg"
    }
];

const ConfirmPayment = () => {
    const [selected, setSelected] = useState("mastercard");
    const [checked, setChecked] = useState(true);
    const [showAddCardForm, setShowAddCardForm] = useState(false);
    const [cardName, setCardName] = useState("");
    const [cardNumber, setCardNumber] = useState("");
    const [expiry, setExpiry] = useState("");
    const [ccv, setCCV] = useState("");
    
    return (
        <Box mt={15} className="mcncghfjs" mb={4} bgcolor="#FCFCFC">
            <p
                className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31] mb-[24px] flex items-center gap-1  dhakghghsfs"
                style={{ fontFamily: "ScandiaMedium" }}
            >
                <span>Project</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={16} height={16} />
                <span>Homes for Hope</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={16} height={16} />
                <span>Make a donation</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={16} height={16} />
                <span>Confirm payment</span>
            </p>
            <div className="tynxrw3mauluilqyrrt">
                {/* Left Panel */}
                <div>
                    {/* Project Info */}
                    <div className="hsdjhhjhddjhfdf">
                        <Box display="flex" alignItems="center" gap={1} mb={2} mt={1} className="ghdfgdfsghdhj">
                            <Avatar src="/Img/city.png" sx={{ width: "48px", height: "48px" }} />
                            <div>
                                <p
                                    className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Rebuild Syria Network</p>
                                <p
                                    className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                                    style={{ fontFamily: "Scandia" }}
                                >Infrastructure & development</p>
                            </div>
                        </Box>

                        <Box className="flex items-start gap-4 mt-[44px] ghdfgdfsghdhj">
                            <Image 
                                src="/Img/Syrian house (1).png" 
                                alt="Project thumbnail" 
                                width={80} 
                                height={80}
                                style={{ borderRadius: "14px" }} 
                                className='sgfyuvuimok' 
                            />
                            <Box>
                                <p
                                    className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#181916] mb-[16px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Homes for Hope</p>
                                <p
                                    className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mb-[16px] m-[0px]" 
                                    style={{ fontFamily: "Scandia" }}
                                >
                                    Reconstructing homes for displaced Syrian families, focusing on sustainable and durable housing.
                                </p>
                            </Box>
                        </Box>
                    </div>
                    <div className="hsdjhhjhddjhfdf mt-[24px]">
                        <p
                            className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >
                            Payment method
                        </p>
                        <Box mt={2}>
                            {paymentMethods.map((method) => (
                                <Box
                                    key={method.value}
                                    className={`flex items-center justify-between mb-2 rounded-xl cursor-pointer transition ${selected === method.value ? "bg-[#f4f4f4]" : " bg-transparent"}`}
                                    p={3}
                                    onClick={() => setSelected(method.value)}
                                >
                                    <Box className="flex items-center gap-3">
                                        <Image
                                            src={selected === method.value ? method.imgSelected : method.img}
                                            alt={method.text}
                                            width={24}
                                            height={24}
                                            style={{ borderRadius: 6 }}
                                        />
                                        <Typography
                                            fontWeight={selected === method.value ? 500 : 400}
                                            color={selected === method.value ? "#000000" : "#181916"}
                                            sx={{
                                                fontFamily: selected === method.value ? "ScandiaMedium" : "Scandia",
                                                fontSize: 16,
                                                letterSpacing: 0,
                                            }}
                                        >
                                            {method.text}
                                        </Typography>
                                    </Box>
                                    <Image src={method.icon} alt="selected" width={16} height={16} />
                                </Box>
                            ))}
                        </Box>
                        {!showAddCardForm ? (
                            <div className='flex items-center gap-2 mt-4 mb-4' style={{ cursor: 'pointer' }} onClick={() => setShowAddCardForm(true)}>
                                <Image src="/Img/confirmPayment/plus.svg" alt="Add payment method" width={16} height={16} />
                                <p
                                    className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#D31B1B] m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Add new payment method</p>
                            </div>
                        ) : (
                            <Box className="rounded-[16px] bg-[#FAFAFA] p-[24px] mt-4 mb-4" boxShadow={0}>
                                <Typography fontWeight={500} fontSize={18} mb={3} style={{ fontFamily: "ScandiaMedium" }}>
                                    Add new payment method
                                </Typography>
                                <Box mb={2}>
                                    <Typography fontWeight={500} fontSize={14} mb={0.5} style={{ fontFamily: "ScandiaMedium" }}>
                                        Card holder name
                                    </Typography>
                                    <input
                                        type="text"
                                        placeholder="Enter card holder name"
                                        value={cardName}
                                        onChange={e => setCardName(e.target.value)}
                                        className="w-full border-0 border-b border-[#ECE7E3] bg-transparent py-2 px-0 outline-none text-[15px] text-[#181916]"
                                        style={{ fontFamily: "Scandia", marginBottom: 12 }}
                                    />
                                </Box>
                                <Box mb={2}>
                                    <Typography fontWeight={500} fontSize={14} mb={0.5} style={{ fontFamily: "ScandiaMedium" }}>
                                        Card number
                                    </Typography>
                                    <input
                                        type="text"
                                        placeholder="Enter card number"
                                        value={cardNumber}
                                        onChange={e => setCardNumber(e.target.value)}
                                        className="w-full border-0 border-b border-[#ECE7E3] bg-transparent py-2 px-0 outline-none text-[15px] text-[#181916]"
                                        style={{ fontFamily: "Scandia", marginBottom: 12 }}
                                    />
                                </Box>
                                <Box className="flex gap-4">
                                    <Box flex={1}>
                                        <Typography fontWeight={500} fontSize={14} mb={0.5} style={{ fontFamily: "ScandiaMedium" }}>
                                            Expiry date
                                        </Typography>
                                        <input
                                            type="text"
                                            placeholder="01/25"
                                            value={expiry}
                                            onChange={e => setExpiry(e.target.value)}
                                            className="w-full border-0 border-b border-[#ECE7E3] bg-transparent py-2 px-0 outline-none text-[15px] text-[#181916]"
                                            style={{ fontFamily: "Scandia", marginBottom: 12 }}
                                        />
                                    </Box>
                                    <Box flex={1}>
                                        <Typography fontWeight={500} fontSize={14} mb={0.5} style={{ fontFamily: "ScandiaMedium" }}>
                                            CCV
                                        </Typography>
                                        <input
                                            type="text"
                                            placeholder="Enter CCV"
                                            value={ccv}
                                            onChange={e => setCCV(e.target.value)}
                                            className="w-full border-0 border-b border-[#ECE7E3] bg-transparent py-2 px-0 outline-none text-[15px] text-[#181916]"
                                            style={{ fontFamily: "Scandia", marginBottom: 12 }}
                                        />
                                    </Box>
                                </Box>
                                <Box mt={3} className="flex gap-2">
                                    <button
                                        className="font-[500] text-[14px] leading-[20px] tracking-[0%] w-[120px] text-[#ffffff] flex items-center justify-center text-center bg-[#D31B1B] rounded-[80px] border-0 h-[40px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >Add Card</button>
                                    <button
                                        className="font-[500] text-[14px] leading-[20px] tracking-[0%] w-[120px] text-[#181916] flex items-center justify-center text-center bg-[#ECE7E3] rounded-[80px] border-0 h-[40px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                        type="button"
                                        onClick={() => setShowAddCardForm(false)}
                                    >Cancel</button>
                                </Box>
                            </Box>
                        )}
                    </div>
                </div>

                {/* Right Panel */}
                <div className="hsdjhhjhddjhfdf">
                    <p className="font-[500] text-[28px] leading-[36px] tracking-[0%] text-[#000000] mb-4 m-[0px]"
                        style={{ fontFamily: "ScandiaMedium" }}>
                        Donation Summary
                    </p>
                    <Box mt={2} className="flex justify-between mb-2">
                        <p
                            className="font-[400] text-[16px] leading-[20px] tracking-[0%] text-[#303030] m-[0px]" 
                            style={{ fontFamily: "Scandia" }}
                        >Donation amount</p>
                        <p
                            className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#303030] mb-[16px]"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >$ 1,000</p>
                    </Box>
                    <Box className="flex justify-between text-sm mb-2">
                        <p
                            className="font-[400] text-[16px] leading-[20px] tracking-[0%] text-[#303030] m-[0px]" 
                            style={{ fontFamily: "Scandia" }}
                        >Fee</p>
                        <p
                            className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#303030] mb-[16px]"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >Free</p>
                    </Box>
                    <Divider className="my-2" />
                    <Box pt={3} mt={2} className="flex justify-between mb-4" style={{ borderTop: "1px solid #ECE7E3" }}>
                        <p
                            className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#303030]"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >Total</p>
                        <p
                            className="font-[500] text-[22px] leading-[32px] tracking-[0%] text-[#181916]"
                            style={{ fontFamily: "ScandiaMedium" }}
                        >$ 1,000</p>
                    </Box>

                    <FormControlLabel
                        control={
                            <Checkbox
                                checked={checked}
                                onChange={(e) => setChecked(e.target.checked)}
                                icon={
                                    <Image
                                        src="/Img/unchecked.svg"
                                        alt="unchecked"
                                        width={24}
                                        height={24}
                                    />
                                }
                                checkedIcon={
                                    <Image
                                        src="/Img/checked.svg"
                                        alt="checked"
                                        width={24}
                                        height={24}
                                    />
                                }
                                sx={{ color: "#d32f2f" }}
                            />
                        }
                        label={<p className="font-[500] text-[12px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                            style={{ fontFamily: "ScandiaMedium" }}>Make this a monthly donation</p>}
                    />
                    <div className="rounded-[12px] py-[12px] px-[16px] mt-[20px] 20px" style={{ background: "#f8f8f8" }}>
                        <p className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#303030] m-[0px]" style={{ fontFamily: "Scandia" }}>
                            <span className="text-[#23232366]"> This donation will be processed </span> monthly <span className="text-[#23232366]">until </span>  June 2025<span className="text-[#23232366]"> or until you update your donation preferences at any time.</span>
                        </p>
                    </div>
                    <button
                        className="font-[500] text-[14px] leading-[20px] tracking-[0%] w-[177px] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-[#D31B1B] rounded-[80px] border-0 h-[49px] mt-[40px]"
                        style={{ fontFamily: "ScandiaMedium" }}
                    >Continue Payment</button>
                </div>
            </div>
        </Box>
    );
};

export default ConfirmPayment;