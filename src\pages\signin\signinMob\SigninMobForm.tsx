'use client';
import Button from "@/components/button/Button";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

interface SigninMobFormProps {
  active: "signIn" | "register";
  setActive: React.Dispatch<React.SetStateAction<"signIn" | "register">>;
}

function SigninMobForm({ active, setActive }: SigninMobFormProps) {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  
  const [errors, setErrors] = useState({
    email: "",
    password: "",
  });

  const validate = () => {
    const newErrors: { email?: string; password?: string } = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    let isValid = true;

    if (!email) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!emailRegex.test(email)) {
      newErrors.email = "Enter a valid email";
      isValid = false;
    }

    if (!password) {
      newErrors.password = "Password is required";
      isValid = false;
    }

    setErrors(newErrors as { email: string, password: string });
    return isValid;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validate()) {
      router.push("/Auth/SignIn/forget-password");
      console.log("Signed in with:", { email, password });
    }
  };

  const handleForget = () => {
    router.push("/Auth/SignIn/forget-password");
  };

  const toggleForm = () => {
    setActive(active === "signIn" ? "register" : "signIn");
  };

  return (
    <div className="w-full">
      <form className="space-y-[52px] md:space-y-[40px]" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-[32px]">
          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="email"
              className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                background: "transparent",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.email && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left font-[scandiaMedium] text-center"
              >
                {errors.email}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                color: "rgba(48, 48, 48, 1)",
                paddingTop: "0px",
                paddingBottom: "8px",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] custom-footer-placeholde"
            />
            {errors.password && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.password}
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-[24px] justify-center text-center">
          <Button type="submit">
            <p className="text-[14px] text-[white] font-[scandiaMedium]">
              {active === "signIn" ? "Sign in" : "Register"}
            </p>
          </Button>

          <div className="flex flex-col gap-2">
            <p
              className="text-[12px] leading-[16px] text-[black] mb-[2px] font-[scandiaMedium] hover:text-[#ec1c24] cursor-pointer"
              onClick={handleForget}
            >
              Forgot password
            </p>
            <p
              className="text-[12px] font-normal leading-[16px] text-[black] font-[scandiaMedium] hover:text-[#ec1c24] cursor-pointer"
              onClick={toggleForm}
            >
              {active === "signIn" ? "Don't have an account? Register" : "Already have an account? Sign in"}
            </p>
          </div>
        </div>
      </form>
    </div>
  );
}

export default SigninMobForm;
