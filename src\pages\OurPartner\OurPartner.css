.our-partner-container {
  display: flex;
  flex-direction: column;

  gap: 64px;
  background-color: white;
}
.our-partner-section {
  position: relative;
  background: url("/Images/ourpartner/backgroundourpartner.png") center
    center/cover no-repeat;
  min-height: 560px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.our-partner-overlay {
  width: 100%;
  height: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  gap: 80px;
  margin-top: 100;
}

.our-partner-title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: center;
  vertical-align: middle;
  color: #ffffff;
}

.our-partner-logos {
  display: flex;
  flex-wrap: wrap;
  gap: 32px 40px;
  width: 100%;
  max-width: 1164px;
  align-items: center;
  justify-content: center;
}

.our-partner-logos img {
  object-fit: contain;
  filter: brightness(0) invert(1);
  transition: transform 0.2s;
  max-width: 120px; /* optional, to preserve similar sizing */
  flex: 0 1 120px; /* grow: 0, shrink: 1, basis: 120px */
}

.our-partner-logos img:hover {
  transform: scale(1.08);
}

.benefit-partner-section {
  margin: 36px auto;
  max-width: 1200px;
  padding: 0 20px;
  display: flex;
  flex-direction: column;
  gap: 64px;
}
.benefit-partner-title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: left;
  vertical-align: middle;
  color: #181916;
  padding-bottom: 49px;
}
.benefit-partner-content {
  display: flex;
  flex-direction: column;
  gap: 60px;
}
.benefit-partner-row {
  display: flex;
  align-items: center;
  gap: 80px;
}
.benefit-partner-row.reverse {
  flex-direction: row-reverse;
}
.benefit-partner-card-wrapper {
  flex: 1 1 80px;
  display: flex;
  justify-content: center;
  background: #ece7e3;
  border-radius: 16px;
  position: relative;
  height: 320px;
}
.card__image__partner {
  position: absolute;
  top: -49px;
}

.benefit-partner-text {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1 1 320px;
}
.benefit-partner-text h3 {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  color: #181916;
}
.benefit-partner-text p {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  color: #303030;
}
.showcase__commitment__container {
  display: flex;
  flex-direction: column;
  gap: 40px;
  background: #b7d084;
  padding: 80px;
  justify-content: center;
  align-items: center;
}
.showcase__commitment__container1 {
  display: flex;
  flex-direction: column;
  gap: 16px;

  align-items: center;
}
.showcase__commitment__text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.showcase__commitment__text_detail {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  vertical-align: middle;
  color: #303030;
  max-width: 900px;
}
.access-exlusive-main-container {
  display: flex;
  flex-direction: column;
  gap: 64px;
  justify-content: center;
  align-items: center;
  padding-bottom: 64px;
  margin-top: 64px;
}
.access-exlusive-container {
  background: #ece7e3;
  border-radius: 16px;
  flex: 80px;
  height: 320px;
  display: flex;
  position: relative;
  width: 486px;
}
.card__image__partner2 {
  position: absolute;
  left: 269px;
}
.access-exlusive-containertext {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 900px;
}
.access-exlusive-text {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 40px;
  line-height: 48px;
  text-align: center;
  vertical-align: middle;
  color: #181916;
}
.access-exlusive-text_detail {
  font-family: Scandia;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
  text-align: center;
  vertical-align: middle;
  color: #303030;
}

@media (max-width: 900px) {
  .benefit-partner-row,
  .benefit-partner-row.reverse {
    flex-direction: column !important;
    gap: 24px;
    align-items: stretch;
  }
  .benefit-partner-card-wrapper,
  .benefit-partner-text {
    flex: unset;
    width: 100%;
    margin-top: 64px;
  }
  .benefit-partner-card {
    width: 100%;
    min-width: 0;
  }
  .card__image__partner {
    height: 420px;
  }
  .benefit-partner-section {
    margin: 50px auto;
  }
  .access-exlusive-main-container {
    padding: 0 24px;
  }
  .our-partner-overlay {
    margin-top: 100px;
  }
}
@media (max-width: 600px) {
  .our-partner-title {
    font-size: 28px;
    line-height: 36px;
  }
  .our-partner-logos {
    gap: 32px 32px;
  }

  .our-partner-overlay {
    gap: 30px;
  }
  .our-partner-section {
    min-height: 470px;
  }
  .benefit-partner-section {
    padding: 0 24px;
  }
  .benefit-partner-title {
    font-size: 22px;
    margin-bottom: 24px;
    line-height: 32px;
    padding-bottom: 0px;
  }
  .benefit-partner-content {
    gap: 32px;
  }
  .benefit-partner-card {
    padding: 12px 8px 8px 8px;
  }
  .benefit-partner-img {
    height: 120px;
  }
  .benefit-partner-text h3 {
    font-size: 28px;
    line-height: 36px;
  }
  .benefit-partner-text p {
    font-size: 14px;
    line-height: 20px;
  }
  .benefit-partner-section {
    margin: 10px auto;
    gap: 20px;
  }
  .our-partner-container {
    gap: 30px;
  }
  .showcase__commitment__container {
    padding: 50px;
  }
  .showcase__commitment__text {
    font-size: 32px;
    line-height: 40px;
  }
  .showcase__commitment__text_detail {
    font-size: 14px;
    line-height: 20px;
  }
  .access-exlusive-container {
    width: 298px;
  }
  .card__image__partner1 {
    width: 235px;
    position: relative;
    left:-23px
  }
  .card__image__partner2 {
    width: 165px;
    left: 164px;
  }
  .showcase__commitment__image {
    height: 40px;
    width: 40px;
  }
  .access-exlusive-containertext {
    max-width: 345px;
  }
  .access-exlusive-text {
    font-size: 32px;
    line-height: 40px;
  }
  .access-exlusive-text_detail {
    font-size: 14px;
    line-height: 20px;
  }
  .benefit-partner-card-wrapper,
  .benefit-partner-text {
    margin-top: 30px;
  }
  .access-exlusive-main-container
  {
    margin-top: 30px;
  }
}
