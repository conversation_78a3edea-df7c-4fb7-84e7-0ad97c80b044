"use client";
import React, { useState } from "react";
import OrganizationCard from "@/components/OrganizationCard/OrganizationCard";
import OrganizationDetail from "./Organizationdetail";
import Donation from "./Donation";
import ConfirmPayment from "./confirmPayment";

interface OrganizationProps {
  onNavigate: (page: "list" | "details" | "donate" | "confirm" | "submit") => void;
}

function CommunityOrganizations({  }: OrganizationProps) {
  const [selectedOrg, setSelectedOrg] = useState<null | {
    id: string;
    name: string;
    title: string;
    imageUrl: string;
    actionLabel: string;
  }>(null);
 const [page, setPage] = useState<"list" | "details" | "donate" | "confirm" | "submit">("details");
  function handleNavigate(nextPage: "list" | "details" | "donate" | "confirm" | "submit" ) {
    setPage(nextPage);
  }
const handleCardClick = (org: typeof organizations[0]) => {
    setSelectedOrg(org);
    handleNavigate("details"); 
  };

  const handleBackToList = () => {
    setSelectedOrg(null);
    handleNavigate("list"); // <-- local handler here
  };

  if (page === "details" && selectedOrg) {
    return (
      <OrganizationDetail
        organization={selectedOrg}
        onBack={handleBackToList}
        onNavigate={handleNavigate}  // pass local handleNavigate
      />
    );
  }

  if (page === "donate") {
    return <Donation   onNavigate={handleNavigate} onBack={() => handleNavigate("details")} />;
  }
   if (page === "confirm") {
    return <ConfirmPayment   onNavigate={handleNavigate} onBack={() => handleNavigate("donate")} />;
  }

  const organizations = [
    {
      id: "1",
      name: "Syrian Orphan Charity",
      title: "Supports refugees and vulnerable communities with education, vocational training, and relief programs.",
      imageUrl: "/Images/CommunityMain/o1.png",
      actionLabel: "View organization"
    },
    {
      id: "2",
      name: "Syrian Arab Red Crescent (SARC)",
      title: "Provides humanitarian aid, medical support, and disaster relief across Syria.",
      imageUrl: "/Images/CommunityMain/o2.png",
      actionLabel: "Follow"
    },
    {
      id: "3",
      name: "White Helmets (Syria Civil Defence)",
      title: "A volunteer organization focused on search and rescue, emergency response, and humanitarian aid.",
      imageUrl: "/Images/CommunityMain/o3.png",
      actionLabel: "Follow"
    },
    {
      id: "4",
      name: "Basmeh & Zeitooneh",
      title: "Supports refugees and vulnerable communities with education, vocational training, and relief programs.",
      imageUrl: "/Images/CommunityMain/o4.png",
      actionLabel: "Follow"
    },
    {
      id: "5",
      name: "Molham Volunteering Team",
      title: "Offers emergency aid, housing, and educational support for displaced Syrians.",
      imageUrl: "/Images/CommunityMain/o2.png",
      actionLabel: "Follow"
    },
    {
      id: "6",
      name: "Big Heart Foundation",
      title: "Focuses on child welfare, education, and humanitarian relief for Syrian communities.",
      imageUrl: "/Images/CommunityMain/o1.png",
      actionLabel: "Follow"
    }
  ];

 
  // // Render detail view if an organization is selected
  // if (selectedOrg) {
  //   return (
  //     <OrganizationDetail 
  //       organization={selectedOrg} 
  //       onBack={handleBackToList}
  //       onNavigate={(onPage) => onNavigate(onPage)}
  //     />
  //   );
  // }

  // Default list view
  return (

   <div className="bg-white rounded-[20px] flex flex-col md:gap-[34px] gap-[19px] p-[32px] max-[1000px]:px-[0]  shadow-[4px_12px_24px_0px_rgba(0,0,0,0.04)] max-[1000px]:shadow-none">
      <div className="max-[1000px]:hidden flex gap-[8px]">
        <button
          style={{
            fontFamily: "ScandiaMedium",
            background: "#ECE7E3",
          }}
          className="font-[scandiaMedium] flex gap-[6px] items-center cursor-pointer text-[#181916] text-[14px] leading-[20px] rounded-[70px] border-none py-[16px] px-[24px]"
        >
          <img src="/Images/CommunityMain/filter.svg" alt="filter" /> Filter
        </button>
        <div
          className="flex items-center gap-[8px] w-[285px] rounded-[99px] border"
          style={{ border: "1px solid #23232366" }}
        >
          <img
            src="/Images/CommunityMain/search.svg"
            alt="search"
            className="w-[16px] h-[16px] ml-[24px]"
          />
          <input
            className="bg-transparent flex-1 focus:outline-none focus:ring-0 placeholder:font-[scandia] text-[14px] font-[ScandiaMedium] py-[13px] pr-[24px] placeholder-[#23232366]"
            placeholder="Enter email"
          />
        </div>
      </div>
      <div className="max-[1000px]:flex hidden justify-between">
        <p className="text-[#181916] text-[22px] leading-[32px] font-[ScandiaMedium]">
          Organization
        </p>
        <div className="flex gap-[12px]">
          <img
            src="/Images/CommunityMain/search.svg"
            alt="search"
            className="w-[24px] h-[24px] cursor-pointer"
          />
          <img
            src="/Images/CommunityMain/filter.svg"
            alt="filter"
            className="w-[24px] h-[24px] cursor-pointer"
          />
        </div>
      </div>
      <img
        style={{ width: "100%" }}
        src="/Images/CommunityMain/seperator.png"
        alt="seperator"
      />
      <div className="flex flex-col sm:gap-[32px] gap-[18px]">
        <div className="flex flex-col gap-[24px] w-full">
          {organizations.map((org) => (
            <OrganizationCard
              key={org.id}
              name={org.name}
              title={org.title}
              imageUrl={org.imageUrl}
              actionLabel={org.actionLabel}
              onClick={() => handleCardClick(org)}
            />
          ))}
        </div>
        <img
          style={{ width: "100%" }}
          className="block sm:hidden"
          src="/Images/CommunityMain/seperator.png"
          alt="seperator"
        />
        <div className="flex w-full flex-col items-center">
          <p className="text-[#303030] text-[14px] leading-[20px] font-[ScandiaMedium] cursor-pointer">
            Load more
          </p>
        </div>
      </div>
    </div>
  );
}

export default CommunityOrganizations;