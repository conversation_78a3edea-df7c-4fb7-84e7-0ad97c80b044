"use client";

// Remove unused imports
import { useRouter } from "next/navigation";
import { useState } from "react";
import "./forgetPass.css"

function FortgetpassWeb() {
  // Remove unused state variables
  const [email, setEmail] = useState<string>("");
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [otpSent, setOtpSent] = useState(false);
  const [code, setCode] = useState(['', '', '', '']);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  const handleSendOtp = () => {
    if (!email) return;

    setLoading(true);
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      setOtpSent(true);
    }, 1500);
  };

  const handleCodeChange = (e: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const { value } = e.target;

    if (value.length <= 1) {
      const updatedCode = [...code];
      updatedCode[index] = value;
      setCode(updatedCode);

      if (value !== '' && index < code.length - 1) {
        document.getElementById(`input-${index + 1}`)?.focus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (e.key === 'Backspace') {
      const updatedCode = [...code];

      if (code[index] === '') {
        if (index > 0) {
          document.getElementById(`input-${index - 1}`)?.focus();
        }
      } else {
        updatedCode[index] = '';
        setCode(updatedCode);
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").slice(0, 4); // Get first 4 characters
    if (pastedData.length > 0) {
      const updatedCode = pastedData.split("");
      while (updatedCode.length < 4) {
        updatedCode.push("");
      }
      setCode(updatedCode);


      setTimeout(() => {
        document.getElementById(`input-${pastedData.length - 1}`)?.focus();
      }, 10);
    }
  };

  const handleVerifyOtp = () => {
    setLoading(true);
    console.log("Navigating to new password page");
    router.push("/Auth/SignIn/new-password-confrim");
    setLoading(false);
  }

  // useEffect(() => {
  //   if (seconds <= 0) {
  //     router.push("/Auth/SignIn/new-password");
  //     return;
  //   }
  
  //   const interval = setInterval(() => {
  //     setSeconds((prev) => prev - 1);
  //   }, 1000);
  
  //   return () => clearInterval(interval);
  // }, [seconds, router]);

  return (
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:rounded-[20px] max-[370px]:px-[18px] max-[919px]:py-[40px] flex-col items-center max-[919px]:w-full w-[40%] max-[919px]:gap-[24px] gap-[40px]">
      <div>
        {/* Replace the img tag on line 101 with the Next.js Image component */}
        {/* <Image
          src="/Images/signin/square-lock-02.svg"
          alt="lock"
          width={64}
          height={64}
          className="max-[919px]:w-[40px] max-[919px]:h-[40px]"
        /> */}
      </div>
      <div className="flex flex-col gap-[12px]">
        <p className="max-[919px]:text-[22px] text-[32px] max-[919px]:leading-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
          Forgot password
        </p>

        {!otpSent ? (
          <div className="flex flex-col gap-[16px]">
            <div className="space-y-[8px] flex flex-col">
              <label
                htmlFor="email"
                className="text-[14px] leading-[14px] font-[scandiaMedium]"
                style={{ color: "rgba(48, 48, 48, 1)" }}
              >
                Email
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={handleChange}
                placeholder="Enter email"
                style={{
                  borderTop: "none",
                  borderLeft: "none",
                  borderRight: "none",
                  borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                  fontFamily: "Scandia",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#2F3B31",
                }}
                className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
              />
            </div>
            <div
              style={{
                display: "flex", alignItems: "center", justifyContent: "center",
              }}
            >
              <button
                style={{
                  height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
                  display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
                }}
                onClick={handleSendOtp}
              >
                {loading ? (
                  <div className="loader"></div>
                ) : (
                  <p className="text-[14px] text-[white] font-[scandiaMedium]">
                    Send Otp
                  </p>
                )}
              </button>
            </div>
          </div>
        ) : (
          <div>
            <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
              Otp confirmation has been sent to
              <span className="text-[14px] cursor-pointer font-[scandiaMedium] text-[black]">
                {" "}
                {email || "<EMAIL>"}
              </span>
              <br className="hiden-block-stlying" /> Check your inbox to create new
              password.
            </p>

            <div className="input-group flex justify-center gap-2 my-4">
              {code.map((value, index) => (
                <input
                  key={index}
                  id={`input-${index}`}
                  type="text"
                  maxLength={1}
                  value={value}
                  className="code-input w-12 h-12 text-center border border-gray-300 rounded-md focus:outline-none focus:border-[#D31B1B]"
                  onChange={(e) => handleCodeChange(e, index)}
                  onKeyDown={(e) => handleKeyDown(e, index)}
                  onPaste={index === 0 ? handlePaste : undefined}
                />
              ))}
            </div>

            <div className="flex flex-col gap-[12px] mt-4">
              {/* <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
                Didn't receive any email?
              </p> */}

              <div
                className="flex justify-center gap-[16px]"
              >


                <button
                  style={{
                    height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
                    display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
                  }}
                >
                  {loading ? (
                    <div className="loader"></div>
                  ) : (
                    <p className="text-[14px] font-[scandiaMedium] text-[white]">
                      Resend in {/* Some comment or code */}
                    </p>
                  )}
                </button>

                <button
                  style={{
                    height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
                    display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
                  }}
                >
                  {loading ? (
                    <div className="loader"></div>
                  ) : (
                    <p
                      className="text-[14px] text-[white] font-[scandiaMedium]"
                      onClick={handleVerifyOtp}
                    >
                      Register
                    </p>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FortgetpassWeb;
