import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../services/apiInterceptor";
import { API_URL } from "../../client";

export const changePassword = createAsyncThunk("changePassword", async (data) => {
  try {
    console.log("changePassword",data);
    const res = await api.post(`${API_URL}/auth/new-password`,data);
    return {
      status: res?.status,
      data: res?.data?.data,
      token: res?.data?.token,
    };
  } catch (error) {
    return {
      message: error?.response?.data?.error,
      status: error?.response?.status,
    };
  }
});