import React from "react";
import Image from "next/image";
import Navbar from "../../components/navbar/Navbar";
import  BecomePartnerByAnima  from "../homepage/sections/BecomePartnerByAnima/BecomePartnerByAnima";
import "./OurPartner.css";

function OurPartner() {
  return (
    <div>
      <Navbar />
      <div className="our-partner-container">
        <div className="our-partner-section">
          <div className="our-partner-overlay">
            <h2 className="our-partner-title">Our Partner</h2>
            <div className="our-partner-logos">
              <Image
                src="/Images/ourpartner/snowflake.svg"
                alt="Snowflake"
                width={143}
                height={32}
              />
              <Image
                src="/Images/ourpartner/paloato.svg"
                alt="palo alto"
                width={171}
                height={32}
              />
              <Image
                src="/Images/ourpartner/togather.svg"
                alt="togather"
                width={159}
                height={32}
              />
              <Image
                src="/Images/ourpartner/linkedin.svg"
                alt="linkedin"
                width={131}
                height={32}
              />
              <Image
                src="/Images/ourpartner/liquid.svg"
                alt="liquid"
                width={110}
                height={32}
              />
              <Image
                src="/Images/ourpartner/address.svg"
                alt="address"
                width={139}
                height={32}
              />
              <Image
                src="/Images/ourpartner/nca.svg"
                alt="nca"
                width={158}
                height={32}
              />
              <Image
                src="/Images/ourpartner/monamed.svg"
                alt="monamed"
                width={131}
                height={32}
              />
              <Image
                src="/Images/ourpartner/octaai.svg"
                alt="octaai"
                width={123}
                height={32}
              />
              <Image
                src="/Images/ourpartner/databrick.svg"
                alt="databrick"
                width={202}
                height={32}
              />
              <Image
                src="/Images/ourpartner/ycombinator.svg"
                alt="ycombinator"
                width={135}
                height={32}
              />
            </div>
          </div>
        </div>
        {/* Benefit Become a Partner Section */}
        <section className="benefit-partner-section">
          <h2 className="benefit-partner-title">Benefit Become a Partner</h2>
          <div className="benefit-partner-content">
            <div className="benefit-partner-row">
              <div className="benefit-partner-card-wrapper">
                <Image
                  className="card__image__partner"
                  src="/Images/ourpartner/card1.png"
                  alt="card"
                  width={370}
                  height={448}
                />
              </div>
              <div className="benefit-partner-text">
                <h3>Empower Syrian Communities Globally</h3>
                <p>
                  Join a platform dedicated to connecting Syrians worldwide,
                  creating impactful projects that contribute to Syria&apos;s
                  rebuilding and future prosperity.
                </p>
              </div>
            </div>
            <div className="benefit-partner-row reverse">
              <div className="benefit-partner-card-wrapper">
                <Image
                  className="card__image__partner"
                  src="/Images/ourpartner/card2.png"
                  alt="card"
                  width={370}
                  height={448}
                />
              </div>
              <div className="benefit-partner-text">
                <h3>Expand Your Network and Influence</h3>
                <p>
                  Collaborate with a diverse global community, fostering
                  partnerships that drive meaningful change while growing your
                  organization&apos;s reach and influence.
                </p>
              </div>
            </div>
          </div>
        </section>
        <div className="showcase__commitment__container">
          <Image
          className="showcase__commitment__image"
            src="/Images/ourpartner/commitment.svg"
            alt="commitment"
            width={80}
            height={80}
          />
          <div className="showcase__commitment__container1">
            <p className="showcase__commitment__text">
              Showcase Your Commitment to Social Impact
            </p>
            <p className="showcase__commitment__text_detail">
              Partnering with Iltezam highlights your dedication to humanitarian
              efforts, enhancing your brand’s reputation through visible,
              purpose-driven initiatives.
            </p>
          </div>
        </div>
        <div className="access-exlusive-main-container">
          <div className="access-exlusive-container">
            <Image
              className="card__image__partner1"
              src="/Images/ourpartner/card3.png"
              alt="card"
              width={364}
              height={260}
            />
              <Image
              className="card__image__partner2"
              src="/Images/ourpartner/card4.png"
              alt="card"
              width={270}
              height={368}
            />
          </div>
          <div className="access-exlusive-containertext">
         <p className="access-exlusive-text">
          Access Exclusive Resources and Opportunities
         </p>
         <p className="access-exlusive-text_detail">
          Gain access to valuable tools, project collaborations, and funding opportunities that support both your goals and the broader mission of rebuilding Syria.
         </p>
          </div>
        </div>
        <BecomePartnerByAnima />
      </div>
    </div>
  );
}

export default OurPartner;
