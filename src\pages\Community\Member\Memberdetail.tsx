import Image from "next/image";
import React, { useState } from "react";

interface MemberDetailProps {
  member: {
    id: string;
    name: string;
    title: string;
    imageUrl: string;
  };
  onBack: () => void;
}

const buttonsGroup1 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
const buttonsGroup2 = [
  {
    value: 120,
    defaultIcon: "/Images/community/comment-01.svg",
    activeIcon: "/Images/community/active-comment-01.svg",
  },
  {
    value: "1.2k",
    defaultIcon: "/Images/community/thumbs-up.svg",
    activeIcon: "/Images/community/active-thumbs-up.svg",
  },
  {
    value: 3,
    defaultIcon: "/Images/community/link-forward.svg",
    activeIcon: "/Images/community/active-link-forward.svg",
  },
];
// const buttonsGroup3 = [
//   {
//     value: 120,
//     defaultIcon: "/Images/community/comment-01.svg",
//     activeIcon: "/Images/community/active-comment-01.svg",
//   },
//   {
//     value: "1.2k",
//     defaultIcon: "/Images/community/thumbs-up.svg",
//     activeIcon: "/Images/community/active-thumbs-up.svg",
//   },
//   {
//     value: 3,
//     defaultIcon: "/Images/community/link-forward.svg",
//     activeIcon: "/Images/community/active-link-forward.svg",
//   },
// ];
// const buttonsGroup4 = [
//   {
//     value: 120,
//     defaultIcon: "/Images/community/comment-01.svg",
//     activeIcon: "/Images/community/active-comment-01.svg",
//   },
//   {
//     value: "1.2k",
//     defaultIcon: "/Images/community/thumbs-up.svg",
//     activeIcon: "/Images/community/active-thumbs-up.svg",
//   },
//   {
//     value: 3,
//     defaultIcon: "/Images/community/link-forward.svg",
//     activeIcon: "/Images/community/active-link-forward.svg",
//   },
// ];

function MemberDetail({ member, onBack }: MemberDetailProps) {
  // const [activeIndex, setActiveIndex] = useState<number | null>(null);
const [activeIndexGroup2, setActiveIndexGroup2] = useState<number | null>(null);
const [activeIndexGroup1, setActiveIndexGroup1] = useState<number | null>(null);
 

  // const handleClick = (index: number) => {
  //   if (activeIndex === index) {
  //     setActiveIndex(null); // toggle off
  //   } else {
  //     setActiveIndex(index); // activate
  //   }
  // };
  // const router = useRouter();
  // const handlecreate = () => {
  //   router.push("/Community/createpost");
  // };
  return (
    <div className="flex flex-col items-start gap-[24px]">
      {/* breadcrums */}
      <div className="max-[1000px]:hidden flex items-center  gap-[8px]">
        <button
          onClick={onBack}
          className="flex cursor-pointer text-center text-[#2F3B31] font-[ScandiaMedium]"
        >
          {/* <img src="/Images/CommunityMain/arrow-left.svg" alt="back" /> */}
          Members
        </button>
        <button
          onClick={onBack}
          className="flex cursor-pointer items-center gap-[8px] text-[#2F3B31] font-[ScandiaMedium]"
        >
          <Image src="/Images/Community/arrow-right-01.svg" alt="back" width={20} height={20} />
          {member?.name}
        </button>
      </div>
      {/* mainpage */}
      <div className="bg-[#FFFFFF] shadow:4px 12px 24px 0px #0000000A rounded-[20px]">
        <div
          style={{
            backgroundImage: "url('/Images/signin/form_partner.webp')",
            backgroundSize: "cover", // cover the whole div
            backgroundPosition: "center",
            borderTopLeftRadius: "20px",
            borderTopRightRadius: "20px",
            height:"200px",
            position:"relative",
          }}
          className=""
        >
          <div>
            <Image
              src={member?.imageUrl}
              alt={member?.name}
              width={120}
              height={120}
              style={{
                position:"absolute",
                bottom:"-45px",
                left:"32px"
              }}
              className="rounded-full object-cover"
            />
          </div>
        </div>
        {/* Member Profile */}
        <div className="flex flex-col gap-[16px] pt-[18px] p-[32px] max-[640px]:px-0">
          <div className="flex flex-col gap-[16px]">
            <div className="flex justify-between max-[1024px]:flex-start max-[1024px]:flex-col pt-[48px] gap-[20px] max-[370px]:items-start items-end my-[2px]">
              <div className="flex flex-col max-[1024px]:w-full  w-[50%] gap-[2px]">
                {" "}
                <h1 className="text-[22px] pb-[2px] leading-[32px] font-[scandiaMedium] ">
                  {member?.name}
                </h1>
                <p className="text-[14px] leading-[20px] font-[scandia] text-[#303030] mb-6">
                  {member?.title}
                </p>
                <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[#303030] mb-6">
                  Sidney, Australia
                </p>
              </div>
              <div className="flex justify-end max-[370px]:flex-col max-[1024px]:justify-start max-[1024px]:w-full w-[50%] gap-[8px]">
                <button>
                  <Image src="/Images/community/Tertiary.svg" alt="tertiary" width={42} height={42} />
                </button>
                <button className="flex border border-[#303030] rounded-[60px] gap-[6px] py-[13px] px-[24px]">
                  <Image
                    src="/Images/community/message-01.svg"
                    alt="message"
                    width={18}
                    height={18}
                  />
                  <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[#303030]">
                    Chat
                  </p>
                </button>{" "}
                <button className="bg-[#D31B1B] text-white px-[20px] py-[12px] text-[14px] leading-[20px] font-[scandiaMedium] rounded-full font-[ScandiaMedium]">
                  {member?.name === "Omar Al-Hakim" ||
                  member?.name === "Yousef Najjar" ||
                  member?.name === "Ahmad Fadel"
                    ? "View Profile"
                    : "Connect"}
                </button>
              </div>
            </div>{" "}
            <div className="flex max-[1050px]:flex-col max-[1050px]:items-start items-end justify-between gap-[15px]">
              <div className="flex  flex-col gap-[8px]">
                <h2 className="text-[14px] leading-[20px] font-[scandia] text-[#303030]">
                  Skills:
                </h2>
                <div className="flex justify-start flex-wrap gap-[4px]">
                  {[
                    "Building",
                    "AutoCAD",
                    "Architecture",
                  
                  ]?.map((skill) => (
                    <span
                      key={skill}
                      className="bg-[#F8F8F8] text-[14px] leading-[20px] font-[scandia] text-[#303030] px-[12px] py-[8px] rounded-[40px] "
                    >
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
              <div className="flex justify-end max-[1024px]:justify-start max-[1024px]:w-full flex-col w-[328px] gap-[8px]">
                <p className="text-right max-[1024px]:text-left text-[16px] leading-[24px] font-[scandiaMedium]">
                  2000+ Connections
                </p>
                <div className="flex justify-end max-[1024px]:justify-start gap-[8px]">
                  <div>
                    <p className="text-right max-[1024px]:text-left text-[12px] leading-[16px] font-[scandiaMedium]">
                      Rami Al-Saleh, Tariq Darwish, and 10 others <span className="text-right max-[1024px]:text-left text-[12px] leading-[16px] font-[scandia]">  joined this group</span>
                    </p>
                    
                  </div>
                  <Image src="/Images/community/Frame 1597884074.svg" alt="group members" width={55} height={24} />
                </div>
              </div>
            </div>
            <div className="flex flex-col gap-[8px]">
              <h2 className="text-[14px] leading-[20px] font-[scandia] text-[#303030]">
                Certifications:
              </h2>
              <div className="flex flex-wrap bg-[#FDF3D8] p-[8px] rounded-[40px] w-fit gap-[4px] items-center">
                <Image src="/Images/community/award-04.svg" alt="award icon" width={24} height={24} />
                <span className=" text-[10px] leading-[16px] font-[scandiaMedium] text-[#303030]  ">
                  Ethics for Engineers
                </span>
              </div>
            </div>
          </div>
          <div
            style={{
              borderTop: "none",
              borderLeft: "none",
              borderRight: "none",
            }}
            className="border border-b-[#ECE7E3] mb-[10px] pt-[10px]"
          >
            {" "}
          </div>
          {/* about */}
          <div>
            <h2 className="font-semibold leading-[24px] text-[16px] mb-3">
              About
            </h2>
            <p className="text-gray-700 text-[14px] leading-[20px]">
              {member?.name} is a {member?.title.toLowerCase()} with extensive
              experience in their field. They are passionate about making a
              difference in their community.
            </p>
          </div>
          <div
            style={{
              borderTop: "none",
              borderLeft: "none",
              borderRight: "none",
            }}
            className="border border-b-[#ECE7E3] mb-[10px] pt-[10px]"
          >
            {" "}
          </div>
          {/* repost */}
          <div className="flex flex-col gap-[16px]">
            <p className="text-[22px] pb-[4px] leading-[32px] font-[scandiaMedium]">
              {" "}
              Recent posts{" "}
            </p>
            <div className="bg-[white] w-full flex flex-col gap-[16px] border border-[#0000000A] text-[black] rounded-[20px] max-[919px]:py-[20px] max-[919px]:px-[16px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex-flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Tariq Darwish
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Architect
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="profile"
                    height={8}
                    width={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col max-[919px]:gap-[12px] gap-[16px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  Clean water is a right, not a privilege. 💧 Today, we
                  installed a new water filtration system in a refugee camp,
                  providing safe drinking water for over 500 people. Small
                  steps, big impact! Let&apos;s keep pushing forward for a healthier,
                  stronger Syria
                </p>
                 <div className="flex max-[1080px]:flex-col gap-[16px] w-full">
            <div className=" max-[1200px]:w-fit w-[60%] h-[395px] max-[800px]:h-auto max-[1000px]:w-full w-[603px] rounded-[12px]">
              <img
                src="/Images/community/feeds1.png"
                alt="profile"
               
                className="w-[100%] object-cover h-[100%] rounded-[20px]"
              />
            </div>
            <div className="flex flex-col max-[1080px]:flex-row max-[1080px]:w-[100%] max-[1200px]:w-fit  w-[40%] max-[800px]:w-full gap-[16px]">
              <img
                src="/Images/community/water filtration work in Syria.png"
                alt="profile"
                className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]: max-[800px]:h-auto"
              />
              <img
                src="/Images/community/Frame 1597884070.png"
                alt="profile"
                className="w-[277px] max-[1080px]:w-[50%] max-[800px]:w-[48%] object-cover h-[192.5px] rounded-[20px] max-[800px]:h-auto"
              />
            </div>
          </div>
                {/* <div className="flex flex-col responsive-box-mob max-[919px]:block hidden gap-[8px]">
                  <div className="flex max-[370px]:flex-col gap-[8px]">
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={8}
                      className="w-[50%] max-[370px]:w-full object-cover h-[50%] rounded-[12px]"
                    />
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={18}
                      className="w-[50%] max-[370px]:w-full object-cover h-[50%]rounded-[12px]"
                    />
                  </div>
                  <div className="flex max-[370px]:flex-col w-full gap-[8px]">
                    <Image
                      src="/Images/community/Frame 1597884070.svg"
                      alt="profile"
                      height={8}
                      width={8}
                      className="w-[50%] max-[370px]:w-full object-cover h-[50%] rounded-[12px]"
                    />
                    <Image
                      src="/Images/community/Frame 15978840702222.svg"
                      alt="profile"
                      height={8}
                      width={18}
                      className="w-[50%] max-[370px]:w-full h-[50%]rounded-[12px]"
                    />
                  </div>
                </div> */}
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 05.23 PM
                </p>
              </div>
              <div className="flex items-center gap-[8px]">
                {buttonsGroup2.map((item, index) => {
                  const isActive = activeIndexGroup2 === index;
                  return (
                    <button
                      key={index}
                      onClick={() => setActiveIndexGroup2(index)}
                      className={`${
                        isActive
                          ? "bg-[#2F3B31] text-white"
                          : "bg-[#ECE7E3] text-black"
                      } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                    >
                      <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                        {item.value}
                      </p>
                      <Image
                        src={isActive ? item?.activeIcon : item?.defaultIcon}
                        alt="icon"
                        height={18}
                        width={18}
                        className="w-[18px] h-[18px]"
                      />
                    </button>
                  );
                })}
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="profile"
                    height={8}
                    width={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item.activeIcon : item.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="profile"
                    height={8}
                    width={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item?.activeIcon : item?.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-[white] w-full flex flex-col gap-[16px] text-[black] border border-[#0000000A] rounded-[20px] max-[919px]:px-[16px] max-[919px]:py-[20px] p-[32px] hover:shadow-[4px_12px_24px_0px_#0000000A]">
              <div className="flex justify-between gap-[12px]">
                <div className="flex items-center max-[919px]:gap-[12px] gap-[16px] ">
                  <div className="w-[48px] h-[48px] rounded-full ">
                    <Image
                      src="/Images/community/image.svg"
                      alt="profile"
                      height={48}
                      width={48}
                    />
                  </div>
                  <div className="flex flex-col gap-[4px]">
                    <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                      Rania Mustafa
                    </p>
                    <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                      Lawyer
                    </p>
                  </div>
                </div>
                <div>
                  <Image
                    src="/Images/community/Frame 1597884065.svg"
                    alt="profile"
                    height={8}
                    width={18}
                    className="cursor-pointer"
                  />
                </div>
              </div>

              <div className="flex flex-col gap-[4px]">
                {" "}
                <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                  My heart is full today! I just became a volunteer teacher for
                  the Schools of Tomorrow project. So many children have been
                  out of school for years, and I can&apos;t wait to help them
                  rediscover the joy of learning. Education is the foundation of
                  a better future! 🎓✨
                </p>
                <p className="text-[#23232366] text-[14px] leading-[20px] font-[scandia]">
                  24 Feb 2025, 07.36 PM
                </p>
              </div>
              <div>
                <div>
                  <div className="flex items-center gap-[8px]">
                    {buttonsGroup1.map((item, index) => {
                      const isActive = activeIndexGroup1 === index;
                      return (
                        <button
                          key={index}
                          onClick={() => setActiveIndexGroup1(index)}
                          className={`${
                            isActive
                              ? "bg-[#2F3B31] text-white"
                              : "bg-[#ECE7E3] text-black"
                          } flex gap-[4px] items-center cursor-pointer py-[7px] px-[16px] rounded-[40px]`}
                        >
                          <p className="text-[14px] leading-[20px] font-[scandiaMedium]">
                            {item.value}
                          </p>
                          <Image
                            src={isActive ? item?.activeIcon : item?.defaultIcon}
                            alt="icon"
                            height={18}
                            width={18}
                            className="w-[18px] h-[18px]"
                          />
                        </button>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default MemberDetail;
