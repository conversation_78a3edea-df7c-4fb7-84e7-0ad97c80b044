.become-partner-section {
  position: relative;
  background: url("/Images/ourpartner/BecomePartner.png") center center/cover
    no-repeat;
  min-height: 684px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px 50px;
}

.become-partner-left {
  position: relative;
  z-index: 2;
  max-width: 648px;
  margin-right: 80px;
  color: #fff;
  display: flex;
  flex-direction: column;
  gap: 24px;
}
.become-partner-title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 68px;
  line-height: 80px;
  vertical-align: middle;
  color: #fff;
}
.become-partner-desc {
  font-family: Scandia;
  font-weight: 400;
  font-size: 22px;
  line-height: 32px;
  vertical-align: middle;
  color: #fff;
}
.become-partner-card {
  position: relative;
  z-index: 2;
  background: #fff;
  border-radius: 20px;
  padding: 40px;
  min-width: 400px;
  max-width: 350px;
  display: flex;
  flex-direction: column;
  align-items: stretcc;
  gap: 40px;
}
.become-partner-card-title {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  vertical-align: middle;
  text-align: left;
}
.become-partner-form {
  display: flex;
  flex-direction: column;
  gap: 18px;
}
.become-partner-form-group {
  display: flex;
  flex-direction: column;
}
.become-partner-label {
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #303030;
}
.become-partner-input {
  padding: 10px 0;
  border: none;
  border-bottom: 1.5px solid #dedede;
  font-family: Scandia;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #23232366;

  vertical-align: middle;

  outline: none;
  background: transparent;
}
.become-partner-input::placeholder {
  color: #a0a0a0;
  opacity: 1;
}
.become-partner-submit {
  margin-top: 22px;
  background: #d31b1b;
  color: #fff;
  border: none;
  border-radius: 80px;
  padding: 16px 0;
  font-family: ScandiaMedium;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: middle;

  cursor: pointer;
  transition: background 0.2s;
  width: 100%;
}
.become-partner-submit:hover {
  background: #c82333;
}
@media (max-width: 900px) {
  .become-partner-section {
    flex-direction: column;
    padding: 32px 50px;
    min-height: 0;
  }
  .become-partner-left {
    margin-right: 0;
    margin-bottom: 32px;
    max-width: 100%;
    text-align: center;
    align-items: center;
  }
  .become-partner-card {
    width: 100%;
    min-width: 0;
    max-width: 100%;
  }
}
@media (max-width: 600px) {
  .become-partner-section {
    flex-direction: column;
    padding: 32px 24px;
    min-height: 0;
  }

  .become-partner-title {
    font-size: 40px;
    line-height: 48px;
    text-align: left;
  }
  .become-partner-desc {
    font-size: 14px;
    line-height: 20px;
    text-align: left;
  }
  .become-partner-card {
    width: 100%;
    max-width: 100%;

    padding: 32px 24px;
    gap: 32px;
  }
  .become-partner-card {
    margin-top: 0px;
  }
  .become-partner-card-title {
    font-size: 28px;
    line-height: 36px;
  }
}
