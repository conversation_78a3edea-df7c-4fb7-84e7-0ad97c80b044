"use client";
import Button from "@/components/button/Button";
import { useRouter } from "next/navigation";
import { useState } from "react";

function CreateNewPassWeb() {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState("");
  const router = useRouter();
  const validatePassword = (password: string) => {
    const hasLetter = /[a-zA-Z]/.test(password);
    const hasNumber = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
    const isLongEnough = password.length >= 8;
    return hasLetter && hasNumber && hasSpecialChar && isLongEnough;
  };

  const handleNewPass = () => {
    setError(""); // reset error first

    if (!newPassword || !confirmPassword) {
      setError("Please fill in both fields.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("Passwords do not match.");
      return;
    }

    if (!validatePassword(newPassword)) {
      setError(
        "Password must contain at least one letter, one number, and one special character."
      );
      return;
    }
    // If all checks pass, redirect
    router.push("/Auth/SignIn/new-password-confrim");
  };

  return (
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:py-[40px] max-[919px]:rounded-[20px] flex-col max-[919px]:w-full xl:w-[410px] max-[919px]:gap-[20px] gap-[40px]">
      <p className="text-[32px] max-[919px]:text-[22px] max-[370px]:text-[20px] max-[919px]:leading-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-start">
        Create new password
      </p>
      <form
        className="max-[919px]:space-y-[52px] space-y-[40px]"
        onSubmit={(e) => {
          e.preventDefault();
          handleNewPass();
        }}
      >
        <div className="flex flex-col gap-[32px]">
          <div className=" flex flex-col">
            <label
              htmlFor="email"
              className="text-[14px] leading-[20px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              New password
            </label>
            <input
              id="new-password"
              type="password"
              placeholder="Enter password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[8px] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[ rgba(35, 35, 35, 0.4)] text-[ rgba(35, 35, 35, 0.4)]  custom-footer-placeholde"
            />
          </div>

          <div className="flex flex-col">
            <label
              htmlFor="password"
              className="text-[14px] leading-[20px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Confirm new password
            </label>
            <input
              id="confirm-password"
              type="password"
              placeholder="Enter password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
              }}
              className="bg-[transparent] w-full lg:w-[410px] leading-[20px] placeholder:leading-[20px] py-[8px] focus:outline-none focus:ring-2 focus:ring-[transparent]  placeholder:text-[ rgba(35, 35, 35, 0.4)] text-[ rgba(35, 35, 35, 0.4)]  custom-footer-placeholde"
            />
            {error && (
              <p
                style={{ color: "#D31B1B" }}
                className="text-sm text-left font-[scandiaMedium] text-center"
              >
                {error}
              </p>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-[24px] justify-center text-center">
          <Button>
            <div onClick={handleNewPass}>
              <p className="text-[14px] font-[scandiaMedium] text-[white]">
                Continue
              </p>
            </div>
          </Button>
        </div>
      </form>
    </div>
  );
}

export default CreateNewPassWeb;
