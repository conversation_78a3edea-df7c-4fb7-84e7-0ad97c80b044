"use client";
import React, { useState } from "react";
import Image from "next/image";
// import {
//   Box,
//   Typography,
//   Checkbox,
//   Divider,
//   Avatar,
//   FormControlLabel,
// } from "@mui/material";
import "./communityConfirm.css";

interface OrganizationDetailProps {
  onBack: () => void;
  onNavigate: (page: "list" | "details" | "donate" | "confirm" |"submit") => void;
}

const ConfirmPayment = ({ onBack,onNavigate }: OrganizationDetailProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    privacyPolicy: false,
  });
  const methods = [
    {
      id: "mastercard",
      name: "MasterCard",
      last4: "2251",
      logo: "/Images/CommunityMain/Mastercard.svg",
    },
    {
      id: "visa",
      name: "Visa",
      last4: "7895",
      logo: "/Images/CommunityMain/Visa.svg",
    },
  ];
  return (
    <div className="flex flex-col gap-[24px] ">
      <div className="min-[1000px]:flex hidden gap-[8px] items-center">
        <p
         onClick={() => onNavigate("list")}
          className="font-[scandiaMedium] cursor-pointer text-[#303030] text-[14px] leading-[20px]"
        >
          Organization
        </p>
        <Image
          width={20}
          height={20}
          src="/Images/CommunityMain/arrowRight.svg"
          alt="arrow"
        />
        <p  onClick={() => onNavigate("details")}
         className="font-[scandiaMedium] cursor-pointer text-[#2F3B31] text-[14px] leading-[20px]">
          Syrian Orphan Charity
        </p>
        <Image
          width={20}
          height={20}
          src="/Images/CommunityMain/arrowRight.svg"
          alt="arrow"
        />
        <p
          onClick={onBack}
          className="font-[scandiaMedium] cursor-pointer text-[#2F3B31] text-[14px] leading-[20px]"
        >
          Contribute
        </p>
        <Image
          width={20}
          height={20}
          src="/Images/CommunityMain/arrowRight.svg"
          alt="arrow"
        />
        <p className="font-[scandiaMedium] cursor-pointer text-[#2F3B31] text-[14px] leading-[20px]">
          Confirm payment
        </p>
      </div>
      <div className="bg-[#FFFFFF] rounded-[20px] flex max-[1000px]:p-0 p-[32px] gap-[32px] max-[1184px]:flex-col shadow-[4px_12px_24px_0px_rgba(0,0,0,0.04)] max-[1000px]:shadow-none">
        <div className="flex flex-col gap-[24px] w-full">
          <div
            className="rounded-[20px] p-[24px] sm:p-[32px] flex flex-col gap-[12px]"
            style={{ border: "1px solid #0000000A" }}
          >
            <div className="flex gap-[12px] items-center">
              <Image
                width={48}
                height={48}
                style={{
                  border: " 0.4px solid #ECE7E3",
                  borderRadius: "100%",
                }}
                src="/Images/CommunityMain/o1.png"
                alt="organization"
              />
              <div className="flex flex-col gap-[2px]">
                <p className="text-[16px] leading-[24px] font-[scandiaMedium] text-[#000000]">
                  Syrian Orphan Charity
                </p>

                <p className="text-[14px] leading-[20px] font-[scandia] text-[#303030]">
                  Non-profit
                </p>
              </div>
            </div>
            <img
              style={{ width: "100%" }}
              className="mt-[10px] mb-[10px]"
              src="/Images/CommunityMain/seperator.png"
              alt="seperator"
            />
            <div className="flex gap-[20px] max-[377px]:flex-col">
              <Image
                width={80}
                height={80}
                style={{width: "80px", height: "80px"}} 
                className=" rounded-[12px]"
                src="/Images/CommunityMain/home.png"
                alt="amount"
              />
              <div className="flex flex-col gap-[4px]">
                <p className="text-[16px] leading-[24px] font-[scandiaMedium] text-[#181916]">
                  Syrian Orphan Charity
                </p>

                <p className="text-[14px] leading-[20px] font-[scandia] text-[#303030]">
                  Reconstructing homes for displaced Syrian families, focusing
                  on sustainable and durable housing.
                </p>
              </div>
            </div>
          </div>
          <div
            className="rounded-[20px] p-[24px] sm:p-[32px] flex flex-col gap-[20px]"
            style={{ border: "1px solid #0000000A" }}
          >
            <p className="font-[scandiaMedium]  text-[#000000] text-[16px] leading-[24px]">
              Payment method
            </p>
            {methods.map((method) => (
              <div
                key={method.id}
                className="flex items-center justify-between gap-2 cursor-pointer"
                onClick={() => setSelectedMethod(method.id)}
              >
                <div className="flex items-center gap-3">
                  <span className="w-[16px] h-[16px] rounded-full border border-black flex items-center justify-center">
                    {selectedMethod === method.id && (
                      <span className="w-[8px] h-[8px] bg-[#D31B1B] rounded-full"></span>
                    )}
                  </span>
                  <div className="flex gap-[8px]">
                    <p className="font-[scandia]  text-[#181916] text-[16px] max-[376px]:text-[14px] max-[376px]:leading-[20px] leading-[24px]">
                      {method.name}
                    </p>
                    <p className="font-[scandia]  text-[#181916] text-[16px] leading-[24px] max-[376px]:text-[14px] max-[376px]:leading-[20px]">
                      •••• {method.last4}
                    </p>
                  </div>
                </div>
                <Image
                  width={1200}
                  height={400}
                  src={method.logo}
                  alt={method.name}
                  className="w-[32px] h-[20px]"
                />
              </div>
            ))}

            {!showAddForm && (
              <button
                onClick={() => setShowAddForm(!showAddForm)}
                className="flex items-center gap-[6px] text-[#D31B1B] cursor-pointer leading-[20px] text-[14px] font-[scandiaMedium]"
              >
                <img src="/Images/CommunityMain/add.svg" alt="add" /> Add new
                payment method
              </button>
            )}

            {showAddForm && (
              <div className="bg-[#F8F8F8] rounded-[20px] sm:px-[32px] px-[24px] pt-[32px] pb-[40px] flex flex-col gap-[32px]">
                <p className="font-[scandiaMedium] text-[#000000] text-[16px] leading-[24px]">
                  Add new payment method
                </p>

                <div className="flex flex-col gap-[8px]">
                  <label className="font-[scandiaMedium] text-[14px] leading-[20px] text-[#303030]">
                    Card holder name
                  </label>
                  <input
                    className="w-full border-b border-[#DEDEDE] font-[scandia] text-[#23232366] placeholder:text-[#23232366] pb-[8px] text-[14px] leading-[20px] placeholder:font-[scandia] placeholder:text-[14px] placeholder:leading-[20px]"
                    placeholder="Enter card holder name"
                  />
                </div>

                <div className="flex flex-col gap-[8px]">
                  <label className="font-[scandiaMedium] text-[14px] leading-[20px] text-[#303030]">
                    Card number
                  </label>
                  <input
                    className="w-full border-b border-[#DEDEDE] font-[scandia] text-[#23232366] placeholder:text-[#23232366] pb-[8px] text-[14px] leading-[20px] placeholder:font-[scandia] placeholder:text-[14px] placeholder:leading-[20px]"
                    placeholder="Enter card number"
                  />
                </div>

                <div className="flex gap-[24px] max-[1305px]:flex-col max-[900px]:flex-row max-[376px]:flex-col">
                  <div className="flex flex-col gap-[8px] w-full">
                    <label className="font-[scandiaMedium] text-[14px] leading-[20px] text-[#303030] whitespace-nowrap">
                      Expiry date
                    </label>
                    <input
                      className="w-full border-b border-[#DEDEDE] font-[scandia] text-[#23232366] placeholder:text-[#23232366] pb-[8px] text-[14px] leading-[20px] placeholder:font-[scandia] placeholder:text-[14px] placeholder:leading-[20px]"
                      placeholder="01/25"
                    />
                  </div>

                  <div className="flex flex-col gap-[8px] w-full">
                    <label className="font-[scandiaMedium] text-[14px] leading-[20px] text-[#303030] whitespace-nowrap">
                      CCV
                    </label>
                    <input
                      className="w-full border-b border-[#DEDEDE] font-[scandia] text-[#23232366] placeholder:text-[#23232366] pb-[8px] text-[14px] leading-[20px] placeholder:font-[scandia] placeholder:text-[14px] placeholder:leading-[20px]"
                      placeholder="Enter CCV"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
        <div
          className="w-full sm:p-[32px] p-[24px] h-min rounded-[20px] flex flex-col gap-[40px]"
          style={{ border: "1px solid #0000000A" }}
        >
          <div className="flex flex-col gap-[24px]">
            <p className="text-[22px] leading-[32px] font-[scandiaMedium] text-[#000000]">
              Donation Summary
            </p>
            <div className="flex flex-col gap-[12px]">
              <div className="flex justify-between items-center">
                <p className="text-[16px] leading-[24px] font-[scandia] text-[#303030]">
                  Donation amount
                </p>
                <p className="text-[16px] leading-[24px] font-[scandiaMedium] text-[#181916]">
                  $ 1,000
                </p>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-[16px] leading-[24px] font-[scandia] text-[#303030]">
                  Fee
                </p>
                <p className="text-[16px] leading-[24px] font-[scandiaMedium] text-[#181916]">
                  Free
                </p>
              </div>
              <div
                className="flex justify-between items-center "
                style={{ borderTop: "1px solid #ECE7E3", paddingTop: "24px" }}
              >
                <p className="text-[16px] leading-[24px] font-[scandiaMedium] text-[#303030]">
                  Total
                </p>
                <p className="text-[22px] leading-[32px] font-[scandiaMedium] text-[#181916]">
                  $ 1,000
                </p>
              </div>

              <div></div>
            </div>
          </div>

            <div className="flex flex-col gap-[12px]">
              <div className="flex gap-[6px] cursor-pointer items-center">
                <div
                  onClick={() =>
                    setFormData((prev) => ({
                      ...prev,
                      privacyPolicy: !prev.privacyPolicy,
                    }))
                  }
                >
                  <Image
                    src={
                      formData.privacyPolicy
                        ? "/Images/registration/tickSquare.svg"
                        : "/Images/registration/emptybox.svg"
                    }
                    alt="checkbox"
                    width={20}
                    height={20}
                  />
                </div>
                <p className="font-[scandiaMedium] text-[12px] leading-[16px] text-[#303030]">
                  Make this a monthly donation
                </p>
              </div>
              <div>
                <div className="flex justify-start flex-wrap gap-[4px]">
                  <p className="bg-[#F8F8F8] text-[12px] leading-[16px] font-[scandia] text-[#303030] px-[16px] py-[12px] rounded-[12px] ">
                    This donation will be processed{" "}
                    <span className="font-[scandiaMedium] "> monthly</span>{" "}
                    until{" "}
                    <span className="font-[scandiaMedium] "> June 2025 </span>{" "}
                    or until you update your donation preferences at any time.
                  </p>
                </div>
              </div>
            </div>
          <button className="bg-[#D31B1B] max-[370px]:w-full w-fit rounded-[80px] py-[16px] px-[24px]">
        <p className="font-[scandiaMedium] text-[14px] text-[white] leading-[20px] ">
          Confirm payment
        </p>
      </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmPayment;
