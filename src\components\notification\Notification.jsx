import React from "react";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import IconButton from "@mui/material/IconButton";
import Button from "@mui/material/Button";
import CloseIcon from "@mui/icons-material/Close";
import Image from "next/image";
const notifications = [
  {
    title: "Your donation was successful",
    desc: "Thank you for supporting 'Education for a Brighter Future'.",
    time: "2 min ago",
  },
  {
    title: "Your volunteer application has been submitted",
    desc: "We'll get back to you soon regarding your role as a teacher.",
    time: "Today at 3:42 PM",
  },
  {
    title: "You have a new comment on your post",
    desc: "This is such a powerful initiative!",
    time: "Yesterday",
  },
  {
    title: "New project launched by Syrian Orphan Charity",
    desc: "Check out 'Safe Haven for Orphans' and get involved!",
    time: "Yesterday",
  },
  {
    title: "Complete your profile to start contributing",
    desc: "Add your skills, location, and interests to get matched with opportunities.",
    time: "Mar 9, 2025",
  },
  {
    title: "Your email has been verified",
    desc: "You now have full access to your account.",
    time: "Mar 9, 2025",
  },
];

const Notification = ({ open, handleClose }) => (
  <Modal
    open={open}
    onClose={handleClose}
    aria-labelledby="notification-modal"
    sx={{
      display: "flex",
      alignItems: "flex-start",
      justifyContent: "flex-end",
      mt: { xs: 2, sm: 6 },
      mr: { xs: 2, sm: 4 },
      ml: { xs: 2, sm: 4 },
    }}
  >
    <Box
      sx={{
        width: { xs: "100vw", sm: 480 },
        maxWidth: "100vw",
        bgcolor: "#fff",
        borderRadius: { xs: 4, sm: 4 },
        boxShadow: 24,
        p: 0,
        mt: { xs: 0, sm: 2 },
        mb: { xs: 0, sm: 2 },
        outline: "none",
        maxHeight: { xs: "100vh", sm: "100vh" },
        overflowY: "auto",
      }}
    >
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          padding: "32px",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            fontFamily: "ScandiaMedium",
            fontWeight: 500,
            fontSize: "22px",
            lineHeight: "32px",
            letterSpacing: "0%",
            verticalAlign: "middle",
            color: "#000000",
          }}
        >
          Notifications
        </Typography>
        <Image
          onClick={handleClose}
          height={24}
          width={24}
          src="/Images/notification/close.svg"
          alt="close"
        />
      </Box>
      <Box
        sx={{
          px: { xs: 3, sm: 4 },
          pt: 2.5,
          pb: { xs: 2, sm: 7 },
          display: "flex",
          flexDirection: "column",
          gap: "16px",
        }}
      >
        {notifications.map((n, i) => (
          <Box
            key={i}
            sx={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            <Box sx={{ display: "flex", flexDirection: "column", gap: "4px" }}>
              <Typography
                sx={{
                  fontFamily: "ScandiaMedium",
                  fontWeight: 500,
                  fontSize: "12px",
                  lineHeight: "16px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#181916",
                }}
              >
                {n.title}
              </Typography>
              <Typography
                sx={{
                  fontFamily: "Scandia",
                  fontWeight: 400,
                  fontSize: "12px",
                  lineHeight: "16px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#303030",
                }}
              >
                {n.desc}
              </Typography>
            </Box>
            <Typography
              sx={{
                fontFamily: "ScandiaMedium",
                fontWeight: 500,
                fontSize: "12px",
                lineHeight: "16px",
                letterSpacing: "0%",
                textAlign: "right",
                verticalAlign: "middle",
                color: "#181916",
                whiteSpace: "nowrap",
              }}
            >
              {n.time}
            </Typography>
          </Box>
        ))}
      </Box>
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          alignItems: "center",
          gap: 2,
          px: 3,
          pb: 4,
         
        }}
      >
        <Button
          sx={{
            color: "#181916",
            textTransform: "none",
            fontFamily: "ScandiaMedium",
            fontWeight: 500,
            fontSize: "14px",
            lineHeight: "20px",
            letterSpacing: "0%",
            textAlign: "center",
            verticalAlign: "middle",
          }}
        >
          See all
        </Button>
        <Button
          sx={{
            padding: "12px 24px",
            borderRadius: "60px",
            background: "transparent",
            border: "1px solid #303030",
            fontFamily: "ScandiaMedium",
            fontWeight: 500,
            fontSize: { xs: "12px", sm: "14px" },
            lineHeight: "20px",
            letterSpacing: "0%",
            textAlign: "center",
            verticalAlign: "middle",
            color: "#303030",
            whiteSpace: "nowrap",
          }}
        >
          Mark all as read
        </Button>
      </Box>
    </Box>
  </Modal>
);

export default Notification;
