/* eslint-disable */
"use client";
import { useRef, useState, useEffect } from "react";
import Image from "next/image";
import type { FormData } from "./StepsRegistration";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../services/redux/store";
import { uploadImage } from "@/services/redux/middleware/register";
import { toast } from "react-toastify";

type Props = {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
};

export default function Step4({ formData, setFormData }: Props) {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const dispatch = useDispatch<AppDispatch>();
  const [tags, setTags] = useState<string[]>(formData.tags || []);
  const [inputValue, setInputValue] = useState("");

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" && inputValue.trim() !== "") {
      e.preventDefault(); // Prevent form submission if inside a form
      if (!tags.includes(inputValue.trim())) {
        setTags([...tags, inputValue.trim()]);
      }
      setInputValue(""); // Clear input
    }
  };

  const handleRemoveTag = (index: number) => {
    setTags(tags.filter((_, i) => i !== index));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/png"];
      if (!validTypes.includes(file.type)) {
        toast.error("Only JPG/PNG files are allowed");
        return;
      }

      // Validate file size (2MB max)
      if (file.size > 2 * 1024 * 1024) {
        toast.error("File size exceeds 2MB limit");
        return;
      }

      // Create FormData and append file
      const formData = new FormData();
      formData.append("photo", file);

      // Upload image using the API
      dispatch(uploadImage(formData)).then((res) => {
        if (res?.payload?.data?.url) {
          console.log("Uploaded profile image URL:", res.payload.data.url);
          setFormData((prev) => ({
            ...prev,
            profileImage: res.payload.data.url,
          }));
          toast.success("Profile image uploaded successfully");
        } else {
          toast.error("Failed to upload profile image");
        }
      });
    }
  };

  useEffect(() => {
    setFormData((prev) => ({
      ...prev,
      tags: tags,
    }));
  }, [tags, setFormData]);
  const handleClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="flex flex-col gap-[64px]">
      <div className="flex flex-col gap-[32px]">
        <div className="flex md:gap-[64px] items-center gap-[40px] max-[768px]:flex-col">
          {/* Image Upload Circle */}
          <div
            className="bg-[#CFCFCF] rounded-full h-[126px] max-w-[126px] w-full flex items-center justify-center overflow-hidden cursor-pointer"
            onClick={handleClick}
          >
            {formData.profileImage ? (
              <Image
                src={formData.profileImage}
                alt="Profile"
                width={126}
                height={126}
                className="object-cover w-full h-full"
              />
            ) : (
              <Image
                src="/Images/registration/imageuploader.svg"
                alt="imageUploader"
                width={48}
                height={48}
              />
            )}
            <input
              type="file"
              accept="image/jpeg,image/png"
              className="hidden"
              ref={fileInputRef}
              onChange={handleImageUpload}
            />
          </div>

          {/* Short Bio */}
          <div className="flex flex-col gap-[8px] w-full">
            <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030]">
              Short bio
            </p>
            <textarea
              value={formData.biodata}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  biodata: e.target.value,
                }))
              }
              placeholder="Enter organization short bio"
              className="w-full bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0 text-[#181916] placeholder:font-[scandia] placeholder:text-[#CFCFCF] resize-none overflow-hidden"
              rows={4}
              style={{ maxHeight: `${20 * 4}px` }}
              onInput={(e) => {
                const target = e.target as HTMLTextAreaElement;
                target.style.height = "auto";
                target.style.height =
                  Math.min(target.scrollHeight, 20 * 4) + "px";
              }}
            />
          </div>
        </div>

        {/* Tags Section */}
        <div className="flex flex-col gap-[16px]">
          <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030] whitespace-nowrap">
            Organization tag
          </p>
          <div className="flex gap-[12px] flex-wrap">
            {tags.map((tag, idx) => (
              <div
                key={idx}
                style={{ border: "1px solid #303030" }}
                className="flex gap-[6px] px-[24px] py-[12px] rounded-[60px] items-center justify-center"
              >
                <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#303030] whitespace-nowrap">
                  {tag}
                </p>
                <Image
                  src="/Images/registration/cancel.svg"
                  alt="Remove"
                  width={12}
                  height={12}
                  onClick={() => handleRemoveTag(idx)}
                  className="cursor-pointer"
                />
              </div>
            ))}
            <div className="flex gap-[6px] rounded-[60px] items-center justify-center">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="e.g. education, profit"
                className="font-[scandia] font-medium text-[14px] leading-[20px] text-[#232323] outline-none bg-transparent"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Terms and Conditions */}
      <div className="flex flex-col gap-[8px]">
        <div className="flex gap-[8px] cursor-pointer items-center">
          <div
            onClick={() => {
              setFormData((prev) => ({
                ...prev,
                acceptedTerms: !prev.acceptedTerms,
              }));
            }}
          >
            <Image
              src={
                formData.acceptedTerms
                  ? "/Images/registration/tickSquare.svg"
                  : "/Images/registration/emptybox.svg"
              }
              alt="checkbox"
              width={20}
              height={20}
            />
          </div>
          <p className="font-[scandia] text-[14px] leading-[20px] text-[#303030]">
            I have read and accept{" "}
            <span className="text-[#D31B1B]">Iltezam's Terms & Conditions</span>
          </p>
        </div>
        <div className="flex gap-[8px] cursor-pointer items-center">
          <div
            onClick={() => {
              setFormData((prev) => ({
                ...prev,
                privacyPolicy: !prev.privacyPolicy,
              }));
            }}
          >
            <Image
              src={
                formData.privacyPolicy
                  ? "/Images/registration/tickSquare.svg"
                  : "/Images/registration/emptybox.svg"
              }
              alt="checkbox"
              width={20}
              height={20}
            />
          </div>
          <p className="font-[scandia] text-[14px] leading-[20px] text-[#303030]">
            I have read and accept{" "}
            <span className="text-[#D31B1B]">Iltezam's Privacy Policy</span>
          </p>
        </div>
      </div>
    </div>
  );
}
