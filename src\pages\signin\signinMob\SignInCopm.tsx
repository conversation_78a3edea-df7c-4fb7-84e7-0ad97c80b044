import React, { useState } from "react";
import SignInMob from "./SignInMob";

function SignInCopm() {
  const [active, setActive] = useState<"signIn" | "register">("signIn");
  return (
    <div
      style={{
        backgroundColor: "rgba(47, 59, 49, 1)",
        height: active === "register" ? "auto" : "100vh",
      }}
      className="flex pt-[41px] p-[24px] gap-[40px] mt-[100px] justify-start flex-col items-start"
    >
      <div className="flex flex-col gap-[12px]">
        <p className="text-[40px] leading-[40px] text-[white] font-[scandiaMedium]">
          Join to give the impact
        </p>
        <p className="font-[scandia] leading-[20px] text-[white] text-sm">
          Join a community dedicated to rebuilding, empowering, and inspiring a
          brighter future for Syria.
        </p>
      </div>
      <SignInMob active={active} setActive={setActive} />
    </div>
  );
}

export default SignInCopm;
