import React from "react";
import "./AboutUs.css";
import Navbar from "../../components/navbar/Navbar";
import Image from "next/image";
function AboutUs() {
  return (
    <div>
      <Navbar />
      <div className="aboutus_main__container">
        <div className="aboutus__container__1">
          <div className="aboutus__hading__container">
            <p className="aboutus__hading__1">Who We are</p>
            <p className="aboutus__para__1">
              Global platform dedicated to connecting Syrians around the world
              to collaborate on rebuilding Syria. By fostering community-driven
              initiatives, Iltezam empowers individuals and organizations to
              contribute to meaningful projects that restore hope, strengthen
              communities, and create lasting change for Syria&apos;s future.
            </p>
          </div>
        </div>
        <div className="our__vision__main__container">
          <div className="our__vision__container">
            <div className="our__vision__hading__container">
              <p className="our__vision__hading">Our vision</p>
              <p className="our__vision__hading__para">
                Empowering ethical futures
              </p>
              <p className="our__vision__para">
                We envision a world where integrity leads progress — where
                professionals, communities, and changemakers are empowered to
                act ethically, think critically, and build responsibly. At
                Iltezam, we believe that ethical conduct isn&apos;t just a guideline
                — it&apos;s the foundation for lasting impact. Our platform is a home
                for learning, collaboration, and transformation rooted in values
                that matter.
              </p>
            </div>
            <Image
              className="our__vision__image"
              src="/Images/aboutus/cardimage1.svg"
              alt="cardimage"
              width={480}
              height={360}
            />
          </div>
          <div className="our__vision__container">
            <Image
              className="our__vision__image"
              src="/Images/aboutus/cardImage2.svg"
              alt="cardimage"
              width={480}
              height={360}
            />
            <div className="our__vision__hading__container">
              <p className="our__vision__hading">Our mission</p>
              <p className="our__vision__hading__para">
                Rebuilding Syria together
              </p>
              <p className="our__vision__para">
                Join a platform dedicated to connecting Syrians worldwide,
                creating impactful projects that contribute to Syria&apos;s
                rebuilding and future prosperity.
              </p>
            </div>
          </div>
        </div>
        <div className="our__pillars__manin__container">
          <p className="our__pillars__hading">Our pillars of purpose</p>
          <div className="our__pillars__container">
            <div className="our__pillars__card">
              <Image
                src="/Images/aboutus/ethics.svg"
                alt="ethicsimage"
                width={64}
                height={64}
              />
              <div className="our__pillars__card__heading_container">
                <p className="our__pillars__card__heading">Ethics</p>
                <p className="our__pillars__card__para">
                  We believe impact must be built on a foundation of trust.
                  Through certified courses in ethics and community leadership,
                  we promote values of transparency, accountability, and
                  respectful collaboration in every initiative.
                </p>
              </div>
            </div>
            <div className="our__pillars__card">
              <Image
                src="/Images/aboutus/knowledge.svg"
                alt="knowledgeimage"
                width={64}
                height={64}
              />
              <div className="our__pillars__card__heading_container">
                <p className="our__pillars__card__heading">Knowledge</p>
                <p className="our__pillars__card__para">
                  Access to knowledge is access to opportunity. Iltezam provides
                  educational tools, mentorship, and guidance to ensure every
                  individual and organization has what they need to learn, grow,
                  and lead with confidence.{" "}
                </p>
              </div>
            </div>
            <div className="our__pillars__card">
              <Image
                src="/Images/aboutus/professional.svg"
                alt="professionalimage"
                width={64}
                height={64}
              />
              <div className="our__pillars__card__heading_container">
                <p className="our__pillars__card__heading">Professionalism</p>
                <p className="our__pillars__card__para">
                  From developing hard skills to building work-ready experience,
                  we&apos;re committed to empowering Syrians with the tools to lead,
                  create, and excel. Professionalism on Iltezam means doing
                  good—well.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="our__team__main__container__about">
          <div className="our__team__container__about">
            <div
              style={{
                backgroundImage: 'url("/Images/aboutus/fromPartner.png")',
                backgroundSize: "100% 100%",
                width: "440px",
                height: "560px",
              }}
              className="our__team__image__container__about"
            >
              <div className="our__team__image__container__about__text_container__top">
              <div className="our__team__image__container__about__text_container__top__line">

              </div>
            
              <div className="our__team__image__container__about__text_container">
                <p className="our__team__image__container__about__text">Mohammed Husary</p>
                <p className="our__team__image__container__about__text__12">
                  Founder of Iltezam
                </p>
              </div>
                </div>
            </div>
            <div className="our__team__hading__container__about">
              <p className="our__team__hading__about__22">Our mission</p>
              <div>
                <p className="our__team__para__about___22">
                  Iltezam was born from a deep belief that doing the right thing
                  shouldn&apos;t feel like the hard choice — it should feel like the
                  natural one.
                </p>
                <p className="our__team__para__about___22">
                  As a founder, I&apos;ve walked alongside inspiring individuals and
                  organizations who wanted to drive change but struggled with
                  access, clarity, or structure.
                </p>
                <p className="our__team__para__about___22">
                  That&apos;s why Iltezam was created — to support professionals and
                  communities in growing their knowledge, sharpening their
                  ethics, and connecting with like-minded peers who care.{" "}
                </p>
                <p className="our__team__para__about___22">
                  This isn&apos;t just a platform; it&apos;s a commitment.
                </p>
                <p className="our__team__para__about___22">
                  To integrity. To people. To progress.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="joinus__section">
          <div className="joinus__illustration">
            {/* Replace with your SVG or image path */}
            <Image height={172} width={172} src="/Images/aboutus/joinUs.svg" alt="Join Us Illustration" />
          </div>
          <div className="joinus__content">
            <h2 className="joinus__heading">Join Us. Shape the Future.</h2>
          </div>
          <div className="joinus__buttons">
            <button className="joinus__button joinus__button--filled">As an Organization</button>
            <button className="joinus__button joinus__button--outline">As an Individual</button>
          </div>
        </div>
    </div>
  );
}

export default AboutUs;
