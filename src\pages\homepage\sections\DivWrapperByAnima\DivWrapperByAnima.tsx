import React, { useState } from "react";
import { Badge } from "../../../../components/ui/badge";
import { But<PERSON> } from "../../../../components/ui/button";
import { Card, CardContent } from "../../../../components/ui/card";
import { Progress } from "../../../../components/ui/progress";
import Image from "next/image";

// Project data for mapping
const projectsData = [
  {
    id: 1,
    image: "/Images/homepage/Doctors.png", // Image URL would be here
    organization: "Rebuild Syria Network",
    title: "Rebuilding Children's Hospital in Aleppo",
    description:
      "This project aims to restore and modernize a critical children's hospital...",
    hasFundraising: true,
    fundraisingGoal: "25,000 USD",
    roles: [],
  },
  {
    id: 2,
    image: "/Images/homepage/Doctors.png", // Image URL would be here
    organization: "Rebuild Syria Network",
    title: "Homes for Hope",
    description:
      "Reconstructing homes for displaced Syrian families, focusing on sustainable and durable housing.",
    hasFundraising: false,
    roles: ["Architect", "Civil Engineer", "+5 More"],
  },
  {
    id: 3,
    image: "/Images/homepage/Doctors.png", // Image URL would be here
    organization: "Syrian Orphan Charity",
    title: "Schools of Tomorrow",
    description:
      "Establishing schools with essential learning materials for war-affected children.",
    hasFundraising: true,
    fundraisingGoal: "400,000 USD",
    roles: [],
  },
  {
    id: 4,
    image: "/Images/homepage/Doctors.png", // Image URL would be here
    organization: "Syria Health Initiative",
    title: "Mobile clinics for rural areas",
    description:
      "Providing urgent medical aid and health screenings in underserved communities.",
    hasFundraising: false,
    roles: ["Doctor", "Nurse", "+5 More"],
  },
];

// Filter categories
 const DivWrapperByAnima = () => {
  const [filterCategories, setCategories] = useState([
    { id: "recent", label: "Recent", active: true },
    { id: "volunteers", label: "Volunteers", active: false },
    { id: "donations", label: "Donations", active: false },
  ]);
  const handleCategoryClick = (clickedId: string) => {
    setCategories(
      filterCategories.map((category) => ({
        ...category,
        active: category.id === clickedId,
      }))
    );
  };

  return (
    <section className="gap-6 px-6 py-16 flex flex-col items-center justify-center w-full bg-[#FCFCFC] lg:px-[100px]">
      <header className="w-full flex flex-col items-start gap-2">
        <span className="flex justify-between items-center w-full">
          <h2 className="font-[ScandiaMedium] text-[22px] md:text-[28px] text-[#181916]">
            Latest Projects
          </h2>
          <p className="font-[ScandiaMedium] text-[14px] visible md:hidden">
            See All
          </p>
        </span>
        <p className="font-[Scandia] hidden md:block text-[16px] text-[#303030]">
          Explore new initiatives driving real change.
        </p>
      </header>

      <div className="w-full flex flex-col gap-6">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center gap-2 overflow-x-auto pb-2">
            {filterCategories.map((category) => (
              <Button
                key={category.id}
                variant={category.active ? "default" : "outline"}
                className={`rounded-[60px] px-6 py-2 md:py-4 cursor-pointer h-auto ${
                  category.active
                    ? "bg-[#2F3B31] text-[#FFF]"
                    : "border-[#2f2f2f] text-grey"
                }`}
                onClick={() => handleCategoryClick(category.id)}
              >
                <span className={`font-[ScandiaMedium] text-[12px] md:text-[16px]${category?.active ? "text-white":"text-[#181916]"}`}>
                  {category.label}
                </span>
              </Button>
            ))}
          </div>

          <div className="md:flex gap-5 items-center hidden">
            <Button
              variant="ghost"
              size="icon"
              className="w-[38px] h-[38px] rounded-full opacity-50"
            >
              <Image src="/Images/homepage/arrowLeft.svg" alt="previous" width={34} height={32} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="w-[38px] h-[38px] rounded-full"
            >
              <Image src="/Images/homepage/arrowRIght.svg" alt="next" width={34} height={32} />
            </Button>
          </div>
        </div>

        <div className="w-full overflow-scroll xl:overflow-hidden no-scrollbar">
          <div className="flex xl:grid xl:grid-cols-4 gap-4 px-1 py-1">
            {projectsData.map((project) => (
              <Card
                key={project.id}
                className="min-w-[300px] max-w-[310px] 2xl:max-w-full w-full flex-shrink-0 xl:min-w-0 lg:w-full flex flex-col border border-solid border-[#0000000a] shadow-card-shadow rounded-[20px]"
              >
                <CardContent className="flex flex-col gap-5 p-5 h-full justify-between">
                  <div className="flex flex-col gap-4">
                    <Image
                      className="w-full h-[186px] object-cover rounded-md"
                      src={project.image}
                      alt={project.title}
                      width={300}
                      height={186}
                    />

                    <div className="flex flex-col gap-2">
                      <div className="flex items-center gap-2">
                        <Image
                          className="w-6 h-6"
                          src="/Images/homepage/rebuildGroup.png"
                          alt="Organization logo"
                          width={24}
                          height={24}
                        />
                        <div className="flex flex-col justify-center flex-1">
                          <span className="font-[ScandiaMedium]text-black text-[12px]">
                            {project.organization}
                          </span>
                        </div>
                      </div>

                      <h3 className="font-[ScandiaMedium] text-[#181916] text-[22px]">
                        {project.title}
                      </h3>

                      <p className="font-[Scandia] text-[14px] text-[#303030]">
                        {project.description}
                      </p>

                      {project.roles.length > 0 && (
                        <div className="flex flex-wrap items-start gap-1 mt-4">
                          {project.roles.map((role, index) => (
                            <Badge
                              key={index}
                              variant="secondary"
                              className="bg-[#F8F8F8] rounded-[40px] px-3 py-2 font-[Scandia] text-[#181916] text-[12px] font-[400]"
                            >
                              {role}
                            </Badge>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {project.hasFundraising && (
                    <div className="flex flex-col gap-1 p-4 bg-base bg-[#ECE7E3] rounded-[12px]">
                      <div className="flex items-center justify-between w-full">
                        <span className="[font-family:'Scandia-Regular',Helvetica] font-normal text-[#181916] text-xs tracking-[0] leading-6">
                          Fundraising goal
                        </span>
                        <span className="font-[ScandiaMedium] text-[#181916] text-[12px] tracking-[0] leading-6">
                          {project.fundraisingGoal}
                        </span>
                      </div>
                      <Progress value={33} className="h-[11px]" />
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      <div className="flex items-center justify-center pt-5">
        <Button
          variant="ghost"
          className="font-[ScandiaMedium] text-[14px] text-[#303030] hidden md:block cursor-pointer"
        >
          See all
        </Button>
      </div>
    </section>
  );
};
export default DivWrapperByAnima;

