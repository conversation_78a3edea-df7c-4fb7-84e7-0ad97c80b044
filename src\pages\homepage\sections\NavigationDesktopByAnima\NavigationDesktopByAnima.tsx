import { GlobeIcon } from "lucide-react";
import React from "react";
import Image from "next/image";
import { Button } from "../../../../components/ui/button";
import {
  NavigationMenu,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
} from "../../../../components/ui/navigation-menu";

const NavigationDesktopByAnima = () => {
  // Navigation menu items
  const navItems = [
    { label: "Projects", href: "#" },
    { label: "Community", href: "#" },
    { label: "Learn", href: "#" },
    { label: "About us", href: "#" },
  ];

  return (
    <header className="sticky top-0 w-full h-20 bg-app-primary z-50">
      <nav className="flex h-20 items-center justify-between px-[100px] bg-app-primary">
        {/* Logo */}
        <div className="flex h-16 items-center gap-2">
          <div className="flex items-center gap-2">
            <div className="relative w-[36px] h-[47px] rounded-[5.75px] overflow-hidden">
              <div className="relative w-[60px] h-[60px] -top-1.5 left-px rotate-180">
                <div className="h-[60px]">
                  <div className="relative w-[47px] h-[47px] top-1.5 left-1.5 bg-[#ffffff] rounded-[1077.76px] overflow-hidden rotate-[-162.20deg]">
                    <div className="relative w-[38px] h-[42px] top-1 -left-2.5 rotate-[-56.23deg]">
                      <div className="relative w-12 h-[52px] top-[-7px] left-[-11px]">
                        <div className="absolute w-12 h-[52px] top-0 left-0">
                          <Image
                            className="absolute w-[22px] h-[22px] top-1 left-[22px] rotate-[56.23deg]"
                            alt="Vector"
                            src="/vector-105.svg"
                            width={22}
                            height={22}
                          />
                          <Image
                            className="w-[5px] h-[7px] top-[39px] left-[26px] absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-1.svg"
                            width={5}
                            height={7}
                          />
                          <Image
                            className="w-2 h-[5px] top-[27px] left-6 absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-13.svg"
                            width={8}
                            height={5}
                          />
                          <Image
                            className="w-[7px] h-[5px] top-[25px] left-[30px] absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-5.svg"
                            width={7}
                            height={5}
                          />
                          <Image
                            className="w-1.5 h-[7px] top-[34px] left-[23px] absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-2.svg"
                            width={6}
                            height={7}
                          />
                          <Image
                            className="absolute w-5 h-5 top-[13px] left-[9px] rotate-[56.23deg]"
                            alt="Vector"
                            src="/vector-107.svg"
                            width={20}
                            height={20}
                          />
                          <Image
                            className="absolute w-[21px] h-[21px] top-[27px] left-1 rotate-[56.23deg]"
                            alt="Vector"
                            src="/vector-108-1.svg"
                            width={21}
                            height={21}
                          />
                        </div>
                        <div className="absolute w-3 h-2.5 top-[38px] left-8">
                          <Image
                            className="w-[5px] h-[5px] top-px left-1.5 absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect.svg"
                            width={5}
                            height={5}
                          />
                          <Image
                            className="w-1.5 h-[5px] top-1 left-px absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-3.svg"
                            width={6}
                            height={5}
                          />
                        </div>
                        <div className="absolute w-[11px] h-[13px] top-[26px] left-[37px]">
                          <Image
                            className="w-1.5 h-[7px] top-[5px] left-1 absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-11.svg"
                            width={6}
                            height={7}
                          />
                          <Image
                            className="w-[5px] h-[7px] top-0 left-0.5 absolute rotate-[56.23deg]"
                            alt="Intersect"
                            src="/intersect-4.svg"
                            width={5}
                            height={7}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-start justify-center gap-1.5">
              <Image className="relative" alt="Frame" src="/frame-42.svg" width={100} height={20} />
              <Image
                className="relative w-[54.74px] h-[8.91px]"
                alt="Image"
                src="/------------.svg"
                width={55}
                height={9}
              />
            </div>
          </div>
        </div>

        {/* Navigation Menu */}
        <NavigationMenu className="h-full">
          <NavigationMenuList className="flex items-center gap-8 h-full">
            {navItems.map((item, index) => (
              <NavigationMenuItem key={index} className="h-full">
                <NavigationMenuLink
                  className="flex flex-col items-center justify-center h-full px-0.5 py-0 relative group"
                  href={item.href}
                >
                  <span className="font-normal text-white text-base text-center tracking-[0] leading-6 whitespace-nowrap">
                    {item.label}
                  </span>
                  <div className="h-px w-full bg-transparent group-hover:bg-white transition-colors duration-200" />
                </NavigationMenuLink>
              </NavigationMenuItem>
            ))}
          </NavigationMenuList>
        </NavigationMenu>

        {/* Sign In and Language Selector */}
        <div className="flex h-20 items-center gap-6">
          <Button
            variant="ghost"
            className="h-20 font-bold text-white text-lg tracking-[0] leading-[27px] hover:bg-transparent"
          >
            Sign In
          </Button>

          <Button
            variant="outline"
            className="flex items-center justify-center gap-1.5 px-6 py-3 rounded-[60px] border border-solid border-white text-white hover:bg-transparent hover:text-white"
          >
            <span className="mt-[-1.00px] font-arabic-h6-headline-6-reg text-white text-center whitespace-nowrap [direction:rtl]">
              العربية
            </span>
            <GlobeIcon className="w-[18px] h-[18px]" />
          </Button>
        </div>
      </nav>
    </header>
  );
};
export default NavigationDesktopByAnima;


