// import { Listbox } from "@headlessui/react";
// import { ChevronDown } from "lucide-react";
import React, { useState } from "react";
import Image from "next/image";
import AnyDonation from "./AnyDonation";
// import SpecificDonation from "../SpecificDonation";
import { FormControl, MenuItem, Select } from "@mui/material";

const options = [
  { name: "Any projects", value: "individual" },
  { name: "Homes for Hope", value: "organization" },
];
 
interface OrganizationDetailProps {
 onNavigate: (page: "list" | "details" | "donate" | "confirm" |"submit") => void;
  onBack: () => void;

}
function Donation({onBack,onNavigate}:OrganizationDetailProps) {
  // const [selected, setSelected] = useState(options[0]);
  const [activeType, setActiveType] = useState("Donation");
const [selectedValue, setSelectedValue] = useState(""); // Local state instead of formData
  const [open, setOpen] = useState(false);
  return (
    <div className="flex flex-col gap-[24px]">
      <div className="flex max-[1000px]:hidden gap-[8px] items-center">
        <p
          onClick={() => onNavigate("list")}
          className="font-[scandiaMedium] cursor-pointer text-[#303030] text-[14px] leading-[20px]"
        >
          Organization
        </p>
        <img src="/Images/CommunityMain/arrowRight.svg" alt="arrow" />
        <p onClick={onBack}
        className="font-[scandiaMedium] cursor-pointer text-[#2F3B31]  text-[14px] leading-[20px]">
          Syrian Orphan Charity
        </p>
         <img src="/Images/CommunityMain/arrowRight.svg" alt="arrow" />
        <p className="font-[scandiaMedium] cursor-pointer text-[#2F3B31] text-[14px] leading-[20px]">
          Contribute
        </p>
      </div>
      <div className="bg-[white] max-[1200px]:flex-col flex max-[1390px]:gap-[20px] gap-[32px] shadow-[4px 12px 24px 0px #0000000A] rounded-[20px] max-[1000px]:p-0 p-[32px] shadow-[4px_12px_24px_0px_rgba(0,0,0,0.04)] max-[1000px]:shadow-none " 
>
        <div className="border-[#0000000A] flex flex-col gap-[16px] max-[1200px]:w-full w-[40%] h-fit border rounded-[20px] max-[768px]:p-[24px] p-[32px] ">
          <div className="flex items-center gap-[12px]">
            <Image 
              src="/Images/community/Syrian Orphan Charity.svg" 
              alt="Syrian Orphan Charity logo"
              width={48}
              height={48}
            />
            <div className="flex flex-col gap-[2px]">
              <p className="text-[16px] pb-[2px] leading-[24px] font-[scandiaMedium] max-[375px]:text-[12px] max-[375px]:leading-[18px]">
                Syrian Orphan Charity
              </p>
              <p className="text-[14px] leading-[20px] font-[scandia] text-[#303030] ">
                Non-profit
              </p>
            </div>
          </div>

          <div
            style={{
              borderTop: "none",
              borderLeft: "none",
              borderRight: "none",
            }}
            className="border border-b-[#ECE7E3] mb-[10px] pt-[10px]"
          />

          <div>
            <h2 className="font-semibold leading-[24px] text-[16px] mb-3">
              About
            </h2>
            <p className="text-gray-700 text-[14px] leading-[20px]">
             Syrian Orphan Charity is dedicated to supporting and empowering orphaned children affected by the Syrian crisis. Through education, healthcare, and essential aid, we provide a nurturing environment to help them rebuild their futures with dignity and hope.
            </p>
          </div>

          <div className="flex justify-start flex-wrap gap-[4px]">
            {[
              "Non-profit",
              "Volunteering",
              "Child protection",
              "Food & nutrition",
              "+3",
            ].map((skill) => (
              <span
                key={skill}
                className="bg-[#F8F8F8] text-[12px] leading-[16px] font-[scandia] text-[#303030] px-[12px] py-[8px] rounded-[40px] "
              >
                {skill}
              </span>
            ))}
          </div>
        </div>

        {/* Right Panel */}
        <div className="border-[#0000000A] max-[1200px]:w-full w-[60%] border rounded-[20px] max-[768px]:p-[24px] p-[32px]">
          <div className="flex flex-col gap-[20px]">
            <p className="sm:text-[22px] text-[16px] leading-[24px] pb-[2px] sm:leading-[32px] font-[scandiaMedium]">
              What kind of contribution you would like to give?
            </p>

            <div className="flex gap-[8px]">
              <button
                onClick={() => setActiveType("Donation")}
                style={{
                  border:
                    activeType === "Donation"
                      ? "none"
                      : "1px solid rgba(48, 48, 48, 1)",
                  background:
                    activeType === "Donation"
                      ? "rgba(47, 59, 49, 1)"
                      : "transparent",
                  color: activeType === "Donation" ? "white" : "black",
                }}
                className="rounded-[80px] leading-[20px] cursor-pointer py-[16px] px-[24px] md:text-[14px] text-[12px] font-[scandiaMedium] transition-all duration-200"
              >
                Donation
              </button>

              <button
                onClick={() => setActiveType("Volunteering")}
                style={{
                  border:
                    activeType === "Volunteering"
                      ? "none"
                      : "1px solid rgba(48, 48, 48, 1)",
                  background:
                    activeType === "Volunteering"
                      ? "rgba(47, 59, 49, 1)"
                      : "transparent",
                  color: activeType === "Volunteering" ? "white" : "black",
                }}
                className="rounded-[80px] cursor-pointer leading-[20px] py-[16px] px-[24px] md:text-[14px] text-[12px] font-[scandiaMedium] transition-all duration-200"
              >
                Volunteering
              </button>
            </div>

            {/* <div className="flex mt-[24px] flex-col">
              <label
                htmlFor="name"
                className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]"
              >
                Select project
              </label>
              <div className="relative w-full">
                <Listbox value={selected} onChange={setSelected}>
                  <div className="relative">
                    <Listbox.Button className="w-full text-[#181916] border-b border-[#d9d9d9] py-2 pr-8 text-left bg-transparent focus:outline-none font-[scandiaMedium]">   {selected.name}
                      <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                        <ChevronDown size={16} className="text-[#181916]" />
                      </span>
                    </Listbox.Button>
                    <Listbox.Options className="absolute mt-1 w-full bg-white shadow-md rounded-[10px] z-10">
                      {options.map((option, idx) => (
                        <Listbox.Option
                          key={idx}
                          className={({ active }) =>
                            `cursor-pointer text-[#232323] px-4 py-2 ${
                              active ? "bg-gray-100" : "bg-white"
                            } rounded-[10px]`
                          }
                          value={option}
                        >
                          {option.name}
                        </Listbox.Option>
                      ))}
                    </Listbox.Options>
                  </div>
                </Listbox>
              </div>
            </div> */}
            <div className="flex mt-[24px] flex-col">
              <label
                htmlFor="name"
                className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]"
              >
                Select project
              </label>
             <FormControl
  fullWidth
  variant="standard"
  sx={{
    "& .MuiInputBase-root": {
      fontFamily: selectedValue ? "ScandiaMedium" : "Scandia",
      color: selectedValue ? "#232323" : "#CFCFCF",
      cursor: "pointer",
      fontSize: "14px",
      lineHeight: "20px",
    },
    "& .MuiInput-underline:before": {
      borderBottom: "1.5px solid #DEDEDE",
    },
    "& .MuiInput-underline:hover:before": {
      borderBottom: "1.5px solid #DEDEDE",
    },
    "& .MuiInput-underline:after": {
      borderBottom: "1.5px solid transparent",
    },
  }}
>
  <Select
    value={selectedValue}
    onChange={(e) => setSelectedValue(e.target.value)}
    onOpen={() => setOpen(true)}
    onClose={() => setOpen(false)}
    displayEmpty
    renderValue={(selected) => selected || "Select project"} // This shows the placeholder
    IconComponent={() => null}
    inputProps={{ "aria-label": "Select project" }}
    MenuProps={{
      PaperProps: {
        elevation: 0,
        sx: {
          fontFamily: "ScandiaMedium",
          boxShadow: "none",
        },
      },
    }}
    sx={{
      "& .MuiSelect-select": {
        paddingRight: "30px",
        fontFamily: selectedValue ? "ScandiaMedium" : "Scandia",
        fontSize: "14px",
        lineHeight: "20px",
        color: selectedValue ? "#232323" : "#CFCFCF",
      },
      position: "relative",
    }}
  >
    {options.map((opt) => (
      <MenuItem
        key={opt.value}
        value={opt.name}
        sx={{
          fontFamily: selectedValue === opt.value ? "ScandiaMedium" : "Scandia",
          fontSize: "14px",
          lineHeight: "20px",
          color: "#181916",
          padding: "8px",
          "&.Mui-selected": {
            backgroundColor: "transparent !important",
          },
          "&.Mui-selected:hover": {
            backgroundColor: "transparent !important",
          },
          "&:focus": {
            backgroundColor: "transparent !important",
          },
          "& .MuiTouchRipple-root": {
            display: "none !important",
          },
        }}
      >
        {opt.name}
      </MenuItem>
    ))}
  </Select>

  {/* Custom arrow icon */}
  <div
    style={{
      position: "absolute",
      right: "8px",
      top: "50%",
      transform: `translateY(-50%) rotate(${open ? 180 : 0}deg)`,
      transition: "transform 0.3s ease",
      pointerEvents: "none",
    }}
  >
    <Image
      src="/Images/registration/arrow-down.svg"
      alt="arrow"
      width={20}
      height={20}
    />
  </div>
</FormControl>
                              </div>

            <div className="mt-6">
              <AnyDonation onNavigate={onNavigate} />

              {/* { {selected.name === "Any projects" ? (
              ) : (
                <SpecificDonation />
              )} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Donation;
