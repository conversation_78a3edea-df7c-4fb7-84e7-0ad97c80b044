"use client";
// Remove or comment out these unused imports
// import Button from "@/components/button/Button";
import { useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";
import "./Register.css";
import { useDispatch } from "react-redux";
import { register } from "../../../services/redux/middleware/register";
import { AppDispatch } from "../../../services/redux/store";
import { toast } from "react-toastify";

// Remove these lines:
interface RegisterPayload {
  email: string;
  password: string;
  confirmPassword: string;
  isOrganization: boolean;
  organizationName?: string;
  FullName?: string;
  designation?: string;
}

function OrgRegisterWeb() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const type = searchParams?.get("type") ?? "individual";
  const [activeType, setActiveType] = useState(type);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const dispatch = useDispatch<AppDispatch>();

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    designation: "", // Added designation field
  });

  const [errors, setErrors] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    designation: "", // Added designation field
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
      designation: "", // Added designation field
    };

    if (!formData?.name.trim()) {
      newErrors.name =
        activeType === "organization"
          ? "Organization name is required"
          : "Name is required";
      isValid = false;
    }

    if (!formData?.email.trim()) {
      newErrors.email = "Email is required";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(formData?.email)) {
      newErrors.email = "Email is invalid";
      isValid = false;
    }

    if (!formData?.password) {
      newErrors.password = "Password is required";
      isValid = false;
    } else if (formData?.password.length < 8) {
      newErrors.password = "Password must be at least 8 characters";
      isValid = false;
    }

    if (!formData?.confirmPassword) {
      newErrors.confirmPassword = "Please confirm your password";
      isValid = false;
    } else if (formData?.password !== formData?.confirmPassword) {
      newErrors.confirmPassword = "Passwords do not match";
      isValid = false;
    }

    // Add validation for designation
    if (activeType === "individual" && !formData?.designation.trim()) {
      newErrors.designation = "Designation is required";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handdleregister = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      setLoading(true);

      // Create data object based on registration type
      const data: RegisterPayload =
        activeType === "organization"
          ? {
            email: formData?.email,
            password: formData?.password,
            confirmPassword: formData?.confirmPassword,
            isOrganization: true,
            organizationName: formData?.name,
          }
          : {
            email: formData?.email,
            password: formData?.password,
            confirmPassword: formData?.confirmPassword,
            isOrganization: false,
            FullName: formData?.name,
            designation: formData?.designation,
          };
      console.log("mydata", data);
      localStorage.setItem("registerData", JSON.stringify(data));
      try {
        const result = await dispatch(register(data));
        console.log("result", result);
        console.log("token", result?.payload?.data?.token);
        if (result?.payload?.statusCode === 200) {
          const token = result?.payload?.data?.token;
          if (token) {
            localStorage.setItem("token", token);
          }
          router.push(
            `/Auth/Register/mail-verification?email=${data.email}&type=${activeType}`
          );
        } else if (result?.payload?.statusCode === 401) {
          toast.error("User already exists");
        }
      } finally {
        setLoading(false);
      }
    }
  };

  const handlesign = () => {
    router.push("/Auth/SignIn/signin");
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    setFormData((prev) => ({ ...prev, [id]: value }));
  };

  return (
    <div className="flex flex-col w-[35%] xl:w-[410px] lg:w-[40%] gap-[40px] formResponsive">
      <div className="flex flex-col gap-[24px]">
        <p className="text-[black] leading-[40px] font-[scandiaMedium] text-[32px]">
          Register
        </p>
        <div className="flex gap-[8px]">
          <button
            onClick={() => setActiveType("individual")}
            style={{
              border:
                activeType === "individual"
                  ? "none"
                  : "1px solid rgba(48, 48, 48, 1)",
              background:
                activeType === "individual"
                  ? "rgba(47, 59, 49, 1)"
                  : "transparent",
              color: activeType === "individual" ? "white" : "black",
            }}
            className="rounded-[80px] leading-[16px] cursor-pointer py-[8px] px-[24px] md:text-[16px] text-[12px] font-[scandiaMedium] transition-all duration-200"
          >
            As individual
          </button>

          <button
            onClick={() => setActiveType("organization")}
            style={{
              border:
                activeType === "organization"
                  ? "none"
                  : "1px solid rgba(48, 48, 48, 1)",
              background:
                activeType === "organization"
                  ? "rgba(47, 59, 49, 1)"
                  : "transparent",
              color: activeType === "organization" ? "white" : "black",
            }}
            className="rounded-[80px] cursor-pointer leading-[16px] py-[8px] px-[24px] md:text-[16px] text-[12px] font-[scandiaMedium] transition-all duration-200"
          >
            As organization
          </button>
        </div>
      </div>
      <form className="space-y-[40px]" onSubmit={handdleregister}>
        <div className="flex flex-col gap-[32px]">
          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="name"
              className="text-[14px] leading-[14px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              {activeType === "organization" ? "Organization Name" : "Name"}
            </label>
            <input
              id="name"
              type="text"
              value={formData?.name}
              onChange={handleChange}
              placeholder="Enter name"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                fontFamily: "Scandia",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                letterSpacing: "0%",
                verticalAlign: "middle",
                color: "#2F3B31",
              }}
              className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
            />
            {errors.name && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[14px] font-[scandiaMedium] text-center"
              >
                {errors.name}
              </span>
            )}
          </div>

          {activeType === "individual" && (
            <div className="space-y-[8px] flex flex-col">
              <label
                htmlFor="designation"
                className="text-[14px] leading-[14px] font-[scandiaMedium]"
                style={{ color: "rgba(48, 48, 48, 1)" }}
              >
                Designation
              </label>
              <input
                id="designation"
                type="text"
                value={formData?.designation}
                onChange={handleChange}
                placeholder="Ex: Project Manager, Lawyer..."
                style={{
                  borderTop: "none",
                  borderLeft: "none",
                  borderRight: "none",
                  borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                  fontFamily: "Scandia",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#2F3B31",
                }}
                className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
              />
              {errors.designation && (
                <span
                  style={{ color: "#D31B1B" }}
                  className="text-sm text-left leading-[14px] font-[scandiaMedium] text-center"
                >
                  {errors.designation}
                </span>
              )}
            </div>
          )}

          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="email"
              className="text-[14px] leading-[14px]  font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Email
            </label>
            <input
              id="email"
              type="email"
              value={formData?.email}
              onChange={handleChange}
              placeholder="Enter email"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                fontFamily: "Scandia",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                letterSpacing: "0%",
                verticalAlign: "middle",
                color: "#2F3B31",
              }}
              className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
            />
            {errors.email && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm leading-[14px] text-left font-[scandiaMedium] text-center"
              >
                {errors.email}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[14px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Password
            </label>
            <div className="relative">
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                value={formData?.password}
                onChange={handleChange}
                placeholder="Enter password"
                style={{
                  borderTop: "none",
                  borderLeft: "none",
                  borderRight: "none",
                  borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                  fontFamily: "Scandia",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#2F3B31",
                }}
                className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                {showPassword ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#23232366"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#23232366"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </svg>
                )}
              </button>
            </div>
            {errors.password && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm leading-[14px] text-left font-[scandiaMedium] text-center"
              >
                {errors.password}
              </span>
            )}
          </div>
          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[14px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Confirm password
            </label>
            <div className="relative">
              <input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={formData?.confirmPassword}
                onChange={handleChange}
                placeholder="Enter password again"
                style={{
                  borderTop: "none",
                  borderLeft: "none",
                  borderRight: "none",
                  borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                  fontFamily: "Scandia",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  verticalAlign: "middle",
                  color: "#2F3B31",
                }}
                className="bg-[transparent] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent] placeholder:text-[#23232366] placeholder:font-[scandia] placeholder:font-normal placeholder:text-[14px] placeholder:leading-[20px]"
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-2 top-1/2 transform -translate-y-1/2"
                style={{
                  background: "none",
                  border: "none",
                  cursor: "pointer",
                }}
              >
                {showConfirmPassword ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#23232366"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#23232366"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </svg>
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm leading-[14px] text-left font-[scandiaMedium] text-center"
              >
                {errors.confirmPassword}
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-[24px] justify-center text-center">
          <div
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <button
              style={{
                height: "52px",
                width: "105px",
                borderRadius: "80px",
                backgroundColor: "#D31B1B",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                border: "none",
              }}
            >
              {loading ? (
                <div className="loader"></div>
              ) : (
                <p
                  className="text-[14px] text-[white] font-[scandiaMedium]"
                  onClick={handdleregister}
                >
                  Register
                </p>
              )}
            </button>
          </div>

          <p
            style={{ color: "rgba(48, 48, 48, 1)" }}
            className="text-[14px] leading-[20px] font-[scandia]"
          >
            Already have an account?
            <span
              className=" font-[scandiaMedium] cursor-pointer"
              style={{ color: "rgba(211, 27, 27, 1)" }}
              onClick={handlesign}
            >
              {" "}
              Sign in
            </span>
          </p>
        </div>
      </form>
    </div>
  );
}

export default OrgRegisterWeb;
