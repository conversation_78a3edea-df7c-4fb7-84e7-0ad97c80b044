"use client";
import Button from "@/components/button/Button";
import { useRouter } from "next/navigation";
function MailVerifiedWeb() {
  const router = useRouter();
  const handlesign = () => {
    router.push("/Auth/SignIn/signin");
  };
  return (
    // <div className="flex flex-col items-center w-[40%] gap-[40px]">
    //   <div>
    //     <Image
    //       src="/Images/signin/elements.svg"
    //       alt="lock"
    //       width={64}
    //       height={64}
    //     />
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <p className="text-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
    //      Your email has been <br className='hiden-block-stlying'/> successfully verified
    //     </p>
    //     <p className="text-[14px] font-[scandia] leading-[20px] text-[black] text-center">
    //      You can now access your account and start contributing.
    //     </p>
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <Button>
    //       <p className="text-[14px] font-[scandiaMedium] text-[white]" onClick={handlesign}>
    //         Sign in
    //       </p>
    //     </Button>
    //   </div>
    // </div>
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:rounded-[20px] max-[370px]:px-[18px] max-[919px]:py-[40px] flex-col items-center max-[919px]:w-full w-[40%] max-[919px]:gap-[24px] gap-[40px]">
      <div>
        <img
          src="/Images/signin/elements.svg"
          alt="lock"
          className="max-[919px]:w-[40px] max-[919px]:h-[40px] w-[64px] h-[64px]"
        />
      </div>
      <div className="flex flex-col gap-[12px]">
        <p className="text-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
          Your email has been <br className="hiden-block-stlying" />{" "}
          successfully verified
        </p>
        <p className="text-[14px] font-[scandia] leading-[20px] text-[black] text-center">
          You can now access your account and start contributing.
        </p>
      </div>
      <div className="flex flex-col gap-[12px]">
        <Button onClick={handlesign}>
          <p className="text-[14px] font-[scandiaMedium] text-[white]">
            Sign in
          </p>
        </Button>
      </div>
    </div>
  );
}
export default MailVerifiedWeb;
