"use client";
import dynamic from "next/dynamic";

const Register = dynamic(
  () => import("@/pages/register/Register"),
  {
    ssr: false,
  }
);

const SignInCopm = dynamic(
  () => import("@/pages/signin/signinMob/SignInCopm"),
  {
    ssr: false,
  }
);


export default function page() {
  return (
    <>
      {" "}
      <div className="mobile-only">
        <SignInCopm />
      </div>
      <div className="desktop-only">
        <Register />
      </div>
    </>
  );
}

