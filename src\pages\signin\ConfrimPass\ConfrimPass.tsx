'use client";';
import Button from "@/components/button/Button";
// Remove or comment out the unused import
 import Image from "next/image";
import React from "react";

function ConfrimPass() {
  return (
    // <div className="flex flex-col items-center w-[40%] gap-[40px]">
    //   <div>
    //     <Image
    //       src="/Images/signin/elements.svg"
    //       alt="lock"
    //       width={64}
    //       height={64}
    //     />
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <p className="text-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
    //       New password has been <br className="hiden-block-stlying"/> successfully created
    //     </p>
    //     <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
    //       You can now access your account and start contributing.
    //     </p>
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <Button>
    //       <p className="text-[14px] font-[scandiaMedium] text-[white]">
    //         Sign in
    //       </p>
    //     </Button>
    //   </div>

    // </div>
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:rounded-[20px] max-[370px]:px-[18px] max-[919px]:py-[40px] flex-col items-center max-[919px]:w-full w-[40%] max-[919px]:gap-[24px] gap-[40px]">
      <div>
        <Image
          src="/Images/signin/elements.svg" 
          alt="description" 
          width={64} // Set appropriate width
          height={64} // Set appropriate height
        />
      </div>
      <div className="flex flex-col gap-[12px]">
        <p className="max-[919px]:text-[22px] text-[32px] max-[919px]:leading-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
          New password has been <br className="hiden-block-stlying" />{" "}
          successfully created
        </p>
        <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
          You can now access your account and start contributing.
        </p>
      </div>
      <div className="flex flex-col gap-[12px]">
        <Button>
          <p className="text-[14px] font-[scandiaMedium] text-[white]">
            Sign in
          </p>
        </Button>
      </div>
    </div>
  );
}

export default ConfrimPass;
