"use client";
import React, { useState } from "react";
import  MainSection  from "./components/MainSection";
import Breadcrumbs from "@/components/breadcrumbs/Breadcrumbs";
import  SideMenu  from "./components/SideMenu";

const Quizes = [
  {
    title: "Introduction to fundraising",
    locked: false,
    completed: false,
    quiz: {
      title: "Quiz Chapter One",
      percentageGained: 0,
      questions: [
        {
          title: "what is iltezam",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "true",
            b: "false",
          },
          correctAnswer: "true",
        },
      ],
    },
  },

  {
    title: "Developing a fundraising strategy",
    locked: false,
    completed: false,
    quiz: {
      title: "Quiz Chapter Two",
      percentageGained: 0,
      questions: [
        {
          title: "what is iltezam?",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "true",
            b: "false",
          },
          correctAnswer: "true",
        },
      ],
    },
  },

  {
    title: "Donor engagement and stewardship",
    locked: true,
    completed: false,
    quiz: {
      title: "Quiz Chapter Two",
      percentageGained: 0,
      questions: [
        {
          title: "what is iltezam?",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "app",
            b: "store",
            c: "declaration",
            d: "constitution",
          },
          correctAnswer: "app",
        },
        {
          title: "what is iltezam",
          option: {
            a: "true",
            b: "false",
          },
          correctAnswer: "true",
        },
      ],
    },
  },
];

export default function Quiz() {
  const [activeChapter, setActiveChapter] = useState(0);
  const [submittedAnswers, setSubmittedAnswers] = useState<
    Record<number, string[]>
  >({});
  const [quizes, setQuizes] = useState(Quizes);

  const handleRetakeQuiz = () => {
    setSubmittedAnswers((prev) => ({
      ...prev,
      [activeChapter]: [],
    }));

    setQuizes((prev) => {
      const updated = [...prev];
      updated[activeChapter] = {
        ...updated[activeChapter],
        completed: false,
        quiz: {
          ...updated[activeChapter].quiz,
          percentageGained: 0,
        },
      };
      return updated;
    });
  };

  const handleChapterChange = (index: number) => {
    setActiveChapter(index);
  };

  const handleQuizSubmit = (
    chapterIndex: number,
    answers: string[],
    percentage: number
  ) => {
    setSubmittedAnswers((prev) => ({
      ...prev,
      [chapterIndex]: answers,
    }));

    setQuizes((prev) => {
      const updated = [...prev];
      updated[chapterIndex] = {
        ...updated[chapterIndex],
        completed: true,
        quiz: {
          ...updated[chapterIndex].quiz,
          percentageGained: percentage,
        },
      };
      return updated;
    });
  };

  return (
    <div className="bg-[#ECE7E3] px-[24px] py-[24px] md:px-[40px] lg:px-[100px] md:py-[40px] mt-[100px] flex flex-col md:flex-row gap-5 w-full">
      <SideMenu
        chapters={quizes}
        activeChapter={activeChapter}
        setActive={handleChapterChange}
      />
      <div className="flex flex-col gap-6 w-full">
        <Breadcrumbs />
        <MainSection
          key={activeChapter} // Force re-mount on chapter change
          quiz={quizes[activeChapter]?.quiz}
          completed={quizes[activeChapter]?.completed}
          chapterIndex={activeChapter}
          defaultAnswers={submittedAnswers[activeChapter] || []}
          onSubmit={handleQuizSubmit}
          onRetake={handleRetakeQuiz}
        />
      </div>
    </div>
  );
}

