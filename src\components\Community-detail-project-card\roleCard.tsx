import React from 'react';
import Image from 'next/image';

interface RoleCardProps {
  role: string;
  openPositions: number;
  imageSrc: string;
  isPaid?: boolean;
  onApply?: () => void;
}

const RoleCard: React.FC<RoleCardProps> = ({
  role,
  openPositions,
  imageSrc,
  isPaid = false,
  onApply,
}) => {
  return (
    <div
      className="rounded-[20px] p-[16px] w-[180px] h-[178px] flex flex-col gap-[32px]"
      style={{ border: "1px solid #ECE7E3" }}
    >
      <div className="flex flex-col gap-[8px]">
        <div className="flex justify-between items-start">
          <div className="relative w-[32px] h-[32px]">
            <Image
              src={imageSrc}
              alt={role}
              layout="fill"
              objectFit="contain"
              className="cursor-pointer"
            />
          </div>
          {isPaid && (
            <div className="px-[8px] py-[4px] rounded-[80px] bg-[#FDDB83] w-[42px]">
              <p className="font-[scandia] text-[#303030] text-[12px] leading-[16px]">
                Paid
              </p>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-[2px]">
          <p className="font-[scandiaMedium] text-[#181916] text-[14px] leading-[20px]">
            {role}
          </p>
          <p className="font-[scandiaMedium] text-[#181916] text-[12px] leading-[16px]">
            Open positions : {openPositions}
          </p>
        </div>
      </div>

      <button
        className="border-none bg-[#D31B1B] rounded-[80px] px-[42.5px] h-[44px] font-[scandiaMedium] text-[#FFFFFF] text-[14px] leading-[20px]"
        onClick={onApply}
      >
        Apply
      </button>
    </div>
  );
};

export default RoleCard;
