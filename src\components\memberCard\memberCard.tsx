// components/MemberCard.tsx
"use client";

import React from "react";
import Image from "next/image";

type MemberCardProps = {
  name: string;
  title: string;
  imageUrl: string;
  actionLabel: string;
  onClick?: () => void;
};

export default function MemberCard({
  name,
  title,
  imageUrl,
  actionLabel,
  onClick,
}: MemberCardProps) {
  const isConnect = actionLabel === "Connect";

  return (
    <div className="py-[8px] flex justify-between items-center w-full">
      <div className="flex gap-[12px] items-center">
        <div className="relative h-[48px] w-[48px] max-[377px]:h-[30px] max-[377px]:w-[30px] rounded-full overflow-hidden">
          <Image
            src={imageUrl}
            alt={name}
            layout="fill"
            objectFit="cover"
            className="rounded-full"
          />
        </div>
        <div className="flex flex-col gap-[4px]">
          <p className="font-[scandiaMedium] text-[#000000] text-[14px] max-[377px]:text-[12px] max-[377px]:leading-[18px] leading-[20px]">
            {name}
          </p>
          <p className="font-[scandia] text-[#303030] text-[14px] max-[377px]:text-[12px] leading-[20px]">
            {title}
          </p>
        </div>
      </div>

      <button
        onClick={onClick}
        style={{
          border: isConnect ? "none" : "1px solid #303030",
        }}
        className={`h-[32px] flex cursor-pointer items-center py-[8px] px-[12px] text-[12px] leading-[16px] rounded-[80px] font-[scandiaMedium] ${
          isConnect ? "bg-[#D31B1B] text-white" : "bg-white text-[#303030]"
        }`}
      >
        {actionLabel}
      </button>
    </div>
  );
}
