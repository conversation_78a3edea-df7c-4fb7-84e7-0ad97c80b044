"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

const Breadcrumbs = () => {
  const pathname = usePathname();

  if (!pathname) return null; // Return nothing if pathname is not available yet

  const pathParts = pathname.split("/").filter(Boolean);

  const crumbs = pathParts.map((segment, index) => {
    const href = "/" + pathParts.slice(0, index + 1).join("/");
    const label = decodeURIComponent(segment.replace(/-/g, " ")).replace(
      /\b\w/g,
      (char) => char.toUpperCase()
    );
    return { label, href };
  });

  return (
    <nav className="text-sm flex items-center flex-wrap space-x-2 text-gray-700">
      {crumbs.map((crumb, index) => {
        const isLast = index === crumbs.length - 1;
        return (
          <div key={crumb.href} className="flex space-x-2 items-center">
            {index > 0 && (
              <span className="h-[13px] w-auto items-center">
                <Image
                  src="/Images/learn/From.png"
                  alt="arrow"
                  width={13}
                  height={13}
                  className="h-[13px] w-auto"
                />
              </span>
            )}
            {isLast ? (
              <span
                style={{
                  fontFamily: "ScandiaMedium",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  color: "#303030",
                }}
              >
                {crumb.label}
              </span>
            ) : (
              <Link
                style={{
                  fontFamily: "ScandiaMedium",
                  fontWeight: 500,
                  fontSize: "14px",
                  lineHeight: "20px",
                  letterSpacing: "0%",
                  color: "#303030",
                }}
                href={crumb.href}
                className="hover:underline font-medium"
              >
                {crumb.label}
              </Link>
            )}
          </div>
        );
      })}
    </nav>
  );
};

export default Breadcrumbs;
