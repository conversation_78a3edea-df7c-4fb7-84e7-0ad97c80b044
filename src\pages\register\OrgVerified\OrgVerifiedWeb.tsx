"use client";
// Remove the unused import
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

function OrgVerifiedWeb() {
  const router = useRouter();
  const [registrationData, setRegistrationData] = useState({
    organizationName: "",
    email: ""
  });

  useEffect(() => {
    // Get data from localStorage
    const storedData = localStorage.getItem('registerData');
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      setRegistrationData({
        organizationName: parsedData.organizationName || "",
        email: parsedData.email || ""
      });
    }
  }, []);

  const handleCompleteRegistration = () => {
    router.push('/Auth/Register/org-register');
  };

  return (
    // <div className="flex flex-col items-center w-[40%] gap-[40px]">
    //   <div>
    //     <Image
    //       src="/Images/signin/elements.svg"
    //       alt="lock"
    //       width={64}
    //       height={64}
    //     />
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <p className="text-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
    //       Your organization email has<br className="hiden-block-stlying"/> been successfully verified
    //     </p>
    //     <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
    //       Verify your organization and complete the registration process
    //     </p>
    //   </div>
    //   <div className="flex flex-col gap-[12px]">
    //     <Button>
    //       <p className="text-[14px] font-[scandiaMedium] text-[white]">
    //         Complete registration
    //       </p>
    //     </Button>dcadsc
    //   </div>
    // </div>
    <div className="flex bg-[white] max-[919px]:px-[24px] max-[919px]:shadow-[4px_12px_24px_0px_#0000000A] max-[919px]:rounded-[20px] max-[370px]:px-[18px] max-[919px]:py-[40px] flex-col items-center max-[919px]:w-full w-[40%] max-[919px]:gap-[24px] gap-[40px]">
      <div>
        <Image 
          src="/path/to/your/image.jpg" 
          alt="Your image description" 
          width={500} // Adjust to appropriate size
          height={300} // Adjust to appropriate size
        />
      </div>
      <div className="flex flex-col gap-[12px]">
        <p className="max-[919px]:text-[22px] text-[32px] max-[919px]:leading-[32px] leading-[40px] font-[scandiaMedium] text-[black] text-center">
          Your organization email has
          <br className="hiden-block-stlying" /> been successfully verified
        </p>
        <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
          Verify your organization and complete the registration process
        </p>
        <div className="flex flex-col gap-[8px] mt-[16px]">
          <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[#232323]">
            Organization Name: <span className="font-[scandia]">{registrationData.organizationName}</span>
          </p>
          <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[#232323]">
            Email: <span className="font-[scandia]">{registrationData.email}</span>
          </p>
        </div>
      </div>
      <div className="flex flex-col gap-[12px]">
        

        
        <button
              style={{
                height: "52px",
                width: "105px",
                borderRadius: "80px",
                backgroundColor: "#D31B1B",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                cursor: "pointer",
                border: "none",
              }}
              onClick={handleCompleteRegistration}
            >
              
                <p
                  className="text-[14px] text-[white] font-[scandiaMedium]"
                 
                >
                  Register
                </p>
              
            </button>
      </div>
      <div className="flex flex-col max-[919px]:block hidden  join-pading justify-center ">
        <p className="text-[50px] max-[919px]:pb-[12px] max-[380px]:text-[32px] max-[919px]:text-[40px] max-[919px]:leading-[40px] leading-[40px] font-[scandiaMedium] text-[white]">
          Join to give the impact
        </p>
        <p className="max-[919px]:text-[14px] text-[22px] max-[919px]:leading-[20px] leading-[32px] font-[scandia] text-[white]">
          Join a community dedicated to rebuilding, empowering, and inspiring a
          brighter future for Syria.
        </p>
      </div>
      <OrgVerifiedWeb />
    </div>
  );
}

export default OrgVerifiedWeb;
