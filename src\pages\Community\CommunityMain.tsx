/* eslint-disable */
"use client";
import React from "react";
import { useState, useEffect } from "react";
import Image from "next/image";
import Feeds from "../feeds/Feeds";
import CommunityMembers from "./Member/Members";
import CommunityOrganizations from "./Organization/Organizations";
import {
  Select,
  MenuItem,
  FormControl,
  SelectChangeEvent,
} from "@mui/material";
import Group from "./Group/Group";
type TabKey = "home" | "member" | "organization" | "group" | "message";
// In your types file or at the top of Community.tsx
type OrganizationSubPage = "list" | "details" | "create" | "contribute" | "donate" | "confirm" | "submit"; 

interface OrganizationProps {
  onNavigate: (page: OrganizationSubPage) => void;
}
type membersSubPage = "list" | "details" | "create";
const ComponentsMap = ({

  onOrgNavigate,
  onmemberNavigate,
}: {
  activeTab: TabKey;
  onOrgNavigate: (page: OrganizationSubPage) => void;
  onmemberNavigate: (page: membersSubPage) => void;
}) => ({
  home: <Feeds />,
  member: <CommunityMembers onNavigate={onmemberNavigate} />,
  organization: <CommunityOrganizations onNavigate={onOrgNavigate} />,
  group: <Group />,
  message: <Feeds />,
});


const navItems = [
  {
    key: "home",
    label: "Home Feed",
    icon: "/Images/CommunityMain/homefeed.svg",
    activeIcon: "/Images/CommunityMain/homeFeedActive.svg",
  },
  {
    key: "member",
    label: "Member",
    icon: "/Images/CommunityMain/member.svg",
    activeIcon: "/Images/CommunityMain/memberactive.svg",
  },
  {
    key: "organization",
    label: "Organization",
    icon: "/Images/CommunityMain/organization.svg",
    activeIcon: "/Images/CommunityMain/organization-active.svg",
  },
  {
    key: "group",
    label: "Group",
    icon: "/Images/CommunityMain/group.svg",
    activeIcon: "/Images/CommunityMain/group-active.svg",
  },
  {
    key: "message",
    label: "Message",
    icon: "/Images/CommunityMain/message.svg",
    activeIcon: "/Images/CommunityMain/message-active.svg",
  },
];

function Community() {
  const [activeTab, setActiveTab] = useState<TabKey | null>(() => {
    if (typeof window !== "undefined") {
      const savedTab = localStorage.getItem("communityActiveTab");
      return savedTab as TabKey | null;
    }
    return null;
  });

  const [orgSubPage, setOrgSubPage] = useState<OrganizationSubPage>("list");
  const [memberSubPage, setmemberSubPage] = useState<membersSubPage>("list");
  const [open, setOpen] = useState(false);
  const handleOrgNavigation = (page: OrganizationSubPage) => {
    setActiveTab("organization");
    setOrgSubPage(page);
  };

  const handlememberNavigation = (page: membersSubPage) => {
    setActiveTab("member");
    setmemberSubPage(page);
  };

  useEffect(() => {
  if (activeTab !== null) {
    localStorage.setItem("communityActiveTab", activeTab);
  }
}, [activeTab]);

  return (
    <div className="pb-[40px] pt-[140px] max-[1250px]:px-[50px]  max-[550px]:px-[24px] px-[20px] px-[100px] max-[1000px]:bg-white bg-[#ECE7E3] flex max-[1000px]:flex-col flex-row gap-[24px]">
      <div
        style={{
          height: "max-content",
        }}
        className="max-[1000px]:hidden flex  w-[100%] min-w-[328px] max-w-[328px] rounded-[20px] py-[24px] px-[32px] bg-[#FFFFFF] flex-col gap-[32px]"
      >
        <p className="font-[scandiaMedium] font-medium text-[28px] leading-[36px] text-[#232323]">
          Community
        </p>
        <div className="flex flex-col gap-[8px]">
          {navItems.map((item) => {
            const isActive = item.key === activeTab;
            return (
              <div
                key={item.key}
                onClick={() => setActiveTab(item.key as TabKey)} // Added type assertion
                className={`flex items-center gap-[12px] py-[12px] px-[24px] cursor-pointer rounded-[80px] transition-all ${
                  isActive ? "bg-[#2F3B31]" : "hover:bg-[#F0F0F0]"
                }`}
              >
                <Image
                  src={isActive ? item.activeIcon : item.icon}
                  alt={item.label}
                  width={20}
                  height={20}
                  className="w-5 h-5"
                />
                <span
                  className={`font-[scandiaMedium] font-medium text-[16px] leading-[24px] ${
                    isActive ? "text-[#FFFFFF] " : "text-[#181916]"
                  }`}
                >
                  {item.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>
      <div className="max-[1000px]:block hidden">
        <FormControl
          fullWidth
          sx={{
            background: "white",
            borderRadius: "99px",
            border: "1px solid #ECE7E3",
            padding: "0",
            fontFamily: "ScandiaMedium",
            "& .MuiOutlinedInput-root": {
              borderRadius: "99px",
              padding: "0",
              "& .MuiOutlinedInput-input": {
                padding: "12px 20px !important",
                border: "none",
              },
              "& fieldset": {
                border: "none",
              },
              "&:hover": {
                background: "none !important",
                backgroundColor: "transparent !important",
              },
              "& .MuiMenuItem-root": {
                "&:hover": {
                  background: "none !important",
                  backgroundColor: "transparent !important",
                },
                "&.Mui-selected": {
                  background: "none !important",
                  backgroundColor: "transparent !important",
                },
                "&.Mui-selected:hover": {
                  background: "none !important",
                  backgroundColor: "transparent !important",
                },
              },
              "& .MuiTouchRipple-root": {
                display: "none !important",
              },
            },
          }}
        >
          <Select
            displayEmpty
            IconComponent={() => null}
            renderValue={(selected) => {
              if (!selected) {
                return "Community";
              }
              const item = navItems.find((item) => item.key === selected);
              return item?.label || "Community";
            }}
            onOpen={() => setOpen(true)}
            onClose={() => setOpen(false)}
            onChange={(e: SelectChangeEvent) => {
              const value = e.target.value;
              if (value && Object.values(navItems).some(item => item.key === value)) {
                setActiveTab(value as TabKey);
              }
            }}
            MenuProps={{
              PaperProps: {
                elevation: 0,
                sx: {
                  fontFamily: "ScandiaMedium",
                  boxShadow: "none",
                  mt: "10px",
                  borderRadius: "30px",
                  border: "1px solid #ECE7E3",
                  padding: "24px",
                  "& .MuiList-root": {
                    display: "flex",
                    flexDirection: "column",
                    gap: "12px", // Adjust gap size here (e.g., 8px, 16px)
                    padding: 0, // Remove default padding
                  }, // ← gap between MenuItems
                  "& .MuiMenuItem-root.Mui-selected": {
                    backgroundColor: "transparent !important",
                    fontFamily: "ScandiaMedium !important",
                  },
                },
              },
            }}
            sx={{
              position: "relative",
              "& .MuiSelect-select": {
                paddingRight: "30px",
                fontFamily: "ScandiaMedium",
              },
            }}
          >
            {navItems.map((item) => (
              <MenuItem
                key={item.key}
                value={item.key}
                sx={{
                  padding: "0px",
                  "&:hover": {
                    backgroundColor: "transparent !important",
                  },
                  "&.Mui-selected": {
                    backgroundColor: "transparent !important",
                  },
                  "&.Mui-selected:hover": {
                    backgroundColor: "transparent !important",
                  },
                  "&:focus": {
                    backgroundColor: "transparent !important",
                  },
                  "& .MuiTouchRipple-root": {
                    display: "none !important",
                  },
                }}
              >
                <div
                  style={{
                    display: "flex",
                    gap: "6px",
                    alignItems: "center",
                    paddingTop: "8px",
                    paddingBottom: "8px",
                  }}
                >
                  <Image src={item.icon} alt={item.label} width={20} height={20} />
                  <p
                    style={{
                      fontFamily: "ScandiaMedium",
                      fontSize: "14px",
                      lineHeight: "20px",
                    }}
                  >
                    {item.label}
                  </p>
                </div>
              </MenuItem>
            ))}
          </Select>

          {/* Custom Arrow */}
          <div
            style={{
              position: "absolute",
              right: "25px",
              top: "50%",
              transform: `translateY(-50%) rotate(${open ? 180 : 0}deg)`,
              transition: "transform 0.3s ease",
              pointerEvents: "none",
            }}
          >
            <Image
              src="/Images/registration/arrow-down.svg"
              alt="arrow"
              width={20}
              height={20}
            />
          </div>
        </FormControl>
      </div>
      <div className="flex-grow">
  {
    ComponentsMap({
      activeTab: activeTab as TabKey,
      onOrgNavigate: handleOrgNavigation,
      onmemberNavigate: handlememberNavigation,
    })[activeTab ?? "home"]
  }
</div>

    </div>
  );
}

export default Community;
