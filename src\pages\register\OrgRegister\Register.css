@media (max-height: 680px) {
    .formResponsive {
       height: 480px;
       overflow-y: auto;
       /* Hide scrollbar for Chrome, Safari and Opera */
       &::-webkit-scrollbar {
           display: none;
       }
       /* Hide scrollbar for IE, Edge and Firefox */
       -ms-overflow-style: none;  /* IE and Edge */
       scrollbar-width: none;  /* Firefox */
    }
}


.loader {
    width: 30px;
    aspect-ratio: 4;
    --_g: no-repeat radial-gradient(circle closest-side,#fff 90%,#0000);
    background: 
      var(--_g) 0%   50%,
      var(--_g) 50%  50%,
      var(--_g) 100% 50%;
    background-size: calc(100%/3) 100%;
    animation: l7 1s infinite linear;
  }
  
  @keyframes l7 {
    33% {
      background-size: calc(100%/3) 0%, calc(100%/3) 100%, calc(100%/3) 100%;
    }
    50% {
      background-size: calc(100%/3) 100%, calc(100%/3) 0%, calc(100%/3) 100%;
    }
    66% {
      background-size: calc(100%/3) 100%, calc(100%/3) 100%, calc(100%/3) 0%;
    }
  }
  