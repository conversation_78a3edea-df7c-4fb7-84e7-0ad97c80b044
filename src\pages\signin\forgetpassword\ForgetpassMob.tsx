"use client";
import Button from "@/components/button/Button";
import Image from "next/image";
// Remove or comment out the unused imports
// import { useRouter } from "next/navigation";
// import { useState } from "react";
// Remove useEffect if not used

function ForgetpassMob() {
  // Remove unused variables
  // const [seconds, setSeconds] = useState(0);
  // const router = useRouter();
  
  // Keep only the state variables you're using
  
  return (
    <div className="w-full">
      <div
        style={{ backgroundColor: "white", borderRadius: "20px" }}
        className="flex flex-col gap-[40px] py-[40px] px-[24px] "
      >
        <div className="flex flex-col items-center gap-[24px]">
          <div>
            <Image
              src="/Images/signin/square-lock-02.svg"
              alt="lock"
              width={40}
              height={40}
            />
          </div>
          <div className="flex flex-col gap-[8px]">
            <p className="text-[22px] leading-[32px] leading-[30px] font-[scandiaMedium] text-[black] text-center">
              Forgot password
            </p>
            <p className="text-[14px] leading-[20px] font-[scandia] text-[black] text-center">
              Email confirmation has been sent to
              <span className="text-[14px] cursor-pointer font-[scandiaMedium] text-[black]">
                {" "}
                <EMAIL>.
              </span>
              <br className="hidden xl:block" /> Check your inbox to create new
              password.
            </p>
          </div>
          <div className="flex flex-col gap-[8px]">
            <p className="text-[14px] leading-[16px] font-[scandia] text-[black] text-center">
              Didn’t receive any email?
            </p>
            <Button>
              <p className="text-[14px] font-[scandiaMedium] text-[white]">
                Resend in 
                {/* {seconds}s */}
              </p>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ForgetpassMob;
