// import { createSlice } from "@reduxjs/toolkit";
// import { forgetPassword } from "../middleware/forgetPassword"; // your asyncThunk

// interface ForgetPasswordState {
//   loading: boolean;
//   error: string | null;
//   forgetPassword: any[]; // adjust type as needed
// }

// const initialState: ForgetPasswordState = {
//   loading: false,
//   error: null,
//   forgetPassword: [],
// };

// const forgetPasswordSlice = createSlice({
//   name: "forgetPassword",
//   initialState,
//   reducers: {},
//   extraReducers: (builder) => {
//     builder
//       .addCase(forgetPassword.pending, (state) => {
//         state.loading = true;
//         state.error = null;
//       })
//       .addCase(forgetPassword.fulfilled, (state, action) => {
//         state.loading = false;
//         state.forgetPassword = action.payload;
//       })
//       .addCase(forgetPassword.rejected, (state, action) => {
//         state.loading = false;
//         state.error = action.error.message || "Something went wrong";
//       });
//   },
// });

// export default forgetPasswordSlice.reducer;
