import React from "react";
import { Button } from "../../../../components/ui/button";
import Image from "next/image";

const HeroSectionByAnima = () => {
  return (
    <section className="flex flex-col md:flex-row items-center justify-center gap-8 md:gap-32 py-26 md:py-32 px-6 md:px-24 bg-app-primary bg-[#2F3B31]">
      <div className="w-full md:w-1/2 max-w-[545px] aspect-square">
        <div className="grid grid-cols-3 grid-rows-3 gap-4 h-full">
          <div className="col-span-2 row-span-2 overflow-hidden rounded-[60px]">
            <Image 
              className="w-full h-full object-cover" 
              src="/Images/homepage/woman.png" 
              alt="Rectangle"
              width={500}
              height={500}
            />
          </div>
          <div className="col-span-1 row-span-1 overflow-hidden rounded-[60px]">
            <Image 
              className="w-full h-full object-cover" 
              alt="Arab people" 
              src="/Images/homepage/boy.png"
              width={500}
              height={250}
            />
          </div>
          <div className="col-span-1 row-span-2 overflow-hidden rounded-[60px]">
            <Image 
              className="w-full h-full object-cover" 
              alt="Muslim people with" 
              src="/Images/homepage/man.png"
              width={500}
              height={500}
            />
          </div>
          <div className="col-span-2 row-span-1 overflow-hidden rounded-[60px]">
            <Image 
              className="w-full h-full object-cover" 
              alt="Handsome happy" 
              src="/Images/homepage/child.png"
              width={500}
              height={250}
            />
          </div>
        </div>
      </div>

      <div className="flex flex-col items-start gap-8 w-full md:w-1/2">
        <h1 className="font-[ScandiaMedium] text-[24px] md:text-[34px] lg:text-[50px] text-white">
          One Community. One Mission. A Better Tomorrow.
        </h1>

        <p className="font-[Scandia] text-white text-[12px] md:text-[14px]">
          Our platform unites Syrians from around the world, fostering
          collaboration, sharing ideas, and driving meaningful change. Join a
          community dedicated to rebuilding, empowering, and inspiring a
          brighter future for Syria.
        </p>

        <Button className="px-12 md:px-16 py-2 md:py-4 h-auto bg-[#D31B1B] rounded-[30px] md:rounded-[80px] text-white hover:bg-[#D31B1B]/90 cursor-pointer">
          <span className="font-[ScandiaMedium] text-white text-[12px] md:text-[14px]">
            Learn More
          </span>
        </Button>
      </div>
    </section>
  );
};
export default HeroSectionByAnima;


