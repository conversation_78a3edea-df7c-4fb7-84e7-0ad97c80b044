"use client";

import type { FormData } from "./StepsRegistration";
import YesNoSelector from "./yesNoSelector";
type Props = {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
};

export default function Step3({ formData, setFormData }: Props) {
  return (
    <div className="flex flex-col gap-[32px]">
      <YesNoSelector
        title="Good Governance"
        question="Is there clear leadership, an independent board, and a commitment to ethical decision-making?"
        value={formData.goodGovernance}
        onChange={(val) =>
          setFormData((prev) => ({ ...prev, goodGovernance: val }))
        }
      />
      <YesNoSelector
        title="Transparency & Reporting"
        question="Are finances, donors, and impact openly disclosed to the public?"
        value={formData.transparencyReporting}
        onChange={(val) =>
          setFormData((prev) => ({ ...prev, transparencyReporting: val }))
        }
      />
      <YesNoSelector
        title="Sustainable Funding"
        question="Are revenue sources diversified to avoid over-reliance on a single donor?"
        value={formData.sustainableFunding}
        onChange={(val) =>
          setFormData((prev) => ({ ...prev, sustainableFunding: val }))
        }
      />
      <div className="flex flex-col gap-[12px]">
        <p className="font-[scandiaMedium] font-medium text-[16px] leading-[24px] text-[#181916]">
          Impact Measurement
        </p>

        <div className="flex flex-col gap-[8px]">
          <p className="font-[scandiaMedium] font-medium text-[14px] leading-[20px] text-[#232323]">
            How are results demonstrated through data, storytelling, and
            accountability reports?
          </p>
          <textarea
            value={formData.impactMeasurement}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                impactMeasurement: e.target.value,
              }))
            }
            placeholder="Write answer here"
            className="w-full overflow-hidden resize-none bg-transparent font-[scandiamedium] text-[14px] leading-[20px] border-0 border-b-[1.5px] border-[#DEDEDE] focus:outline-none focus:ring-0 text-[#181916] placeholder:font-[scandia] placeholder:text-[#CFCFCF]"
            rows={1}
//              style={{
//     maxHeight: `${20 * 1}px`, // 4 lines x 20px line-height
//   }}
            onInput={(e) => {
              const target = e.target as HTMLTextAreaElement;
              target.style.height = "auto";
              target.style.height = target.scrollHeight + "px";
            }}
          />
        </div>
      </div>
    </div>
  );
}
