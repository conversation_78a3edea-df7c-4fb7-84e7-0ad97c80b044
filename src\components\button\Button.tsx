'use client';
import React, { ReactNode, ButtonHTMLAttributes } from "react";

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  children: ReactNode;
}

const Button: React.FC<ButtonProps> = ({ children, ...rest }) => {
  return (
    <button
      {...rest}
      className="flex justify-center items-center cursor-pointer w-fit rounded-[80px] max-[919px]:py-[12px] py-[16px] px-[24px]"
      style={{ background: "rgba(211, 27, 27, 1)" }}
    >
      <span className="leading-[20px]">{children}</span>
    </button>
  );
};

export default Button;

