// import Button from "@/components/button/Button";
import React, { useState } from "react";
import Image from "next/image";
interface AnyDonationProps {
   onNavigate: (page: "list" | "details" | "donate" | "confirm" | "submit") => void;
  //  onBack: () => void;
}
function 
AnyDonation({ onNavigate }: AnyDonationProps)  {
  const [activeType, setActiveType] = useState("Volunteering");
  const [customAmount, setCustomAmount] = useState("");
  const [formData, setFormData] = useState({
    privacyPolicy: false,
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomAmount(e.target.value);
  };

  const isCustom = activeType === "Volunteering";

  const getInputValue = () => {
    if (isCustom) return customAmount;
    return activeType.replace("$", "");
  };

  return (
    <div className="flex flex-col gap-[40px]">
      <div className="flex flex-col gap-[16px]">
        <p className="font-[scandiaMedium] text-[16px] leading-[15px] text-[#303030]">Select amount</p>
        <div className="flex gap-[8px] flex-wrap">
          {["$100", "$200", "$500", "Volunteering"].map((amount) => (
            <button
              key={amount}
              onClick={() => setActiveType(amount)}
              style={{
                border:
                  activeType === amount
                    ? "none"
                    : "1px solid rgba(48, 48, 48, 1)",
                background:
                  activeType === amount ? "rgba(47, 59, 49, 1)" : "transparent",
                color: activeType === amount ? "white" : "black",
              }}
              className="rounded-[80px] leading-[20px] cursor-pointer py-[16px] px-[24px] md:text-[14px] text-[12px] font-[scandiaMedium] transition-all duration-200"
            >
              {amount === "Volunteering" ? "Other" : amount}
            </button>
          ))}
        </div>

        <div className="space-y-[8px] flex flex-col">
          <label
            htmlFor="amount"
            className="text-[14px] leading-[20px] text-[rgba(48, 48, 48, 1)] font-[scandiaMedium]"
          >
            {isCustom ? "Other amount" : "Amount"}
          </label>
          <input
            id="amount"
            type="number"
            value={getInputValue()}
            onChange={handleInputChange}
            placeholder="Enter amount"
            disabled={!isCustom}
            style={{
              borderTop: "none",
              borderLeft: "none",
              borderRight: "none",
              borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
              color: "rgba(48, 48, 48, 1)",
              paddingTop: "0px",
              paddingBottom: "8px",
              backgroundColor: isCustom ? "transparent" : "#f9f9f9",
            }}
            className="bg-[none] placeholder:leading-[20px] placeholder:font-[scandia] font-[scandia] text-[14px] leading-[16px] leading-[20px]  placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent]"
          />
        </div>
      </div>

      {/* Privacy Policy Checkbox */}
      <div className="flex flex-col gap-[12px]">
        <div className="flex gap-[8px] cursor-pointer items-center">
          <div
            onClick={() =>
              setFormData((prev) => ({
                ...prev,
                privacyPolicy: !prev.privacyPolicy,
              }))
            }
          >
            <Image
              src={
                formData.privacyPolicy
                  ? "/Images/registration/tickSquare.svg"
                  : "/Images/registration/emptybox.svg"
              }
              alt="checkbox"
              width={20}
              height={20}
            />
          </div>
          <p className="font-[scandiaMedium] text-[12px] leading-[16px] text-[#303030]">
            Make this a monthly donation
          </p>
        </div>
        <div>
          <div className="flex justify-start flex-wrap gap-[4px]">
            <p className="bg-[#F8F8F8] text-[12px] leading-[16px] font-[scandia] text-[#303030] px-[16px] py-[12px] rounded-[12px] ">
              This donation will be processed{" "}
              <span className="font-[scandiaMedium] "> monthly</span> until{" "}
              <span className="font-[scandiaMedium] "> June 2025 </span> or
              until you update your donation preferences at any time.
            </p>
          </div>
        </div>
      </div>
      <button className="bg-[#D31B1B] max-[370px]:w-full w-fit rounded-[80px] py-[16px] px-[33.5px]"
       onClick={() => onNavigate("confirm")}>
        <p className="font-[scandiaMedium] text-[14px] text-[white] leading-[20px] ">
          Donate now
        </p>
      </button>
      <div className="flex gap-[12px]">
        <Image src="/Images/community/Apple Pay.svg" alt="Apple Pay" width={40} height={24} />
        <Image src="/Images/community/Google Pay.svg" alt="Google Pay" width={40} height={24} />
        <Image src="/Images/community/Samsung Pay.svg" alt="Samsung Pay" width={40} height={24} />
        <Image src="/Images/community/Visa.svg" alt="Visa" width={40} height={24} />
        <Image src="/Images/community/Mastercard.svg" alt="Mastercard" width={40} height={24} />
      </div>
    </div>
  );
}

export default AnyDonation;
