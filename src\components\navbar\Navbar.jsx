"use client";
import * as React from "react";
import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import CssBaseline from "@mui/material/CssBaseline";
import Divider from "@mui/material/Divider";
import Drawer from "@mui/material/Drawer";
import IconButton from "@mui/material/IconButton";
import List from "@mui/material/List";
import ListItem from "@mui/material/ListItem";
import ListItemButton from "@mui/material/ListItemButton";
import ListItemText from "@mui/material/ListItemText";
import MenuIcon from "@mui/icons-material/Menu";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import Button from "@mui/material/Button";
import Image from "next/image";
import Modal from "@mui/material/Modal";
import CloseIcon from "@mui/icons-material/Close";
import NotificationsNoneIcon from "@mui/icons-material/NotificationsNone";
import Notification from "../notification/Notification";
import { useRouter } from "next/navigation";
import { usePathname } from "next/navigation";
const drawerWidth = 240;
const navItems = ["Projects", "Community", "Learn", "About us"];

function DrawerAppBar() {
  const router = useRouter();
  const pathname = usePathname();
  const [mobileOpen, setMobileOpen] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState("");
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [isMobileMenu, setIsMobileMenu] = useState(false);
  const [screenWidth, setScreenWidth] = useState(0);
  const [modalOpen, setModalOpen] = React.useState(false);
  const [notificationOpen, setNotificationOpen] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setScreenWidth(width);
      setIsSmallScreen(width < 1170);
      setIsMobileMenu(width < 1000);
    };
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  useEffect(() => {
    if (pathname.includes === "/Project/Projects") {
      setSelectedItem("Projects");
      setModalOpen(false);
    } else if (pathname.includes("/Community")) {
      setSelectedItem("Community");
    } else if (pathname.includes("/learn")) {
      setSelectedItem("Learn");
    } else if (pathname.includes("/aboutus")) {
      setSelectedItem("About us");
    } else {
      setSelectedItem("");
    }
  }, [pathname]);
  const handleDrawerToggle = () => {
    setMobileOpen((prevState) => !prevState);
  };

  const handleModalToggle = () => {
    setModalOpen((prevState) => !prevState);
  };
  const handleSignIn = () => {
    router.push("/Auth/SignIn/signin");
  };
  const handleHomeClick = () => {
    router.push("/");
  };
  const toolbarStyle = {
    minHeight: "100px",
    padding:
      screenWidth < 425 ? "0px 20px" : isSmallScreen ? "0px 50px" : "0px 100px",
    flexDirection:
      screenWidth < 425
        ? "row-reverse"
        : isSmallScreen
        ? "row-reverse"
        : "0px 100px",
    justifyContent: screenWidth < 425 ? "space-between" : "none",
  };

  const dropdownModal = (
    <Modal
      open={modalOpen}
      onClose={handleModalToggle}
      aria-labelledby="menu-modal"
      sx={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Box
        sx={{
          width: "100%",
          height: "100vh",
          bgcolor: "#2F3B31",
          // borderRadius: "12px",
          // boxShadow: 24,
          outline: "none",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          position: "relative",
        }}
      >
        <Box
          sx={{
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            px: 3,
            pt: 2,
            pb: 2,
            borderBottom: "1px solid #D9D9D91A",
          }}
        >
          <Image
            onClick={handleHomeClick}
            src="/Images/navbar/logo.svg"
            alt="Iltezam"
            width={90}
            height={50}
            style={{ cursor: "pointer" }}
          />
          <Box sx={{ display: "flex", alignItems: "center", gap: 2.5 }}>
            <Image
              src="/Images/navbar/notify.svg"
              alt="notify"
              width={24}
              height={24}
            />
            <IconButton onClick={handleModalToggle}>
              <Image
                src="/Images/navbar/cancel.svg"
                alt="cancel"
                width={24}
                height={24}
              />
            </IconButton>
          </Box>
        </Box>
        <Box
          sx={{
            padding: "44px 0px",
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            justifyContent: "center",
            gap: 4,
            width: "100%",
          }}
        >
          {navItems.map((item) => (
            <Typography
              key={item}
              sx={{
                fontFamily: "ScandiaMedium",
                fontWeight: 500,
                fontSize: "14px",
                lineHeight: "20px",
                letterSpacing: "0%",
                textAlign: "center",
                verticalAlign: "middle",
                color: "#fff",
              }}
              onClick={() => {
                setSelectedItem(item);
                if (item === "Projects") {
                  router.push("/Project/Projects");
                  setModalOpen(false);
                } else if (item === "Community") {
                  router.push("/Community");
                  setModalOpen(false);
                } else if (item === "Learn") {
                  router.push("/learn");
                  setModalOpen(false);
                } else if (item === "About us") {
                  router.push("/aboutus");
                  setModalOpen(false);
                } else {
                  router.push("/"); // Default route for Projects
                  setModalOpen(false);
                }
              }}
            >
              {item}
            </Typography>
          ))}
        </Box>
        <Divider sx={{ width: "80%", bgcolor: "#fff", opacity: 0.1, mb: 3 }} />
        <Button
          sx={{
            background: "#D31B1B",
            color: "#fff",
            borderRadius: "80px",
            padding: "12px 24px",
            fontFamily: "ScandiaMedium",
            fontWeight: 500,
            fontSize: "14px",
            lineHeight: "20px",
            textAlign: "center",
            verticalAlign: "middle",
            mb: 3,
            textTransform: "none",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "10px",
            cursor: "pointer",
          }}
          onClick={handleSignIn}
        >
          Sign in
          <Image
            src="/Images/navbar/arrow.svg"
            alt="arrow"
            width={18}
            height={18}
          />
        </Button>
        <Button
          sx={{
            background: "#ECE7E3",
            color: "#181916",
            borderRadius: "80px",
            padding: "12px 24px",
            fontFamily: "ScandiaMedium",
            fontWeight: 500,
            fontSize: "14px",
            lineHeight: "20px",
            textAlign: "center",
            verticalAlign: "middle",
            mb: 3,
            textTransform: "none",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "10px",
          }}
        >
          Account dashboard{" "}
          <Image
            src="/Images/navbar/dashboard.svg"
            alt="arrow"
            width={18}
            height={18}
          />
        </Button>
      </Box>
    </Modal>
  );

  const container =
    typeof window !== "undefined" ? () => window.document.body : undefined;

  return (
    <Box sx={{ display: "flex" }}>
      <CssBaseline />
      <AppBar
        sx={{ backgroundColor: "#2F3B31", boxShadow: "none" }}
        component="nav"
      >
        <Toolbar style={toolbarStyle}>
          <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
            {isMobileMenu && (
              <Image
                src="/Images/navbar/notify.svg"
                alt="notify"
                width={24}
                height={24}
                style={{ cursor: "pointer" }}
                onClick={() => setNotificationOpen(true)}
              />
            )}
            <IconButton
              color="inherit"
              aria-label="open menu"
              edge="start"
              onClick={handleModalToggle}
              sx={{ display: isMobileMenu ? "block" : "none" }}
            >
              <MenuIcon />
            </IconButton>
          </Box>
          <Box
            style={{
              display: "flex",
              justifyContent: "space-between",
              width: screenWidth < 425 ? "unset" : "100%",
              alignItems: "center",
            }}
          >
            <Image
              onClick={handleHomeClick}
              src="/Images/navbar/logo.svg"
              alt="Iltezam"
              width={114}
              height={64}
              style={{ cursor: "pointer" }}
            />
            <Box
              sx={{
                display: isMobileMenu ? "none" : "flex",
                gap: "37px",
              }}
            >
              {navItems.map((item) => (
                <Button
                  key={item}
                  onClick={() => {
                    setSelectedItem(item);
                    if (item === "Projects") {
                      router.push("/Project/Projects");
                      setModalOpen(false);
                    } else if (item === "Community") {
                      router.push("/Community");
                      setModalOpen(false);
                    } else if (item === "Learn") {
                      router.push("/learn");
                      setModalOpen(false);
                    } else if (item === "About us") {
                      router.push("/aboutus");
                      setModalOpen(false);
                    } else {
                      router.push("/"); // Default route for Projects
                      setModalOpen(false);
                    }
                  }}
                  sx={{
                    position: "relative",
                    fontFamily:
                      selectedItem === item ? "ScandiaBold" : "Scandia",
                    fontWeight: selectedItem === item ? 700 : 400,
                    fontSize: "16px",
                    textAlign: "center",
                    verticalAlign: "middle",
                    color: "#fff",
                    textTransform: "none",
                    "&::after":
                      selectedItem === item
                        ? {
                            content: '""',
                            position: "absolute",
                            bottom: "-30px",
                            width: "100%",
                            height: "6px",
                            backgroundColor: "#D31B1B",
                            borderRadius: "2px",
                          }
                        : {},
                  }}
                >
                  {item}
                </Button>
              ))}
            </Box>
            <Box style={{ display: "flex", gap: "24px", alignItems: "center" }}>
              {!isMobileMenu && (
                <Typography
                  sx={{
                    fontFamily: "ScandiaBold",
                    fontWeight: 700,
                    fontSize: "18px",
                    lineHeight: "150%",
                    textAlign: "center",
                    verticalAlign: "middle",
                    color: "#fff",
                    position: "relative",
                    cursor: "pointer",
                    "&::after": {
                      content: selectedItem === "Sign In" ? '""' : undefined,
                      position: "absolute",
                      bottom: "-36px",
                      width: "100%",
                      height: "6px",
                      backgroundColor: "#D31B1B",
                      borderRadius: "2px",
                      left: "1%",
                    },
                  }}
                  onClick={() => {
                    handleSignIn();
                    setSelectedItem("Sign In");
                  }}
                >
                  Sign In
                </Typography>
              )}
              {!isMobileMenu && (
                <Image
                  src="/Images/navbar/notify.svg"
                  alt="notify"
                  width={24}
                  height={24}
                  style={{ cursor: "pointer" }}
                  onClick={() => setNotificationOpen(true)}
                />
              )}
              {!isMobileMenu && (
                <Image
                  src="/Images/navbar/profile.svg"
                  alt="profile"
                  width={48}
                  height={48}
                />
              )}
              {!isMobileMenu && (
                <Button
                  sx={{
                    padding: "12px 24px",
                    border: "1px solid #FFFFFF",
                    backgroundColor: "transparent",
                    borderRadius: "60px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "6px",
                  }}
                >
                  <Typography
                    sx={{
                      fontFamily: "coheadlineRegular",
                      fontWeight: 400,
                      fontSize: "16px",
                      lineHeight: "24px",
                      textAlign: "center",
                      verticalAlign: "middle",
                      color: "#fff",
                    }}
                  >
                    العربية
                  </Typography>
                  <Image
                    src="/Images/navbar/globe.svg"
                    alt="globe"
                    width={18}
                    height={18}
                  />
                </Button>
              )}
            </Box>
          </Box>
        </Toolbar>
      </AppBar>
      <Notification
        open={notificationOpen}
        handleClose={() => setNotificationOpen(false)}
      />
      {dropdownModal}
      {/* <Box component="main">
        <Toolbar />
      </Box> */}
    </Box>
  );
}

export default DrawerAppBar;
