"use client";
import dynamic from "next/dynamic";

const SignInCopm = dynamic(
  () => import("@/pages/signin/signinMob/SignInCopm"),
  {
    ssr: false,
  }
);

const SignIn = dynamic(
  () => import("@/pages/signin/signinweb/SignIn"),
  {
    ssr: false,
  }
);
export default function Page() {
  return (
    <>
      <div className="mobile-only">
        <SignInCopm />
      </div>
      <div className="desktop-only">
        <SignIn />
      </div>
    </>
  );
}

