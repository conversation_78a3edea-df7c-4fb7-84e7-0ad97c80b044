"use client";
import React, { useState } from "react";
import { Card, CardContent,  TextField, Checkbox, FormControlLabel, Box,  Chip, LinearProgress, Avatar } from "@mui/material";
import Image from "next/image";
import "./ProjectDetail.css"
import { useRouter } from 'next/navigation';
const PDmakeDonation = () => {
    const [checked, setChecked] = useState(true);
    const router = useRouter();

    const handleApplyNowClick = () => {
      router.push("/Project/ConfirmPayment");
    };
    return (
        <Box mt={15} className="mcncghfjs" mb={4} bgcolor="#FCFCFC">
            <p
                className="font-[500] leading-[20px] tracking-[0%] text-[#2F3B31] mb-[24px] flex items-center gap-1  dhakghghsfs"
                style={{ fontFamily: "ScandiaMedium" }}
            >
                <span>Project</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
                <span>Homes for Hope</span>
                <Image src="/Img/arrow.svg" alt="arrow" width={20} height={20} />
                <span>Make a donation</span>
            </p>
            <div className="tynxrw3mauluilqyrrt">

                <div className="hsdjhhjhddjhfdf">
                    <Card sx={{ border: "none", boxShadow: "none", padding: "0px" }}>
                        <CardContent sx={{ padding: "0px" }}>
                            <Box display="flex" alignItems="center" gap={1} mb={2} mt={1}>
                                <Avatar src="/Img/city.png" sx={{ width: "48px", height: "48px" }} />
                                <div >
                                    <p
                                        className="font-[500] text-[16px] leading-[20px] tracking-[0%] text-[#000000] m-[0px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >Rebuild Syria Network</p>
                                    <p
                                        className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#303030] mt-[2px] m-[0px]"
                                        style={{ fontFamily: "Scandia" }}
                                    >Infrastructure & development</p>
                                </div>
                            </Box>
                            <Box borderRadius={4} mb={2} overflow="hidden">
                                <Image
                                    src="/Img/Syrian house.png"
                                    alt="Main Project"
                                    className="fsdhjsdfjdfhjjhsf"
                                    width={800}
                                    height={400}
                                />
                            </Box>
                            <p
                                className="font-[500] leading-[20px] tracking-[0%] text-[#181916] mb-[16px] sdfggsdfdhsgasdfhasdja"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Homes for Hope</p>
                            <p className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#181916] mb-[16px] m-[0px]" style={{ fontFamily: "Scandia" }}>
                                Reconstructing homes for displaced Syrian families, focusing on sustainable and durable housing.
                            </p>
                            <Box mt={2}>
                                <p
                                    className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}
                                >Details</p>
                                <div className="flex items-center gap-[30px]">
                                    <div className="flex items-center gap-[8px]">
                                        <Image src="/Img/loc.svg" alt="Location" width={24} height={24} />
                                        <div>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Location:</p>
                                            <p className="font-[400] leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Aleppo, Syria</p>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-[8px]">
                                        <Image src="/Img/calendar.svg" alt="Calendar" width={24} height={24} />
                                        <div>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#23232366] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>Project timeline:</p>
                                            <p className="font-[400]  leading-[20px] tracking-[0%] text-[#181916] m-[0px] whitespace-nowrap dhakghghsfs" style={{ fontFamily: "Scandia" }}>April–June 2025</p>
                                        </div>
                                    </div>
                                </div>

                            </Box>
                            <Box className="flex  flex-col items-start bg-[#ECE7E3] rounded-[12px] py-[10px] px-[16px] mt-[50px]">
                                <div className="flex items-center justify-between w-full">
                                    <p
                                        className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#181916]  m-[0px]" style={{ fontFamily: "Scandia" }}
                                    >Fundraising goal</p>
                                    <p
                                        className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#181916] m-[0px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                    >520000</p>
                                </div>
                                <LinearProgress
                                    variant="determinate"
                                    value={25}
                                    sx={{
                                        mt: 2,
                                        height: 12,
                                        borderRadius: 5,
                                        width: "100%",
                                        backgroundColor: '#D9D3CD',
                                        '& .MuiLinearProgress-bar': {
                                            backgroundColor: '#2F3B31',
                                        },
                                    }}
                                />
                                <p
                                    className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#303030]  m-[0px]" style={{ fontFamily: "Scandia" }}
                                >325,000 USD <span className="text-[#23232366]"> more to reach the goal</span></p>
                            </Box>
                        </CardContent>
                    </Card>
                </div>

                <div className="hsdjhhjhddjhfdf">
                    <Card sx={{ border: "none", boxShadow: "none", padding: "0px" }}>
                        <CardContent sx={{ padding: "0px" }}>
                            <p className="font-[500] leading-[30px] tracking-[0%] text-[#181916] sdfggsdfdhsgasdfhasdja"
                                style={{ fontFamily: "ScandiaMedium" }}>
                                How much donation you would like to give?
                            </p>
                            <p className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px]"
                                style={{ fontFamily: "ScandiaMedium" }}>Select amount</p>
                            <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
                                {["100", "200", "500", "1,000", "Other"].map((amount, i) => (
                                    <Chip
                                        key={i}
                                        label={`$${amount}`}
                                        variant={amount === "Other" ? "filled" : "outlined"}
                                        clickable
                                        className="font-[400] text-[14px] leading-[20px] tracking-[0%] text-[#3B0915] m-[0px]"
                                        style={{ fontFamily: "ScandiaMedium" }}
                                        sx={
                                            amount === "Other"
                                                ? {
                                                    backgroundColor: "#2F3B31",
                                                    color: "#FFFFFF",
                                                    '&:hover': {
                                                        backgroundColor: "#263128",
                                                    },
                                                }
                                                : {
                                                    borderColor: "#2F3B31",
                                                    color: "#2F3B31",
                                                    '&:hover': {
                                                        backgroundColor: "#f5f5f5",
                                                    },
                                                }
                                        }
                                    />
                                ))}
                            </Box>
                            <TextField
                                label="Other amount"
                                fullWidth
                                variant="standard"
                                className="font-[500] text-[14px] leading-[20px] tracking-[0%] text-[#000000] mb-[8px] m-[0px] "
                                sx={{
                                    fontFamily: "ScandiaMedium",
                                    mb: 2,
                                    '& .MuiInputLabel-root': {
                                        color: '#000000',
                                    },
                                    '& .MuiInputLabel-root.Mui-focused': {
                                        color: '#000000',
                                    },
                                }}
                            />

                            <FormControlLabel
                                control={
                                    <Checkbox
                                        checked={checked}
                                        onChange={(e) => setChecked(e.target.checked)}
                                        icon={
                                            <Image
                                                src="/Img/unchecked.svg"
                                                alt="unchecked"
                                                width={24}
                                                height={24}
                                            />
                                        }
                                        checkedIcon={
                                            <Image
                                                src="/Img/checked.svg"
                                                alt="checked"
                                                width={24}
                                                height={24}
                                            />
                                        }
                                        sx={{ color: "#d32f2f" }}
                                    />
                                }
                                label={<p className="font-[500] text-[12px] leading-[20px] tracking-[0%] text-[#000000]  m-[0px]"
                                    style={{ fontFamily: "ScandiaMedium" }}>Make this a monthly donation</p>}
                            />
                            <div className=" rounded-[12px] py-[12px] px-[16px] mt-[20px] 20px" style={{ background: "#f8f8f8" }}>
                                <p className="font-[400] text-[12px] leading-[24px] tracking-[0%] text-[#303030]  m-[0px]" style={{ fontFamily: "Scandia" }}>
                                    <span className="text-[#23232366]"> This donation will be processed </span> monthly <span className="text-[#23232366]">until </span>  June 2025<span className="text-[#23232366]"> or until you update your donation preferences at any time.</span>
                                </p>
                            </div>
                            <button   onClick={handleApplyNowClick}
                                className="font-[500] text-[14px] leading-[20px] tracking-[0%] w-[150px] text-[#ffffff] m-[0px] flex items-center justify-center text-center bg-[#D31B1B] rounded-[80px] border-0  h-[49px] mt-[40px] cursor-pointer"
                                style={{ fontFamily: "ScandiaMedium" }}
                            >Donate now</button>
                            <Box display="flex" alignItems="center" gap="8px" mt={5} >
                                <Image src="/Img/ipay.svg" alt="Apple Pay" width={40} height={24} style={{ opacity: "0.6" }} />
                                <Image src="/Img/gpay.svg" alt="Google Pay" width={40} height={24} style={{ opacity: "0.6" }} />
                                <Image src="/Img/spay.svg" alt="Samsung Pay" width={40} height={24} style={{ opacity: "0.6" }} />
                                <Image src="/Img/visa.svg" alt="Visa" width={40} height={24} style={{ opacity: "0.6" }} />
                                <Image src="/Img/mastercrd.svg" alt="Mastercard" width={40} height={24} style={{ opacity: "0.6" }} />
                            </Box>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </Box>
    )
}

export default PDmakeDonation