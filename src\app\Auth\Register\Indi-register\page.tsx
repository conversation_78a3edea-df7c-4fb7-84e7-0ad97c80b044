"use client";
import dynamic from "next/dynamic";

const OrgRegister = dynamic(
  () => import("@/pages/register/OrgRegister/OrgRegister"),
  {
    ssr: false,
  }
);

const SignInCopm = dynamic(
  () => import("@/pages/signin/signinMob/SignInCopm"),
  {
    ssr: false,
  }
);
function page() {
  return (
    <>
      <div className="mobile-only">
        <SignInCopm />
      </div>
      <div className="desktop-only">
        <OrgRegister />
      </div>
    </>
  );
}

export default page;
