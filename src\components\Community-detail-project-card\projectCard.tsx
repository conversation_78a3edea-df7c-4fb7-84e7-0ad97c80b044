import React from "react";
import Image from "next/image";

interface ProjectCardProps {
  imageUrl: string;
  orgImage: string;
  orgName: string;
  title: string;
  description: string;
  goalAmount?: string;
  progressPercent?: number;
  tags?: string[];
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  imageUrl,
  orgImage,
  orgName,
  title,
  description,
  goalAmount,
  progressPercent,
  tags = [],
}) => {
  return (
    <div
      className="rounded-[20px] flex-shrink-0 px-[20px] pt-[20px] pb-[24px] w-[310px] flex flex-col h-[490px] justify-between"
      style={{
        border: "1px solid #0000000A",
        boxShadow: "4px 12px 24px 0px #0000000A",
      }}
    >
      <div className="flex flex-col gap-[16px]">
        <div className="relative w-full h-[186px]">
          <Image
            src={imageUrl}
            alt="project"
            layout="fill"
            objectFit="cover"
            className="rounded-[12px]"
          />
        </div>
        <div className="flex flex-col gap-[8px]">
          <div className="flex gap-[8px] items-center">
            <Image
              src={orgImage}
              alt="org"
              width={24}
              height={24}
              className="rounded-full"
            />
            <p className="font-[scandiaMedium] text-[#000000] text-[12px] leading-[16px]">
              {orgName}
            </p>
          </div>
          <p className="font-[scandiaMedium] text-[#181916] text-[22px] leading-[32px]">
            {title}
          </p>
        </div>
        <p className="font-[scandia] text-[#303030] text-[14px] leading-[20px]">
          {description}
        </p>
      </div>

      {goalAmount && progressPercent !== undefined ? (
        <div className="flex flex-col gap-[4px] px-[16px] pt-[10px] pb-[16px] bg-[#ECE7E3] rounded-[12px]">
          <div className="flex justify-between">
            <p className="text-[12px] leading-[24px] font-[scandia] text-[#181916]">
              Fundraising goal
            </p>
            <p className="text-[14px] leading-[20px] font-[scandiaMedium] text-[#181916]">
              {goalAmount}
            </p>
          </div>
          <div className="w-full h-[7px] bg-[#D9D3CD] rounded-full overflow-hidden">
            <div
              className="h-full bg-[#2F3B31] rounded-full"
              style={{ width: `${progressPercent}%` }}
            ></div>
          </div>
        </div>
      ) : (
        <div className="flex gap-[4px] flex-wrap">
          {tags.map((tag, index) => (
            <div
              key={index}
              className="bg-[#F8F8F8] rounded-[40px] py-[8px] px-[12px] flex items-center"
            >
              <p className="font-[scandia] text-[#181916] text-[12px] leading-[16px] whitespace-nowrap">
                {tag}
              </p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProjectCard;
