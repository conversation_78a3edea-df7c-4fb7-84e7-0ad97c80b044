"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";

function Footer() {
  const router = useRouter();

  const handleOurPartnerClick = () => {
    router.push("/ourpartner");
  };

  return (
    <div className="bg-[#181916] max-[550px]:px-[24px] max-[1160px]:px-[50px] px-[100px] pt-[60px] pb-[40px] flex flex-col gap-[64px]">
      <div>
        <Image
          src="/Images/footer/Logo.png"
          alt="logo"
          width={77.14}
          height={96.6}
        />
      </div>
      <div className="flex justify-between w-full max-[850px]:flex-col gap-[30px]">
        <div className="flex flex-col gap-[64px]">
          <div className="flex flex-col gap-[20px]">
            <p className="font-[scandiaMedium] font-medium text-[22px] leading-[32px] text-[#FFFFFF] max-[768px]:text-[18px] max-[500px]:text-[16px]">
              Stay connected and updated
            </p>
            <div className="flex gap-[12px]">
              <input
                className="bg-[transparent] max-[1000px]:w-[100%] max-[600px]:py-[6.5px] w-[290px] focus:outline-none focus:ring-0 rounded-[99px] py-[10.5] px-[24px] placeholder-white custom-footer-placeholder"
                style={{ border: "1px solid #FFFFFF" }}
                placeholder="Enter email"
              />
              <button
                style={{
                  fontFamily: "ScandiaMedium",
                  background: "#ECE7E3",
                }}
                className="font-[scandiaMedium] cursor-pointer max-[600px]:py-[12px] max-[600px]:px-[12px] text-[#181916] text-[14px] leading-[20px] rounded-[70px] border-none p-[16px]"
              >
                Subscribe
              </button>
            </div>
          </div>
          <div className="flex gap-[16px]">
            <Image
              className="cursor-pointer"
              src="/Images/footer/elements.svg"
              alt="Instagram"
              width={24}
              height={24}
            />
            <Image
              className="cursor-pointer"
              src="/Images/footer/tiktok.svg"
              alt="Tiktok"
              width={24}
              height={24}
            />
            <Image
              className="cursor-pointer"
              src="/Images/footer/new-twitter.svg"
              alt="Twitter"
              width={24}
              height={24}
            />
            <Image
              className="cursor-pointer"
              src="/Images/footer/youtube.svg"
              alt="Youtube"
              width={24}
              height={24}
            />
          </div>
        </div>
        <div className="grid grid-cols-3 max-[550px]:grid-cols-2 max-[1100px]:gap-[30px] gap-[64px]">
          <div className="flex flex-col gap-[13px] cursor-pointer">
            <h3 className="font-[scandiaMedium] font-medium text-[16px] leading-[24px] text-[#FFFFFF]">
              Project
            </h3>
            <div className="flex flex-col gap-[8px]">
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                All Project
              </p>
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Latest Volunteer
              </p>
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Latest Donation
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-[13px] cursor-pointer">
            <h3 className="font-[scandiaMedium] font-medium text-[14px] sm:text-[16px] leading-[24px] text-[#FFFFFF]">
              Community
            </h3>
            <div className="flex flex-col gap-[8px]">
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Latest Joiner
              </p>
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Latest Port
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-[13px] cursor-pointer">
            <h3 className="font-[scandiaMedium] font-medium text-[14px] sm:text-[16px] leading-[24px] text-[#FFFFFF]">
              Learn
            </h3>
            <div className="flex flex-col gap-[8px]">
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                All Courses
              </p>
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Hot Courses
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-[13px] cursor-pointer">
            <h3 className="font-[scandiaMedium] font-medium text-[14px] sm:text-[16px] leading-[24px] text-[#FFFFFF]">
              Partner
            </h3>
            <div className="flex flex-col gap-[8px]">
              <p
                onClick={handleOurPartnerClick}
                className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4"
              >
                Benefit
              </p>
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Become a Partner
              </p>
            </div>
          </div>
          <div className="flex flex-col gap-[13px] cursor-pointer">
            <h3 className="font-[scandiaMedium] font-medium text-[14px] sm:text-[16px] leading-[24px] text-[#FFFFFF]">
              Get in touch
            </h3>
            <div className="flex flex-col gap-[8px]">
              <p className="font-[scandia] text-[12px] leading-[16px] text-[#FFFFFF] hover:underline hover:underline-offset-4">
                Contact
              </p>
            </div>
          </div>
        </div>
      </div>
      <div>
        <div
          style={{
            height: "1px",
            background: "#ECE7E3",
            width: "100%",
            marginTop: "10px",
            marginBottom: "10px",
          }}
        ></div>
        <div className="pt-[24px] flex flex-row justify-between max-[660px]:flex-col gap-[16px]">
          <p className="font-[scandia] text-[#FFFFFF] text-[12px] leading-[16px]">
            Copyright © 2025. All rights reserved.
          </p>
          <div className="flex gap-[40px] max-[900px]:gap-[20px] max-[371px]:gap-[10px]">
            <p className="font-[scandia] text-[#FFFFFF] text-[12px] max-[357px]:text-[10px] leading-[16px] cursor-pointer">
              Privacy Policy
            </p>
            <p className="font-[scandia] text-[#FFFFFF] text-[12px] max-[357px]:text-[10px] leading-[16px] cursor-pointer">
              Cookie policy
            </p>
            <p className="font-[scandia] text-[#FFFFFF] text-[12px] max-[357px]:text-[10px] leading-[16px] cursor-pointer">
              Terms and conditions
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Footer;
