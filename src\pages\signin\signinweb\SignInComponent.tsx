'use client';

import { login } from "@/services/redux/middleware/register";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { AppDispatch } from "../../../services/redux/store";
export interface loginPayload {
  email: string;
  password: string;

}
function SignInComponent() {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [password, setPassword] = useState("");
  const [errors, setErrors] = useState<{ email?: string; password?: string }>(
    {}
  );
  const handdleforget = () => {
    router.push("/Auth/SignIn/forget-password");
  };

  const handleregister = () => {
    router.push("/Auth/Register/register");
  };
  // Either use the validate function in handleSubmit or remove it
  // Since handleSubmit has its own validation logic, we can remove this unused function
  // const validate = () => {
  //   const newErrors: { email?: string; password?: string } = {};
  //   const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  //
  //   if (!email) {
  //     newErrors.email = "Email is required";
  //   } else if (!emailRegex.test(email)) {
  //     newErrors.email = "Enter a valid email";
  //   }
  //
  //   if (!password) {
  //     newErrors.password = "Password is required";
  //   }
  //
  //   setErrors(newErrors);
  //   return Object.keys(newErrors).length === 0;
  // };

  const handleSubmit = async (e?: React.FormEvent | React.MouseEvent) => {
    if (e) e.preventDefault();


    const newErrors: { email?: string; password?: string } = {};
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!email) {
      newErrors.email = "Email is required";
    } else if (!emailRegex.test(email)) {
      newErrors.email = "Enter a valid email";
    }

    if (!password) {
      newErrors.password = "Password is required";
    }

    setErrors(newErrors);


    if (Object.keys(newErrors).length === 0) {

      console.log("Signed in with:", { email, password });
      const data = {
        email: email,
        password: password,

      };
      try {
        setLoading(true);
        const result = await dispatch(login(data));
        console.log(result);
        console.log(result?.payload?.statusCode);
        console.log(result?.payload?.message?.status);
        
        if (result?.payload?.statusCode === 200) {
          toast.success("Login successfully");
          localStorage.setItem("token", result?.payload?.data?.token);
          localStorage.setItem("user", JSON.stringify(result?.payload?.data?.user));
          
        } else if (result?.payload?.message?.status === 401) {
          toast.error("Incorrect Password")
        }
      } finally {
        setLoading(false);
      }



    }
  };

  return (
    <div className="flex flex-col w-[35%] lg:w-[40%] xl:w-[410px] gap-[40px]">
      <div className="flex flex-col gap-[12px]">
        <p className="text-[black] font-[scandiaMedium] leading-[40px] text-[32px]">Sign In</p>
        <p
          style={{ color: "rgba(48, 48, 48, 1)" }}
          className="text-[14px] leading-[20px] font-[scandia]"
        >
          Don’t have account?
          <span
            className=" font-[scandiaMedium] cursor-pointer"
            style={{ color: "rgba(211, 27, 27, 1)" }}
            onClick={handleregister}
          >
            {" "}
            Register
          </span>
        </p>
      </div>
      <form className="space-y-[40px]" onSubmit={handleSubmit}>
        <div className="flex flex-col gap-[32px]">
          <div className="space-y-[8px] flex flex-col">
            <label
              htmlFor="email"
              className="text-[14px] leading-[20px] font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Email
            </label>

            <input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter email"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                fontFamily: "Scandia",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                letterSpacing: "0%",
                verticalAlign: "middle",
              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent]  placeholder:text-[ #23232366] text-[ rgba(35, 35, 35, 0.4)]  custom-footer-placeholde"
            />
            {errors.email && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.email}
              </span>
            )}
          </div>

          <div className="flex flex-col gap-[8px]">
            <label
              htmlFor="password"
              className="text-[14px] leading-[20px]  font-[scandiaMedium]"
              style={{ color: "rgba(48, 48, 48, 1)" }}
            >
              Password
            </label>
            <input
              id="password"
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              style={{
                borderTop: "none",
                borderLeft: "none",
                borderRight: "none",
                borderBottom: "1.5px solid rgba(222, 222, 222, 1)",
                fontFamily: "Scandia",
                fontWeight: 400,
                fontSize: "14px",
                lineHeight: "20px",
                letterSpacing: "0%",
                verticalAlign: "middle",

              }}
              className="bg-[transparent] leading-[20px] placeholder:leading-[20px] w-full lg:w-[410px] py-[10.5] focus:outline-none focus:ring-2 focus:ring-[transparent]  placeholder:text-[ #23232366] text-[ rgba(35, 35, 35, 0.4)]  custom-footer-placeholde"
            />
            {errors.password && (
              <span
                style={{ color: "#D31B1B" }}
                className="text-sm text-left leading-[20px] font-[scandiaMedium] text-center"
              >
                {errors.password}
              </span>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-[24px] justify-center text-center">
          <div
            style={{
              display: "flex", alignItems: "center", justifyContent: "center",
            }}
          >
            <button
              style={{
                height: "52px", width: "105px", borderRadius: "80px", backgroundColor: "#D31B1B",
                display: "flex", alignItems: "center", justifyContent: "center", cursor: "pointer", border: "none",
              }}
            >
              {loading ? (
                <div className="loader"></div>
              ) : (
                <p
                  className="text-[14px] text-[white] font-[scandiaMedium]"
                  onClick={handleSubmit}
                >
                  Sign in
                </p>
              )}
            </button>
          </div>
          <p
            className="text-[14px] text-[black] leading-[20px] font-[scandiaMedium] hover:text-[#ec1c24] cursor-pointer"
            onClick={handdleforget}
          >
            Forgot password
          </p>
        </div>
      </form>
    </div>
  );
}

export default SignInComponent;
