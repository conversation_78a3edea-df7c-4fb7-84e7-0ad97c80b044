interface GroupCardProps {
  imageSrc: string;
  title: string;
  members: string;
  buttonText: "Join" | "View group chat";
}

export default function GroupCard({
  imageSrc,
  title,
  members,
  buttonText,
}: GroupCardProps) {
  const isJoin = buttonText === "Join";

  return (
    <div className="flex justify-between items-center py-[8px]">
      <div className="flex gap-[12px] items-center">
        <img
          style={{ border: "0.71px solid #ECE7E3" }}
          src={imageSrc}
          alt="group"
          className="w-[48px] h-[48px] rounded-full"
        />
        <div className="flex flex-col gap-[2px]">
          <p className="font-[scandiaMedium] text-[#000000] text-[16px] max-[375px]:text-[12px] max-[377px]:text-[14px] leading-[24px]">
            {title}
          </p>
          <p className="font-[scandiaMedium] text-[#000000] text-[14px] max-[377px]:text-[12px] leading-[20px]">
            {members} Members
          </p>
        </div>
      </div>

      <button
        className={`h-[32px] flex cursor-pointer items-center py-[8px] px-[12px] text-[12px] max-[375px]:text-[10px] leading-[16px] rounded-[80px] font-[scandiaMedium] ${
          isJoin
            ? "bg-[#D31B1B] text-white border-none"
            : "bg-white text-[#303030] border"
        }`}
        style={!isJoin ? { border: "1px solid #303030" } : {}}
      >
        {buttonText}
      </button>
    </div>
  );
};
