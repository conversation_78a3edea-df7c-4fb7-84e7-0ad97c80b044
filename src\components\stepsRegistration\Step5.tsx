"use client";
import Image from "next/image";

export default function Step5() {
  return (
    <div className="flex flex-col gap-[40px] items-center pt-[50px] pb-[60px] max-[550px]:pt-[40px] max-[900px]:pb-[10px]">
      <Image
        src="/Images/registration/Validation.svg"
        alt="Validate"
        width={120}
        height={120}
        className="w-auto h-auto"
      />
      <div className="flex flex-col gap-[12px] items-center max-w-[458px]">
        <p className="font-[scandiaMedium] font-medium text-[32px] leading-[40px] text-[#181916] max-[550px]:text-[22px] max-[550px]:leading-[32px]">
          Submission received!
        </p>
        <p className="font-[scandia] font-medium text-[14px] leading-[20px] max-[380px]:text-[12px] text-[#303030] max-[380px]:leading-[18px] text-center">
          Thank you for registering your organization with Iltezam.
        </p>
        <p className="font-[scandia] font-medium text-[14px] leading-[20px] text-[#303030] max-[380px]:text-[12px] max-[380px]:leading-[18px] text-center">
          Our team is now reviewing your information to ensure legitimacy and
          alignment with our mission to rebuild and empower Syrian communities.
        </p>
      </div>
      <button className="cursor-pointer bg-[#D31B1B] w-fit rounded-[80px] py-[16px] px-[24px] border-none text-[#FFFFFF] font-[scandiaMedium] text-[14px] leading-[20px]">
        Back to home
      </button>
      <p className="font-[scandia] font-medium text-[14px] leading-[20px] max-[380px]:text-[12px] max-[380px]:leading-[18px] text-[#303030] text-center">
        Contact us at{" "}
        <span className="font-[scandiaMedium]"><EMAIL></span> if
        you have questions
      </p>
    </div>
  );
}
