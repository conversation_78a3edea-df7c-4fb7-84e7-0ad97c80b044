.dhakghghsfs {
    font-size: 14px;
}

.dscbuwbanue {
    padding: 48px;
}

.hdskguqzqa {
    font-size: 40px;
}

.sdfggsdfdhsgasdfhasdja {
    font-size: 32px;
}

.okj<PERSON>a {
    font-size: 22px;
}

.dghdfgaghghsdadgh {
    font-size: 28px;
}

.trweyuassdj {
    height: 38px;
    width: 38px;
}

.sdghfsdhdgsjhsjs {
    display: flex;
    justify-content: space-between;
    padding: 64px 100px;
    align-items: center;
    background-image: url("/Img/Become\ Partner.png");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;

}

.msuhkqwaida {
    display: flex;
    align-items: center;
    gap: 20px;
}

.retwytqeuwqryer {
    display: flex;
    justify-content: space-between;
    gap: 40px;
    width: 100%;
    align-items: center;
}

.tynxrw3mauluilqyrrt {
    display: flex;
    gap: 32px;
    padding: 32px;
    background-color: #ffffff;
    border-radius: 20px;
    box-shadow: 4px 12px 24px 0px #0000000A;

}

.sgfyuvuimok {
    width: 80px;
    height: 80px;
}

.hsdjhhjhddjhfdf {
    padding: 32px;
    border-radius: 20px;
    border: 1px solid #0000000A;
    box-shadow: 4px 12px 24px 0px #0000000A;
}

.fsdhjsdfjdfhjjhsf {
    width: 416px;
    height: 345px;
}

.mcncghfjs {
    padding: 32px;
}

@media(max-width:1100px) {
    .tynxrw3mauluilqyrrt {
        flex-direction: column;
        padding: 12px;
    }


    .mcncghfjs {
        padding: 15px;
    }

    .hsdjhhjhddjhfdf {
        padding: 15px;
    }
}

@media(max-width:1024px) {
    .retwytqeuwqryer {
        flex-direction: column;
        gap: 0;

    }

    .hdskguqzqa {
        font-size: 33px;
    }

    .sdghfsdhdgsjhsjs {
        padding: 40px 40px;
    }

}

@media (max-width: 768px) {
    .fsdhjsdfjdfhjjhsf {
        width: 416px;
        height: 211px;
    }
    .trweyuassdj {
        height: 30px;
        width: 30px;
    }

    .ghdfgdfsghdhj {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .hdskguqzqa {
        font-size: 30px;
    }

    .dhakghghsfs {
        font-size: 11px;
    }

    .fhdweryjfuyw {
        font-size: 11px !important;
    }

    .dghdfgaghghsdadgh {
        font-size: 20px;
    }

    .okjyyaa {
        font-size: 18px;
    }

    .sdfggsdfdhsgasdfhasdja {
        font-size: 25px;
    }

    .dscbuwbanue {
        padding: 20px;
    }
}

@media(max-width:525px) {
    .msuhkqwaida {
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 425px) {
    .trweyuassdj {
        height: 25px;
        width: 25px;
    }

    .hdskguqzqa {
        font-size: 28px;
    }

    .dghdfgaghghsdadgh {
        font-size: 18px;
    }

    .dhakghghsfs {
        font-size: 9px;
    }

    .okjyyaa {
        font-size: 16px;
    }

    .sdfggsdfdhsgasdfhasdja {
        font-size: 22px;
    }
}