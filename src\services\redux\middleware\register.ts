/* eslint-disable */
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../services/apiInterceptor";
import { API_URL } from "../../client";
import {
  loginPayload,
  OrganizationRegisterPayload,
  RegisterPayload,
  resendPayload,
  verifyPayload,
} from "../type";

export const register = createAsyncThunk(
  "auth/register",
  async (data: RegisterPayload, { rejectWithValue }) => {
    try {
      const res = await api.post(`${API_URL}/api/auth/register`, data);
      return res.data;
    } catch (error) {
      return rejectWithValue({
        message: error,
      });
    }
  }
);

export const login = createAsyncThunk(
  "auth/login",
  async (data: loginPayload, { rejectWithValue }) => {
    try {
      const res = await api.post(`${API_URL}/api/auth/login`, data);
      return res.data;
    } catch (error) {
      return rejectWithValue({
        message: error,
      });
    }
  }
);

export const verifyEmail = createAsyncThunk(
  "auth/verifyEmail",
  async (data: verifyPayload, { rejectWithValue }) => {
    try {
      const res = await api.post(`${API_URL}/api/auth/verify-email`, data);
      return res.data;
    } catch (error) {
      return rejectWithValue({
        message: error,
      });
    }
  }
);

export const resendVerification = createAsyncThunk(
  "auth/resendVerification",
  async (data: resendPayload, { rejectWithValue }) => {
    try {
      const res = await api.post(
        `${API_URL}/api/auth/resend-verification`,
        data
      );
      return res.data;
    } catch (error) {
      return rejectWithValue({
        message: error,
      });
    }
  }
);

export const organizerRegister = createAsyncThunk(
  "auth/organizerRegister",
  async (data: OrganizationRegisterPayload, { rejectWithValue }) => {
    try {
      const token = localStorage.getItem("token"); 
      

      const res = await api.post(`${API_URL}/api/organiser/create`, data, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      return res.data;
    } catch (error: any) {
      return rejectWithValue({
        message: error?.response?.data?.message || error.message,
      });
    }
  }
);


export const uploadImage = createAsyncThunk(
  "uploadImage",
  async (formData: FormData) => {
    try {
      console.log("Inside uploadImage BEFORE API", formData);

      const res = await api.post(`${API_URL}/api/uploadd/photo`, formData, {
        headers: {
          // 'Content-Type': 'multipart/form-data' ← don't set this manually; Axios handles it
        },
      });

      console.log("Inside uploadImage AFTER API", res.data);
      return res.data;
    } catch (error) {
      console.error("Error in uploadImage:", error);
      return { message: error };
    }
  }
);
